import './bootstrap';
import EmblaCarousel from "embla-carousel";

import Alpine from 'alpinejs';

window.Alpine = Alpine;
document.addEventListener("DOMContentLoaded", () => {
    const emblaNode = document.querySelector(".embla");
    const viewport = document.querySelector(".embla__viewport");

    const prevButton = document.querySelector(".embla__button--prev");
    const nextButton = document.querySelector(".embla__button--next");
  
    if (emblaNode) {
        const embla = EmblaCarousel(viewport, { loop: true });
  
      // Enable/disable buttons based on carousel state
      const setButtonState = () => {
        if (embla.canScrollPrev()) {
          prevButton.removeAttribute("disabled");
        } else {
          prevButton.setAttribute("disabled", "true");
        }
  
        if (embla.canScrollNext()) {
          nextButton.removeAttribute("disabled");
        } else {
          nextButton.setAttribute("disabled", "true");
        }
      };
  
      // Button click handlers
      prevButton.addEventListener("click", () => embla.scrollPrev());
      nextButton.addEventListener("click", () => embla.scrollNext());
  
      // Update button state on carousel events
      embla.on("select", setButtonState);
      embla.on("init", setButtonState);
      document.addEventListener("keydown", (event) => {
        if (event.key === "ArrowLeft") {
          embla.scrollPrev();
        }
        if (event.key === "ArrowRight") {
          embla.scrollNext();
        }
      });
      // Initialize the button state
      setButtonState();
    }
  });
  
Alpine.start();
