<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Vendors') }}
        </h2>
    </x-slot>

    <h1 class="">Vendors</h1>
    <a href="{{ route('vendors.create') }}" class="btn btn-primary ">Add New Vendor</a>
    
    <table class="table text-xs">
        <thead>
            <tr>
                <th>Name</th>
                <th>email</th>
                <th>phone</th>
                <th>address</th>
                <th>user</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($vendors as $vendor)
                <tr>
                    <td class="">{{ $vendor->name }}</td>
                    <td class="">{{ $vendor->email }}</td>
                    <td class="">{{ $vendor->phone }}</td>
                    <td class="">{{ $vendor->address }}</td>
                    <td>{{ $vendor->user->name ?? 'no user' }}</td>
                    <td>
                        <a href="{{ route('vendors.edit', $vendor) }}" class="btn btn-warning">Edit</a>
                        <form action="{{ route('vendors.destroy', $vendor) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">Delete</button>
                        </form>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $vendors->links() }}
</x-app-layout>
