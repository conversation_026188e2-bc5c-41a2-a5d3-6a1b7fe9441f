<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Create Auction') }}
        </h2>
    </x-slot>
    <h1>Create Auction</h1>

    <form action="{{ route('auctions.store') }}" method="POST">
        @csrf
        <label for="product_id">Product:</label>
        <select name="product_id" required>
            @foreach($products as $product)
            <option value="{{ $product->id }}">{{ $product->name }}</option>
            @endforeach
        </select>

        <label for="start_time">Start Time:</label>
        <input type="datetime-local" name="start_time" required>

        <label for="end_time">End Time:</label>
        <input type="datetime-local" name="end_time" required>

        <label for="starting_price">Starting Price:</label>
        <input type="number" name="starting_price" step="0.01" required>

        <label for="reserve_price">Reserve Price:</label>
        <input type="number" name="reserve_price" step="0.01">

        <button type="submit">Create Auction</button>
    </form>

</x-app-layout>