<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __(' Auction') }}
        </h2>
    </x-slot>
    <div class="container">
        <h1>Auction for {{ $auction->product->name }}</h1>

        <p>Starting Price: ${{ $auction->starting_price }}</p>
        <p>Current Price: ${{ $auction->current_price }}</p>
        <p>Auction Status: {{ $auction->status }}</p>

        <h2>Place a Bid</h2>
        <form action="{{ route('bids.store', $auction) }}" method="POST">
            @csrf
            <label for="bid_amount">Bid Amount:</label>
            <input type="number" name="bid_amount" step="0.01"
                min="{{ max($auction->current_price, $auction->starting_price) }}" required>
            <button type="submit">Place Bid</button>
        </form>

        <h3>All Bids</h3>
        <ul>
            @foreach($auction->bids as $bid)
            <li>{{ $bid->user->name }} bid ${{ $bid->bid_amount }} at {{ $bid->created_at->toDayDateTimeString() }}</li>
            @endforeach
        </ul>
    </div>
</x-app-layout>