<x-app-layout>
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-4">All Auctions</h1>

        @if($auctions->isEmpty())
        <p>No auctions available.</p>
        @else
        <table class="min-w-full bg-white border">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b">Product</th>
                    <th class="py-2 px-4 border-b">Starting Price</th>
                    <th class="py-2 px-4 border-b">Current Price</th>
                    <th class="py-2 px-4 border-b">End Time</th>
                    <th class="py-2 px-4 border-b">Status</th>
                    <th class="py-2 px-4 border-b">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($auctions as $auction)
                <tr class="text-center">
                    <td class="py-2 px-4 border-b">{{ $auction->product->name }}</td>
                    <td class="py-2 px-4 border-b">${{ number_format($auction->starting_price, 2) }}</td>
                    <td class="py-2 px-4 border-b">${{ number_format($auction->current_price, 2) }}</td>
                    <td class="py-2 px-4 border-b">{{ $auction->end_time->format('Y-m-d H:i') }}</td>
                    <td class="py-2 px-4 border-b">{{ ucfirst($auction->status) }}</td>
                    <td class="py-2 px-4 border-b">
                        <a href="{{ route('auctions.show', $auction->id) }}" class="text-blue-500">View</a>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @endif
    </div>
</x-app-layout>