<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Edit Products') }}
        </h2>
    </x-slot>
    <h1>Edit Product</h1>

    <form action="{{ route('products.update', $product) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        <div class="form-group">
            <label for="name">Product Name</label>
            <input type="text" name="name" id="name" class="form-control" value="{{ $product->name }}" required>
        </div>


        <div class="form-group">
            <label for="category_id">Category</label>
            <select name="category_id" id="category_id" class="form-control">
                <option value="">None</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ $category->id == $product->category_id ? 'selected' : '' }}>{{
                    $category->name }}</option>
                @endforeach
            </select>
        </div>
        <div class="form-group">
            <label for="description">Product description</label>
            <input type="text" name="description" id="description" class="form-control"
                value="{{ $product->description }}">
        </div>
        <div>
            <label for="images">Featured Image:</label>
            <img src="{{ $product->image_path }}" alt="{{ $product->name }}" class="w-32 h-32 object-cover">
        </div>

        <div>
            <label for="images">Product Image:</label>
            <input type="file" name="images[]" multiple>
        </div>
        <div class="grid grid-cols-6 gap-20">
            @foreach($product->images as $image)
            <img src="{{ Storage::url($image->image_path) }}" alt="Product Image" class="w-full h-auto">
            @endforeach
        </div>

        <button type="submit" class="btn btn-primary">Update Product</button>
    </form>
</x-app-layout>