<x-app-layout>

    <div class="flex justify-between items-center pb-4">
        <h1 class="text-xl font-bold">Products</h1>
        <a href="{{ route('products.create') }}" class="p-2 rounded bg-white shadow ">Add New Product</a>
    </div>
    <table class="min-w-full bg-white border ">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b">Name</th>
                <th class="py-2 px-4 border-b">Category</th>
                <th class="py-2 px-4 border-b">Vendor</th>
                <th class="py-2 px-4 border-b">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($products as $product)
            <tr class="text-center">
                <td class="py-2 px-4 border-b">{{ $product->name }}</td>
                <td class="py-2 px-4 border-b">{{ $product->category->name ?? 'Uncategorized' }}</td>
                <td class="py-2 px-4 border-b">{{ $product->vendor->name }}</td>
                <td class="py-2 px-4 border-b">
                    <a href="{{ route('products.edit', $product) }}" class="btn btn-warning">Edit</a>
                    <form action="{{ route('products.destroy', $product) }}" method="POST" style="display:inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    {{-- {{ $products->links() }} --}}
</x-app-layout>