<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Create Products') }}
        </h2>
    </x-slot>
    <h1>Add New Product</h1>
    <form action="{{ route('products.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="form-group">
            <label for="name">Product Name</label>
            <input type="text" name="name" id="name" class="form-control" required>
        </div>


        <div class="form-group">
            <label for="category_id">Category</label>
            <select name="category_id" id="category_id" class="form-control">
                <option value="">None</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}">{{ $category->name }}</option>
                @endforeach
            </select>
            <div class="form-group">
                <label for="description">Product description</label>
                <input type="text" name="description" id="description" class="form-control">
            </div>
            <div>
                <label for="image">Featured Image:</label>
                <input type="file" name="image" id="image">
            </div>
            <div>
                <label for="images">Product Images:</label>
                <input type="file" name="images[]" multiple>
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Add Product</button>
    </form>
</x-app-layout>