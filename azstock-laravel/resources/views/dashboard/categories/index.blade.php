<x-app-layout>

    <div class="flex justify-between items-center pb-4">
        <h1 class="text-xl font-bold">Categories</h1>
        <a href="{{ route('categories.create') }}" class="btn btn-primary ">Add New Category</a>
    </div>
    <table class="min-w-full bg-white ">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b">Name</th>
                <th class="py-2 px-4 border-b">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($categories as $category)
            <tr class="text-center">
                <td class="py-2 px-4 border-b">{{ $category->name }}</td>
                <td class="py-2 px-4 border-b">
                    <a href="{{ route('categories.edit', $category) }}" class="btn btn-warning">Edit</a>
                    <form action="{{ route('categories.destroy', $category) }}" method="POST" style="display:inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    {{ $categories->links() }}
</x-app-layout>