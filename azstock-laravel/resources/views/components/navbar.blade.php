<header class="flex justify-between items-center gap-2 py-3">
    <div class="flex  ">
        <x-application-logo class="w-28 h-auto " />
    </div>
    @if (Route::has('login'))
    <nav class="-mx-3 flex items-center justify-end font-semibold gap-5">
        <a href="{{url('/all-auctions')}}">Shop All Auctions</a>
        <div class="dropdown dropdown-hover">
            <div tabindex="0" role="button" class=" m-1">For Buyers</div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                <li><a href="{{ url('/how-it-works') }}">How it works</a></li>
                <li><a href="{{url('/buyer-resource-center')}}">Buyer resource center</a></li>
            </ul>
        </div>

        <div class="dropdown dropdown-hover">
            <div tabindex="0" role="button" class=" m-1">For Sellers</div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                <li><a href="{{url('/sellers')}}">Want ot sell with us?</a></li>
                <li><a href="{{url('/sellers/enterprise')}}">Enterprise seller</a></li>
            </ul>
        </div>
        @auth
        <a href="{{ url('/dashboard') }}"
            class="rounded-md pl-3 py-2 text-black ring-1 ring-transparent transition hover:text-black/70 focus:outline-none focus-visible:ring-[#FF2D20] dark:text-white dark:hover:text-white/80 dark:focus-visible:ring-white">
            Dashboard
        </a>
        @else
        <a href="{{ route('login') }}"
            class="rounded-md pl-3 py-2 text-black ring-1 ring-transparent transition hover:text-black/70 focus:outline-none focus-visible:ring-[#FF2D20] dark:text-white dark:hover:text-white/80 dark:focus-visible:ring-white">
            Log in
        </a>

        @if (Route::has('register'))
        <a href="{{ route('register') }}"
            class="rounded-md  py-2 text-black ring-1 ring-transparent transition hover:text-black/70 focus:outline-none focus-visible:ring-[#FF2D20] dark:text-white dark:hover:text-white/80 dark:focus-visible:ring-white">
            Register
        </a>
        @endif
        @endauth

    </nav>
    @endif
</header>