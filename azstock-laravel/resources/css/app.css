@tailwind base;
@tailwind components;
@tailwind utilities;


.embla {
    overflow: hidden;
  }
  
  .embla__viewport {
    width: 100%;
  }
  
  .embla__container {
    display: flex;
    align-items: center;
    gap: 10px;

  }
  
  .embla__slide {
    flex: 0 0 auto;
    width: 30%; /* Adjust as needed */
    position: relative;
  }
  .embla__button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .embla__button--prev {
    left: 10px;
  }
  
  .embla__button--next {
    right: 10px;
  }
  
  .embla__button:hover {
    background: rgba(0, 0, 0, 0.8);
  }