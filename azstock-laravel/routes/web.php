<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AuctionController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\AdminDashboardController;
use App\Models\Auction;
use App\Models\Category;
use App\Http\Controllers\BidController;
use App\Models\Vendor;

Route::get('/', function () {
    $auctions = Auction::with(['auctionItems', 'vendor'])->latest()->take(5)->get(); // Fetch the latest 5 auctions
    $vendors = Vendor::latest()->take(5)->get(); // Fetch the latest 5 vendors

    return view('welcome',compact('auctions', 'vendors'));
});
Route::get('/how-it-works', function () {

    return view('website.buyer.how-it-works');
});
Route::get('/buyer-resource-center', function () {

    return view('website.buyer.buyer-resource-center');
});
Route::get('/sellers', function () {

    return view('website.sellers.index');
});
Route::get('/sellers/enterprise', function () {

    return view('website.sellers.enterprise');
});
// Route::get('/auctions', function () {
//     $products = Product::latest()->take(5)->get(); // Fetch the latest 5 products
//     return view('auctions.index',compact('products'));
// });
Route::get('/all-auctions/{auction}', [AuctionController::class, 'myAuction'])->name('auctions.myAuction');

// Route::resource('auctions', ProductController::class)->names([
//     'index' => 'auctions',
//     'show' => 'auctions.show',
// ]);

Route::get('all-auctions', [AuctionController::class, 'index'])->name('auctions.index');


Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');


Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::resource('dashboard/categories', CategoryController::class)->names([
        'index' => 'categories',
        'create' => 'categories.create',
    ]);
    // Product routes removed - replaced by auction items
    Route::get('dashboard/auctions', [AuctionController::class, 'myAuctions'])->name('auctions.myAuctions');

    // Standard CRUD routes for the dashboard auctions
    Route::resource('dashboard/auctions', AuctionController::class)->except(['index','myAuction']);
    Route::resource('dashboard/vendors', VendorController::class);

// Auction routes


// Bid route
Route::post('dashboard/auctions/{auction}/bid', [BidController::class, 'store'])->name('bids.store');


Route::get('/images', [ImageController::class, 'index'])->name('images.index');
    Route::get('/images/create', [ImageController::class, 'create'])->name('images.create');
    Route::post('/images', [ImageController::class, 'store'])->name('images.store');

});

Route::middleware(['role:superadmin'])->group(function () {
    Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');
    Route::get('/admin/vendors', [VendorController::class, 'index'])->name('admin.vendors');

});


require __DIR__.'/auth.php';

