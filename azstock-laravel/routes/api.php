<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CategoryApiController;
use App\Http\Controllers\Api\ProductApiController;
use App\Http\Controllers\Api\AuctionApiController;
use App\Http\Controllers\Api\BidApiController;
use App\Http\Controllers\Api\VendorApiController;
use App\Http\Controllers\Api\UserApiController;
use App\Http\Controllers\Api\WalletApiController;
use App\Http\Controllers\Api\ActivityLogController;
use App\Http\Controllers\Api\ImageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// Categories
Route::get('/categories', [CategoryApiController::class, 'index']);
Route::get('/categories/{category}', [CategoryApiController::class, 'show']);

// Products
Route::get('/products', [ProductApiController::class, 'index']);
Route::get('/products/{product}', [ProductApiController::class, 'show']);

// Auctions
Route::get('/auctions', [AuctionApiController::class, 'index']);
Route::get('/auctions/{auction}', [AuctionApiController::class, 'show']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [UserApiController::class, 'show']);

    // Categories
    Route::post('/categories', [CategoryApiController::class, 'store']);
    Route::put('/categories/{category}', [CategoryApiController::class, 'update']);
    Route::delete('/categories/{category}', [CategoryApiController::class, 'destroy']);

    // Products
    Route::post('/products', [ProductApiController::class, 'store']);
    Route::put('/products/{product}', [ProductApiController::class, 'update']);
    Route::delete('/products/{product}', [ProductApiController::class, 'destroy']);

    // Auctions
    Route::post('/auctions', [AuctionApiController::class, 'store']);
    Route::put('/auctions/{auction}', [AuctionApiController::class, 'update']);
    Route::delete('/auctions/{auction}', [AuctionApiController::class, 'destroy']);
    Route::post('/auctions/{auction}/end', [AuctionApiController::class, 'endAuction']);
    Route::post('/auctions/{auction}/refunds', [AuctionApiController::class, 'processRefunds']);
    Route::get('/my-auctions', [AuctionApiController::class, 'myAuctions']);

    // Auction Product Management
    Route::post('/auctions/{auction}/products', [AuctionApiController::class, 'addProducts']);
    Route::delete('/auctions/{auction}/products', [AuctionApiController::class, 'removeProducts']);

    // My Categories and Products
    Route::get('/my-categories', [CategoryApiController::class, 'myCategories']);
    Route::get('/my-products', [ProductApiController::class, 'myProducts']);

    // Images
    Route::post('/images/upload', [ImageController::class, 'uploadImages']); // General image upload
    Route::post('/images/products', [ImageController::class, 'uploadProductImages']); // Legacy endpoint
    Route::delete('/images/{image}', [ImageController::class, 'deleteProductImage']);
    Route::get('/products/{product}/images', [ImageController::class, 'getProductImages']);
    Route::post('/images/debug-url', [ImageController::class, 'debugUrl']); // Debug endpoint
    Route::get('/images/debug-product', [ImageController::class, 'debugProductImages']); // Debug endpoint
    Route::post('/images/test-move', [ImageController::class, 'testImageMove']); // Debug endpoint

    // Bids
    Route::post('/auctions/{auction}/bids', [BidApiController::class, 'store']);
    Route::get('/my-bids', [BidApiController::class, 'myBids']);
    Route::post('/bids/{bid}/cancel', [BidApiController::class, 'cancel']);

    // Wallet
    Route::get('/wallet', [WalletApiController::class, 'show']);
    Route::post('/wallet/deposit', [WalletApiController::class, 'deposit']);
    Route::post('/wallet/withdraw', [WalletApiController::class, 'withdraw']);
    Route::get('/wallet/transactions', [WalletApiController::class, 'transactions']);
    Route::get('/wallet/holds', [WalletApiController::class, 'holds']);

    // Vendors
    Route::get('/vendors', [VendorApiController::class, 'index']);
    Route::get('/vendors/{vendor}', [VendorApiController::class, 'show']);
    Route::post('/vendors', [VendorApiController::class, 'store']);
    Route::put('/vendors/{vendor}', [VendorApiController::class, 'update']);
    Route::delete('/vendors/{vendor}', [VendorApiController::class, 'destroy']);

    // Admin User Management
    Route::get('/admin/users', [UserApiController::class, 'index']);
    Route::post('/admin/users', [UserApiController::class, 'store']);
    Route::get('/admin/users/{user}', [UserApiController::class, 'showUser']);
    Route::put('/admin/users/{user}', [UserApiController::class, 'updateUser']);
    Route::delete('/admin/users/{user}', [UserApiController::class, 'destroy']);

    // User Verification Management (Admin only)
    Route::post('/admin/users/{user}/verify', [UserApiController::class, 'verifyUser']);
    Route::get('/admin/users/{user}/verification-status', [UserApiController::class, 'getVerificationStatus']);

    // Admin Activity Logs
    Route::get('/admin/activity-logs', [ActivityLogController::class, 'index']);
    Route::get('/admin/activity-logs/statistics', [ActivityLogController::class, 'statistics']);
});