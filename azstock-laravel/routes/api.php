<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CategoryApiController;
use App\Http\Controllers\Api\AuctionApiController;
use App\Http\Controllers\Api\BidApiController;
use App\Http\Controllers\Api\VendorApiController;
use App\Http\Controllers\Api\UserApiController;
use App\Http\Controllers\Api\WalletApiController;
use App\Http\Controllers\Api\ActivityLogController;
use App\Http\Controllers\Api\WatchlistApiController;
use App\Http\Controllers\Api\VendorStatsApiController;
use App\Http\Controllers\Api\ImageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// Categories
Route::get('/categories', [CategoryApiController::class, 'index']);
Route::get('/categories/{category}', [CategoryApiController::class, 'show']);

// Note: Products functionality replaced by auction items

// Auctions
Route::get('/auctions', [AuctionApiController::class, 'index']);
Route::get('/auctions/{auction}', [AuctionApiController::class, 'show']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [UserApiController::class, 'show']);

    // Categories
    Route::post('/categories', [CategoryApiController::class, 'store']);
    Route::put('/categories/{category}', [CategoryApiController::class, 'update']);
    Route::delete('/categories/{category}', [CategoryApiController::class, 'destroy']);

    // Note: Product CRUD replaced by auction item management

    // Auctions
    Route::post('/auctions', [AuctionApiController::class, 'store']);
    Route::put('/auctions/{auction}', [AuctionApiController::class, 'update']);
    Route::delete('/auctions/{auction}', [AuctionApiController::class, 'destroy']);
    Route::post('/auctions/{auction}/end', [AuctionApiController::class, 'endAuction']);
    Route::post('/auctions/{auction}/refunds', [AuctionApiController::class, 'processRefunds']);
    Route::get('/my-auctions', [AuctionApiController::class, 'myAuctions']);

    // Auction Item Management
    Route::post('/auctions/{auction}/items', [AuctionApiController::class, 'addItems']);
    Route::delete('/auctions/{auction}/items', [AuctionApiController::class, 'removeItems']);

    // My Categories
    Route::get('/my-categories', [CategoryApiController::class, 'myCategories']);

    // Images
    Route::post('/images/upload', [ImageController::class, 'uploadImages']); // General image upload
    Route::post('/images/auction-items', [ImageController::class, 'uploadAuctionItemImages']); // Auction item images
    Route::delete('/images/{image}', [ImageController::class, 'deleteAuctionItemImage']);
    Route::get('/auction-items/{auctionItem}/images', [ImageController::class, 'getAuctionItemImages']);

    // Bids
    Route::post('/auctions/{auction}/bids', [BidApiController::class, 'store']);
    Route::get('/my-bids', [BidApiController::class, 'myBids']);
    Route::post('/bids/{bid}/cancel', [BidApiController::class, 'cancel']);

    // Wallet
    Route::get('/wallet', [WalletApiController::class, 'show']);
    Route::post('/wallet/deposit', [WalletApiController::class, 'deposit']);
    Route::post('/wallet/withdraw', [WalletApiController::class, 'withdraw']);
    Route::get('/wallet/transactions', [WalletApiController::class, 'transactions']);
    Route::get('/wallet/holds', [WalletApiController::class, 'holds']);

    // Vendors
    Route::get('/vendors', [VendorApiController::class, 'index']);
    Route::get('/vendors/{vendor}', [VendorApiController::class, 'show']);
    Route::get('/vendors/{vendor}/statistics', [VendorApiController::class, 'statistics']);
    Route::post('/vendors', [VendorApiController::class, 'store']);
    Route::put('/vendors/{vendor}', [VendorApiController::class, 'update']);
    Route::delete('/vendors/{vendor}', [VendorApiController::class, 'destroy']);

    // Vendor Profile (for authenticated vendor)
    Route::get('/vendor/profile', [VendorApiController::class, 'profile']);

    // Vendor Statistics
    Route::get('/vendor/stats', [VendorStatsApiController::class, 'index']);
    Route::get('/vendor/dashboard-stats', [VendorStatsApiController::class, 'dashboard']);

    // Watchlist
    Route::get('/watchlist', [WatchlistApiController::class, 'index']);
    Route::post('/watchlist', [WatchlistApiController::class, 'store']);
    Route::delete('/watchlist/{auctionId}', [WatchlistApiController::class, 'destroy']);
    Route::get('/watchlist/check/{auctionId}', [WatchlistApiController::class, 'check']);
    Route::get('/watchlist/stats', [WatchlistApiController::class, 'stats']);

    // Admin User Management
    Route::get('/admin/users', [UserApiController::class, 'index']);
    Route::post('/admin/users', [UserApiController::class, 'store']);
    Route::get('/admin/users/{user}', [UserApiController::class, 'showUser']);
    Route::put('/admin/users/{user}', [UserApiController::class, 'updateUser']);
    Route::delete('/admin/users/{user}', [UserApiController::class, 'destroy']);

    // User Verification Management (Admin only)
    Route::post('/admin/users/{user}/verify', [UserApiController::class, 'verifyUser']);
    Route::get('/admin/users/{user}/verification-status', [UserApiController::class, 'getVerificationStatus']);

    // Admin Activity Logs
    Route::get('/admin/activity-logs', [ActivityLogController::class, 'index']);
    Route::get('/admin/activity-logs/statistics', [ActivityLogController::class, 'statistics']);
});