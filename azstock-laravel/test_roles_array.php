<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use Spatie\Permission\Models\Role;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Testing Roles Array Consistency ===" . PHP_EOL;

try {
    // Ensure roles exist
    Role::firstOrCreate(['name' => 'buyer']);
    Role::firstOrCreate(['name' => 'vendor']);
    Role::firstOrCreate(['name' => 'admin']);

    // Test 1: User with single role
    echo "Test 1: Single role user" . PHP_EOL;
    $singleRoleUser = User::where('email', '<EMAIL>')->first();
    if (!$singleRoleUser) {
        $singleRoleUser = User::create([
            'name' => 'Single Role User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'buyer',
            'is_verified_buyer' => true,
            'is_verified_vendor' => false,
            'is_verified_admin' => false,
        ]);
        $singleRoleUser->assignRole('buyer');
    }

    $singleRoles = $singleRoleUser->getUserRoles();
    echo "- Roles: " . json_encode($singleRoles) . PHP_EOL;
    echo "- Is array: " . (is_array($singleRoles) ? 'YES' : 'NO') . PHP_EOL;
    echo "- Is indexed: " . (array_keys($singleRoles) === range(0, count($singleRoles) - 1) ? 'YES' : 'NO') . PHP_EOL;

    // Test 2: User with multiple roles
    echo PHP_EOL . "Test 2: Multi-role user" . PHP_EOL;
    $multiRoleUser = User::where('email', '<EMAIL>')->first();
    if (!$multiRoleUser) {
        $multiRoleUser = User::create([
            'name' => 'Multi Role User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'buyer',
            'is_verified_buyer' => true,
            'is_verified_vendor' => false,
            'is_verified_admin' => false,
        ]);
    }
    
    // Assign multiple roles
    $multiRoleUser->syncRoles(['buyer', 'vendor']);
    $multiRoleUser->refresh();

    $multiRoles = $multiRoleUser->getUserRoles();
    echo "- Roles: " . json_encode($multiRoles) . PHP_EOL;
    echo "- Is array: " . (is_array($multiRoles) ? 'YES' : 'NO') . PHP_EOL;
    echo "- Is indexed: " . (array_keys($multiRoles) === range(0, count($multiRoles) - 1) ? 'YES' : 'NO') . PHP_EOL;

    // Test 3: JSON encoding consistency
    echo PHP_EOL . "Test 3: JSON encoding" . PHP_EOL;
    $singleJson = json_encode(['roles' => $singleRoles]);
    $multiJson = json_encode(['roles' => $multiRoles]);
    
    echo "- Single role JSON: " . $singleJson . PHP_EOL;
    echo "- Multi role JSON: " . $multiJson . PHP_EOL;

    // Test 4: Verification status endpoint format
    echo PHP_EOL . "Test 4: Verification status format" . PHP_EOL;
    $verificationData = [
        'user_id' => $multiRoleUser->id,
        'name' => $multiRoleUser->name,
        'email' => $multiRoleUser->email,
        'verification_status' => $multiRoleUser->getVerificationStatus(),
        'roles' => $multiRoleUser->getUserRoles(),
    ];
    
    echo "- Verification endpoint JSON: " . json_encode($verificationData, JSON_PRETTY_PRINT) . PHP_EOL;

    echo PHP_EOL . "🎉 All tests passed! Roles are consistently returned as indexed arrays." . PHP_EOL;

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    echo "File: " . $e->getFile() . ":" . $e->getLine() . PHP_EOL;
}
