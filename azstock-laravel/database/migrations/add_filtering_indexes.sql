-- Database Indexes for Enhanced Filtering Performance
-- Run these indexes to optimize the filtering queries implemented in the API

-- Products table indexes
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_vendor_id ON products(vendor_id);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_name_search ON products(name);
CREATE INDEX idx_products_description_search ON products(description);
-- Composite index for common filter combinations
CREATE INDEX idx_products_category_vendor ON products(category_id, vendor_id);
CREATE INDEX idx_products_vendor_created ON products(vendor_id, created_at);

-- Auctions table indexes
CREATE INDEX idx_auctions_status ON auctions(status);
CREATE INDEX idx_auctions_vendor_id ON auctions(vendor_id);
CREATE INDEX idx_auctions_start_time ON auctions(start_time);
CREATE INDEX idx_auctions_end_time ON auctions(end_time);
CREATE INDEX idx_auctions_current_price ON auctions(current_price);
CREATE INDEX idx_auctions_starting_price ON auctions(starting_price);
CREATE INDEX idx_auctions_created_at ON auctions(created_at);
-- Composite indexes for common filter combinations
CREATE INDEX idx_auctions_status_end_time ON auctions(status, end_time);
CREATE INDEX idx_auctions_vendor_status ON auctions(vendor_id, status);
CREATE INDEX idx_auctions_status_price ON auctions(status, current_price);

-- Bids table indexes
CREATE INDEX idx_bids_user_id ON bids(user_id);
CREATE INDEX idx_bids_auction_id ON bids(auction_id);
CREATE INDEX idx_bids_status ON bids(status);
CREATE INDEX idx_bids_bid_amount ON bids(bid_amount);
CREATE INDEX idx_bids_created_at ON bids(created_at);
-- Composite indexes for common filter combinations
CREATE INDEX idx_bids_user_status ON bids(user_id, status);
CREATE INDEX idx_bids_user_created ON bids(user_id, created_at);
CREATE INDEX idx_bids_auction_amount ON bids(auction_id, bid_amount);

-- Users table indexes
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email_verified_at ON users(email_verified_at);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_name_search ON users(name);
CREATE INDEX idx_users_email_search ON users(email);
-- Composite indexes for admin filtering
CREATE INDEX idx_users_role_created ON users(role, created_at);
CREATE INDEX idx_users_role_verified ON users(role, email_verified_at);

-- Categories table indexes
CREATE INDEX idx_categories_name ON categories(name);
CREATE INDEX idx_categories_created_at ON categories(created_at);

-- Product Images table indexes (for image-related queries)
CREATE INDEX idx_product_images_product_id ON product_images(product_id);
CREATE INDEX idx_product_images_created_at ON product_images(created_at);

-- Wallet-related indexes
CREATE INDEX idx_wallets_user_id ON wallets(user_id);
CREATE INDEX idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(type);
CREATE INDEX idx_wallet_transactions_created_at ON wallet_transactions(created_at);
CREATE INDEX idx_wallet_holds_user_id ON wallet_holds(user_id);
CREATE INDEX idx_wallet_holds_status ON wallet_holds(status);
CREATE INDEX idx_wallet_holds_bid_id ON wallet_holds(bid_id);

-- Full-text search indexes (MySQL specific)
-- Uncomment if using MySQL and want full-text search capabilities
-- ALTER TABLE products ADD FULLTEXT(name, description);
-- ALTER TABLE categories ADD FULLTEXT(name);
-- ALTER TABLE users ADD FULLTEXT(name, email);

-- Performance monitoring queries
-- Use these to monitor index usage and query performance

-- Check index usage
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     SUB_PART,
--     PACKED,
--     NULLABLE,
--     INDEX_TYPE
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = 'your_database_name'
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- Monitor slow queries (enable slow query log)
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1; -- Log queries taking more than 1 second

-- Example queries to test index performance
-- EXPLAIN SELECT * FROM products WHERE category_id = 1 AND vendor_id = 2;
-- EXPLAIN SELECT * FROM auctions WHERE status = 'active' AND end_time <= NOW() + INTERVAL 24 HOUR;
-- EXPLAIN SELECT * FROM bids WHERE user_id = 1 AND status = 'active' ORDER BY created_at DESC;
