<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role-specific verification fields
            $table->boolean('is_verified_buyer')->default(false)->after('role');
            $table->boolean('is_verified_vendor')->default(false)->after('is_verified_buyer');
            $table->boolean('is_verified_admin')->default(false)->after('is_verified_vendor');
        });

        // Set existing users as verified for their current roles (backward compatibility)
        $this->migrateExistingUsers();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['is_verified_buyer', 'is_verified_vendor', 'is_verified_admin']);
        });
    }

    /**
     * Migrate existing users to be verified for their current roles
     */
    private function migrateExistingUsers(): void
    {
        // Get all existing users
        $users = DB::table('users')->get();
        
        foreach ($users as $user) {
            $updates = [];
            
            // Verify users for their current role
            switch ($user->role) {
                case 'buyer':
                    $updates['is_verified_buyer'] = true;
                    break;
                case 'vendor':
                    $updates['is_verified_vendor'] = true;
                    $updates['is_verified_buyer'] = true; // Vendors can also buy
                    break;
                case 'admin':
                    $updates['is_verified_admin'] = true;
                    $updates['is_verified_vendor'] = true;
                    $updates['is_verified_buyer'] = true; // Admins have all permissions
                    break;
            }
            
            if (!empty($updates)) {
                DB::table('users')
                    ->where('id', $user->id)
                    ->update($updates);
            }
        }
        
        // Also verify users based on their Spatie roles
        $this->verifySpatieRoles();
    }

    /**
     * Verify users based on their Spatie roles
     */
    private function verifySpatieRoles(): void
    {
        // Check if model_has_roles table exists (Spatie package)
        if (Schema::hasTable('model_has_roles')) {
            $userRoles = DB::table('model_has_roles')
                ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                ->where('model_type', 'App\\Models\\User')
                ->get(['model_id', 'roles.name']);
            
            foreach ($userRoles as $userRole) {
                $updates = [];
                
                switch ($userRole->name) {
                    case 'buyer':
                        $updates['is_verified_buyer'] = true;
                        break;
                    case 'vendor':
                        $updates['is_verified_vendor'] = true;
                        $updates['is_verified_buyer'] = true; // Vendors can also buy
                        break;
                    case 'admin':
                        $updates['is_verified_admin'] = true;
                        $updates['is_verified_vendor'] = true;
                        $updates['is_verified_buyer'] = true; // Admins have all permissions
                        break;
                }
                
                if (!empty($updates)) {
                    DB::table('users')
                        ->where('id', $userRole->model_id)
                        ->update($updates);
                }
            }
        }
    }
};
