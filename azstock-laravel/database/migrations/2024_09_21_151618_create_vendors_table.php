<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVendorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id(); // Primary key (auto-incrementing)
            $table->string('name'); // Vendor's name (e.g., Amazon)
            $table->string('slug')->unique(); // URL-friendly version (e.g., 'mywebsite.com/amazon')
            $table->string('email')->unique(); // Contact email
            $table->string('logo')->nullable(); // Path to vendor's logo
            $table->string('website')->nullable(); // Optional website URL
            $table->string('phone')->nullable(); // Optional contact phone number
            $table->string('address')->nullable(); // Vendor's address
            $table->text('description')->nullable(); // Description of the vendor
            $table->boolean('is_active')->default(true); // Status of the vendor
            $table->unsignedBigInteger('user_id'); // Foreign key to users table (if each vendor is created by a user)
            $table->timestamps(); // Created at & Updated at timestamps
            
            // Foreign key constraint for user
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vendors');
    }
}