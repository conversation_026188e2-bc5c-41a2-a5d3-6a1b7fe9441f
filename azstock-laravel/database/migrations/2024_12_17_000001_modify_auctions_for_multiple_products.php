<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyAuctionsForMultipleProducts extends Migration
{
    public function up()
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Make product_id nullable since products will now reference auctions
            $table->foreignId('product_id')->nullable()->change();
            
            // Add fields for multi-product auctions
            $table->string('title')->nullable()->after('vendor_id');
            $table->text('description')->nullable()->after('title');
            
            // Make starting_price nullable since individual products will have their own prices
            $table->decimal('starting_price', 10, 2)->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Revert changes
            $table->foreignId('product_id')->nullable(false)->change();
            $table->dropColumn(['title', 'description']);
            $table->decimal('starting_price', 10, 2)->nullable(false)->change();
        });
    }
}
