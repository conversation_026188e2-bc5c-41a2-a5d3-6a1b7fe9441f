<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuctionProductTable extends Migration
{
    public function up()
    {
        Schema::create('auction_product', function (Blueprint $table) {
            $table->id();
            $table->foreignId('auction_id')->constrained('auctions')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->decimal('starting_price', 10, 2);
            $table->decimal('current_price', 10, 2)->nullable();
            $table->decimal('reserve_price', 10, 2)->nullable();
            $table->timestamps();

            // Ensure unique auction-product combinations
            $table->unique(['auction_id', 'product_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('auction_product');
    }
}
