<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAuctionFieldsToProducts extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Add auction_id foreign key to establish one-to-many relationship
            $table->foreignId('auction_id')->nullable()->after('vendor_id')->constrained('auctions')->onDelete('set null');
            
            // Add auction-specific fields for each product
            $table->integer('auction_quantity')->nullable()->after('auction_id');
            $table->decimal('auction_starting_price', 10, 2)->nullable()->after('auction_quantity');
            $table->decimal('auction_current_price', 10, 2)->nullable()->after('auction_starting_price');
            $table->decimal('auction_reserve_price', 10, 2)->nullable()->after('auction_current_price');
        });
    }

    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['auction_id']);
            $table->dropColumn([
                'auction_id',
                'auction_quantity',
                'auction_starting_price',
                'auction_current_price',
                'auction_reserve_price'
            ]);
        });
    }
}
