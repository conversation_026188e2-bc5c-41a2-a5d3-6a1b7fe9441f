<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuctionItemImagesTable extends Migration
{
    public function up()
    {
        Schema::create('auction_item_images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('auction_item_id')->constrained('auction_items')->onDelete('cascade');
            $table->string('image_path');
            $table->string('thumbnail_url')->nullable();
            $table->string('original_name')->nullable();
            $table->bigInteger('file_size')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('auction_item_images');
    }
}
