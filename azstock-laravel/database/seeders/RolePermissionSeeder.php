<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Product permissions
            'create products',
            'edit products',
            'delete products',
            'view products',

            // Auction permissions
            'create auctions',
            'edit auctions',
            'delete auctions',
            'end auctions',
            'view auctions',
            'bid on auctions',

            // User management permissions
            'manage users',
            'view users',
            'edit users',
            'delete users',

            // Vendor permissions
            'manage vendors',
            'view vendors',
            'edit vendors',
            'delete vendors',

            // Category permissions
            'manage categories',
            'view categories',

            // Wallet permissions
            'manage wallet',
            'view wallet',
            'deposit funds',
            'withdraw funds',

            // Admin permissions
            'access admin panel',
            'view reports',
            'manage system settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $buyerRole = Role::create(['name' => 'buyer']);
        $buyerRole->givePermissionTo([
            'view products',
            'view auctions',
            'bid on auctions',
            'view wallet',
            'deposit funds',
        ]);

        $vendorRole = Role::create(['name' => 'vendor']);
        $vendorRole->givePermissionTo([
            'view products',
            'create products',
            'edit products',
            'delete products',
            'view auctions',
            'create auctions',
            'edit auctions',
            'delete auctions',
            'end auctions',
            'view wallet',
            'deposit funds',
            'withdraw funds',
            'manage wallet',
        ]);

        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all()); // Admin gets all permissions

        echo "Roles and permissions created successfully!\n";
        echo "- Buyer role: " . $buyerRole->permissions->count() . " permissions\n";
        echo "- Vendor role: " . $vendorRole->permissions->count() . " permissions\n";
        echo "- Admin role: " . $adminRole->permissions->count() . " permissions\n";
    }
}
