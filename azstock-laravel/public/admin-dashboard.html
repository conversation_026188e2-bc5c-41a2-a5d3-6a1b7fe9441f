<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
            color: #2196F3;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
        }
        .chart-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart-card {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button.delete {
            background-color: #f44336;
        }
        .button.delete:hover {
            background-color: #d32f2f;
        }
        .nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav a {
            padding: 10px 15px;
            background-color: #f1f1f1;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
        }
        .nav a:hover, .nav a.active {
            background-color: #4CAF50;
            color: white;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background-color: #f5f5f5;
        }
        .table tr:hover {
            background-color: #f9f9f9;
        }
        .date-filter {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }
        .date-filter input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Admin Dashboard</h1>
        <div>
            <span id="user-name"></span>
            <button id="logout-button" class="button delete">Logout</button>
        </div>
    </div>
    
    <div id="login-section" class="card">
        <h2>Admin Login</h2>
        <form id="login-form">
            <div>
                <label for="email">Email</label>
                <input type="email" id="email" required>
            </div>
            <div>
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <button type="submit" class="button">Login</button>
            <div id="login-error" class="error hidden"></div>
        </form>
    </div>
    
    <div id="dashboard-section" class="hidden">
        <div class="nav">
            <a href="admin-dashboard.html" class="active">Dashboard</a>
            <a href="admin-panel.html">User Management</a>
            <a href="admin-auctions.html">Auctions</a>
            <a href="admin-products.html">Products</a>
            <a href="admin-logs.html">Activity Logs</a>
        </div>
        
        <div class="date-filter">
            <label for="start-date">From:</label>
            <input type="date" id="start-date">
            <label for="end-date">To:</label>
            <input type="date" id="end-date">
            <button id="filter-button" class="button">Apply Filter</button>
        </div>
        
        <div id="dashboard-loading" class="loading">Loading dashboard data...</div>
        
        <div id="dashboard-content" class="hidden">
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-label">Total Users</div>
                    <div class="stat-value" id="total-users">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Total Products</div>
                    <div class="stat-value" id="total-products">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Active Auctions</div>
                    <div class="stat-value" id="active-auctions">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Total Bids</div>
                    <div class="stat-value" id="total-bids">0</div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-card">
                    <h3>User Activity</h3>
                    <canvas id="activity-chart"></canvas>
                </div>
                <div class="chart-card">
                    <h3>User Roles Distribution</h3>
                    <canvas id="roles-chart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>Recent Activity</h3>
                <table class="table" id="activity-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Action</th>
                            <th>Description</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody id="activity-table-body">
                        <!-- Activity rows will be added here -->
                    </tbody>
                </table>
            </div>
            
            <div class="card">
                <h3>Top Users by Activity</h3>
                <table class="table" id="top-users-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Activity Count</th>
                        </tr>
                    </thead>
                    <tbody id="top-users-table-body">
                        <!-- Top users rows will be added here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('adminToken');
        let currentUser = null;
        let activityChart = null;
        let rolesChart = null;
        
        // Set default date range (last 30 days)
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        
        document.getElementById('start-date').value = formatDate(thirtyDaysAgo);
        document.getElementById('end-date').value = formatDate(today);
        
        // Format date as YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // Check if admin is already logged in
        if (authToken) {
            fetchCurrentUser();
        } else {
            document.getElementById('login-section').classList.remove('hidden');
        }
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const errorElement = document.getElementById('login-error');
            errorElement.classList.add('hidden');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('email').value,
                        password: document.getElementById('password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Check if user is admin
                    if (data.data.role !== 'admin') {
                        errorElement.textContent = 'Access denied. Admin privileges required.';
                        errorElement.classList.remove('hidden');
                        return;
                    }
                    
                    // Save token and user data
                    localStorage.setItem('adminToken', data.token);
                    authToken = data.token;
                    currentUser = data.data;
                    
                    // Show dashboard section, hide login section
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('dashboard-section').classList.remove('hidden');
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Load dashboard data
                    loadDashboardData();
                } else {
                    errorElement.textContent = data.message || 'Login failed. Please check your credentials.';
                    errorElement.classList.remove('hidden');
                }
            } catch (error) {
                errorElement.textContent = 'An error occurred. Please try again.';
                errorElement.classList.remove('hidden');
                console.error('Login error:', error);
            }
        });
        
        // Logout button
        document.getElementById('logout-button').addEventListener('click', function() {
            // Clear token and user data
            localStorage.removeItem('adminToken');
            authToken = null;
            currentUser = null;
            
            // Show login section, hide dashboard section
            document.getElementById('login-section').classList.remove('hidden');
            document.getElementById('dashboard-section').classList.add('hidden');
            
            // Clear login form
            document.getElementById('login-form').reset();
            document.getElementById('login-error').classList.add('hidden');
        });
        
        // Filter button
        document.getElementById('filter-button').addEventListener('click', function() {
            loadDashboardData();
        });
        
        // Fetch current user
        async function fetchCurrentUser() {
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.data;
                    
                    // Check if user is admin
                    if (currentUser.role !== 'admin') {
                        // Not an admin, show login section
                        document.getElementById('login-section').classList.remove('hidden');
                        return;
                    }
                    
                    // Show dashboard section, hide login section
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('dashboard-section').classList.remove('hidden');
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Load dashboard data
                    loadDashboardData();
                } else {
                    // Token might be invalid, show login section
                    document.getElementById('login-section').classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error fetching current user:', error);
                document.getElementById('login-section').classList.remove('hidden');
            }
        }
        
        // Load dashboard data
        async function loadDashboardData() {
            document.getElementById('dashboard-loading').classList.remove('hidden');
            document.getElementById('dashboard-content').classList.add('hidden');
            
            try {
                // Get date range
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                // Fetch activity log statistics
                const statsResponse = await fetch(`/api/admin/activity-logs/statistics?start_date=${startDate}&end_date=${endDate}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (statsResponse.ok) {
                    const statsData = await statsResponse.json();
                    
                    // Update statistics
                    updateStatistics(statsData);
                    
                    // Update charts
                    updateCharts(statsData);
                    
                    // Update activity table
                    updateActivityTable(statsData);
                    
                    // Update top users table
                    updateTopUsersTable(statsData);
                    
                    document.getElementById('dashboard-loading').classList.add('hidden');
                    document.getElementById('dashboard-content').classList.remove('hidden');
                } else {
                    console.error('Error loading statistics:', await statsResponse.json());
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }
        
        // Update statistics
        function updateStatistics(data) {
            // For now, we'll use placeholder data
            // In a real implementation, you would fetch this data from the API
            document.getElementById('total-users').textContent = '42';
            document.getElementById('total-products').textContent = '156';
            document.getElementById('active-auctions').textContent = '23';
            document.getElementById('total-bids').textContent = '312';
        }
        
        // Update charts
        function updateCharts(data) {
            // Activity chart
            const activityCtx = document.getElementById('activity-chart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (activityChart) {
                activityChart.destroy();
            }
            
            // Create new chart
            activityChart = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: data.daily_activity.map(item => item.date),
                    datasets: [{
                        label: 'User Activity',
                        data: data.daily_activity.map(item => item.count),
                        backgroundColor: 'rgba(33, 150, 243, 0.2)',
                        borderColor: 'rgba(33, 150, 243, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Roles chart
            const rolesCtx = document.getElementById('roles-chart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (rolesChart) {
                rolesChart.destroy();
            }
            
            // Create new chart with placeholder data
            // In a real implementation, you would fetch this data from the API
            rolesChart = new Chart(rolesCtx, {
                type: 'pie',
                data: {
                    labels: ['Buyers', 'Vendors', 'Admins'],
                    datasets: [{
                        data: [25, 15, 2],
                        backgroundColor: [
                            'rgba(76, 175, 80, 0.7)',
                            'rgba(33, 150, 243, 0.7)',
                            'rgba(244, 67, 54, 0.7)'
                        ],
                        borderColor: [
                            'rgba(76, 175, 80, 1)',
                            'rgba(33, 150, 243, 1)',
                            'rgba(244, 67, 54, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true
                }
            });
        }
        
        // Update activity table
        function updateActivityTable(data) {
            // For now, we'll use placeholder data
            // In a real implementation, you would fetch this data from the API
            const tableBody = document.getElementById('activity-table-body');
            tableBody.innerHTML = '';
            
            const activities = [
                { user: 'John Doe', action: 'login', description: 'User logged in', date: '2023-05-14 10:23:45' },
                { user: 'Jane Smith', action: 'create', description: 'Created new product', date: '2023-05-14 09:15:30' },
                { user: 'Bob Johnson', action: 'update', description: 'Updated auction #123', date: '2023-05-13 16:42:18' },
                { user: 'Alice Brown', action: 'bid', description: 'Placed bid on auction #456', date: '2023-05-13 14:30:22' },
                { user: 'Charlie Wilson', action: 'logout', description: 'User logged out', date: '2023-05-13 12:10:05' }
            ];
            
            activities.forEach(activity => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${activity.user}</td>
                    <td>${activity.action}</td>
                    <td>${activity.description}</td>
                    <td>${activity.date}</td>
                `;
                tableBody.appendChild(row);
            });
        }
        
        // Update top users table
        function updateTopUsersTable(data) {
            const tableBody = document.getElementById('top-users-table-body');
            tableBody.innerHTML = '';
            
            if (data.top_users && data.top_users.length > 0) {
                data.top_users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.name}</td>
                        <td>${user.email}</td>
                        <td>${user.role}</td>
                        <td>${user.count}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                // No data, show placeholder
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="4">No data available</td>';
                tableBody.appendChild(row);
            }
        }
    </script>
</body>
</html>
