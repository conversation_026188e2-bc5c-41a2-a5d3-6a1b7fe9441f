<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>API Authentication Test</h1>
    
    <div class="card">
        <h2>Register</h2>
        <form id="register-form">
            <div class="form-group">
                <label for="register-name">Name</label>
                <input type="text" id="register-name" required>
            </div>
            <div class="form-group">
                <label for="register-email">Email</label>
                <input type="email" id="register-email" required>
            </div>
            <div class="form-group">
                <label for="register-password">Password</label>
                <input type="password" id="register-password" required>
            </div>
            <div class="form-group">
                <label for="register-password-confirmation">Confirm Password</label>
                <input type="password" id="register-password-confirmation" required>
            </div>
            <button type="submit">Register</button>
        </form>
        <div id="register-result" class="hidden"></div>
    </div>
    
    <div class="card">
        <h2>Login</h2>
        <form id="login-form">
            <div class="form-group">
                <label for="login-email">Email</label>
                <input type="email" id="login-email" required>
            </div>
            <div class="form-group">
                <label for="login-password">Password</label>
                <input type="password" id="login-password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="login-result" class="hidden"></div>
    </div>
    
    <div class="card">
        <h2>Test Protected Endpoint</h2>
        <div class="form-group">
            <label for="token">Bearer Token</label>
            <input type="text" id="token" placeholder="Paste your token here">
        </div>
        <button id="test-auth-btn">Test Authentication</button>
        <div id="test-result" class="hidden"></div>
    </div>
    
    <script>
        // Register form submission
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('register-result');
            resultElement.innerHTML = 'Registering...';
            resultElement.className = '';
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        name: document.getElementById('register-name').value,
                        email: document.getElementById('register-email').value,
                        password: document.getElementById('register-password').value,
                        password_confirmation: document.getElementById('register-password-confirmation').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `
                        <p>Registration successful!</p>
                        <p>Token: ${data.token}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    // Auto-fill the token field
                    document.getElementById('token').value = data.token;
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Registration failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
            
            resultElement.classList.remove('hidden');
        });
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('login-result');
            resultElement.innerHTML = 'Logging in...';
            resultElement.className = '';
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('login-email').value,
                        password: document.getElementById('login-password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `
                        <p>Login successful!</p>
                        <p>Token: ${data.token}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    // Auto-fill the token field
                    document.getElementById('token').value = data.token;
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Login failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
            
            resultElement.classList.remove('hidden');
        });
        
        // Test authentication
        document.getElementById('test-auth-btn').addEventListener('click', async function() {
            const token = document.getElementById('token').value;
            const resultElement = document.getElementById('test-result');
            
            if (!token) {
                resultElement.className = 'error';
                resultElement.innerHTML = 'Please enter a token first!';
                resultElement.classList.remove('hidden');
                return;
            }
            
            resultElement.innerHTML = 'Testing authentication...';
            resultElement.className = '';
            
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `
                        <p>Authentication successful!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Authentication failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
            
            resultElement.classList.remove('hidden');
        });
    </script>
</body>
</html>
