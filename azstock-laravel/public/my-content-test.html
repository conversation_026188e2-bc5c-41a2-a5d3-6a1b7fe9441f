<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Content Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.delete {
            background-color: #f44336;
        }
        button.delete:hover {
            background-color: #d32f2f;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            background-color: #f1f1f1;
            cursor: pointer;
            border: 1px solid #ddd;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .user-info {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .role-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            margin-left: 10px;
        }
        .role-buyer {
            background-color: #4CAF50;
            color: white;
        }
        .role-vendor {
            background-color: #2196F3;
            color: white;
        }
        .role-admin {
            background-color: #f44336;
            color: white;
        }
        .hidden {
            display: none;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .item-list {
            display: grid;
            gap: 15px;
        }
        .item-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .item-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .item-meta {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>My Content Test</h1>
        <div>
            <span id="user-name"></span>
            <button id="logout-button" class="button delete">Logout</button>
        </div>
    </div>
    
    <div id="auth-section" class="card">
        <h2>Authentication</h2>
        <div class="tabs">
            <div class="tab active" data-tab="login">Login</div>
            <div class="tab" data-tab="register">Register</div>
        </div>
        
        <div class="tab-content active" id="login-tab">
            <form id="login-form">
                <div class="form-group">
                    <label for="login-email">Email</label>
                    <input type="email" id="login-email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" required>
                </div>
                <button type="submit">Login</button>
            </form>
            <div id="login-result" class="hidden"></div>
        </div>
        
        <div class="tab-content" id="register-tab">
            <form id="register-form">
                <div class="form-group">
                    <label for="register-name">Name</label>
                    <input type="text" id="register-name" required>
                </div>
                <div class="form-group">
                    <label for="register-email">Email</label>
                    <input type="email" id="register-email" required>
                </div>
                <div class="form-group">
                    <label for="register-password">Password</label>
                    <input type="password" id="register-password" required>
                </div>
                <div class="form-group">
                    <label for="register-password-confirmation">Confirm Password</label>
                    <input type="password" id="register-password-confirmation" required>
                </div>
                <div class="form-group">
                    <label for="register-role">Role</label>
                    <select id="register-role" required>
                        <option value="buyer">Buyer</option>
                        <option value="vendor">Vendor</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <button type="submit">Register</button>
            </form>
            <div id="register-result" class="hidden"></div>
        </div>
    </div>
    
    <div id="content-section" class="hidden">
        <div class="user-info">
            <h2>User Profile</h2>
            <div id="user-details"></div>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-tab="categories">My Categories</div>
            <div class="tab" data-tab="products">My Products</div>
            <div class="tab" data-tab="auctions">My Auctions</div>
        </div>
        
        <div class="tab-content active" id="categories-tab">
            <div class="card">
                <h3>My Categories</h3>
                <button id="load-categories">Load My Categories</button>
                <button id="create-category">Create Test Category</button>
                <div id="categories-result" class="hidden"></div>
                <div id="categories-list" class="item-list"></div>
            </div>
        </div>
        
        <div class="tab-content" id="products-tab">
            <div class="card">
                <h3>My Products</h3>
                <button id="load-products">Load My Products</button>
                <div id="products-result" class="hidden"></div>
                <div id="products-list" class="item-list"></div>
            </div>
        </div>
        
        <div class="tab-content" id="auctions-tab">
            <div class="card">
                <h3>My Auctions</h3>
                <button id="load-auctions">Load My Auctions</button>
                <div id="auctions-result" class="hidden"></div>
                <div id="auctions-list" class="item-list"></div>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        
        // Check if user is already logged in
        if (authToken) {
            fetchUserProfile();
        }
        
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabGroup = this.parentElement;
                const contentContainer = tabGroup.nextElementSibling;
                
                // Remove active class from all tabs in this group
                tabGroup.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Hide all tab content in this container
                const allTabContent = contentContainer.parentElement.querySelectorAll('.tab-content');
                allTabContent.forEach(content => content.classList.remove('active'));
                
                // Show the corresponding tab content
                const targetTab = document.getElementById(this.dataset.tab + '-tab');
                if (targetTab) {
                    targetTab.classList.add('active');
                }
            });
        });
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('login-result');
            resultElement.innerHTML = 'Logging in...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('login-email').value,
                        password: document.getElementById('login-password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Login successful!</p>`;
                    
                    // Save token to localStorage
                    localStorage.setItem('authToken', data.token);
                    authToken = data.token;
                    
                    // Fetch user profile
                    fetchUserProfile();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Login failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Register form submission
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('register-result');
            resultElement.innerHTML = 'Registering...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        name: document.getElementById('register-name').value,
                        email: document.getElementById('register-email').value,
                        password: document.getElementById('register-password').value,
                        password_confirmation: document.getElementById('register-password-confirmation').value,
                        role: document.getElementById('register-role').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Registration successful!</p>`;
                    
                    // Save token to localStorage
                    localStorage.setItem('authToken', data.token);
                    authToken = data.token;
                    
                    // Fetch user profile
                    fetchUserProfile();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Registration failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Logout button
        document.getElementById('logout-button').addEventListener('click', async function() {
            try {
                await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
            } catch (error) {
                console.error('Error logging out:', error);
            }
            
            // Clear token and user data
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            
            // Show auth section, hide content section
            document.getElementById('auth-section').classList.remove('hidden');
            document.getElementById('content-section').classList.add('hidden');
            
            // Clear form fields
            document.getElementById('login-form').reset();
            document.getElementById('register-form').reset();
            
            // Hide results
            document.getElementById('login-result').classList.add('hidden');
            document.getElementById('register-result').classList.add('hidden');
        });
        
        // Fetch user profile
        async function fetchUserProfile() {
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.data;
                    
                    // Update UI based on user
                    updateUserInterface();
                    
                    // Hide auth section, show content section
                    document.getElementById('auth-section').classList.add('hidden');
                    document.getElementById('content-section').classList.remove('hidden');
                } else {
                    // Token might be invalid, clear it
                    localStorage.removeItem('authToken');
                    authToken = null;
                }
            } catch (error) {
                console.error('Error fetching user profile:', error);
            }
        }
        
        // Update user interface
        function updateUserInterface() {
            if (!currentUser) return;
            
            // Update user details
            const userDetailsElement = document.getElementById('user-details');
            const roleBadgeClass = `role-badge role-${currentUser.role}`;
            
            userDetailsElement.innerHTML = `
                <p><strong>Name:</strong> ${currentUser.name}</p>
                <p><strong>Email:</strong> ${currentUser.email}</p>
                <p><strong>Role:</strong> <span class="${roleBadgeClass}">${currentUser.role.toUpperCase()}</span></p>
            `;
            
            // Update user name in header
            document.getElementById('user-name').textContent = currentUser.name;
        }
        
        // Load categories button
        document.getElementById('load-categories').addEventListener('click', async function() {
            const resultElement = document.getElementById('categories-result');
            const listElement = document.getElementById('categories-list');
            
            resultElement.innerHTML = 'Loading categories...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            listElement.innerHTML = '';
            
            try {
                const response = await fetch('/api/my-categories', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Loaded ${data.data.length} categories</p>`;
                    
                    // Display categories
                    if (data.data.length > 0) {
                        data.data.forEach(category => {
                            const categoryCard = document.createElement('div');
                            categoryCard.className = 'item-card';
                            categoryCard.innerHTML = `
                                <div class="item-title">${category.name}</div>
                                <div class="item-meta">
                                    ID: ${category.id} | 
                                    Products: ${category.products_count || 0} | 
                                    Created: ${new Date(category.created_at).toLocaleDateString()}
                                </div>
                            `;
                            listElement.appendChild(categoryCard);
                        });
                    } else {
                        listElement.innerHTML = '<p>No categories found. Create one to get started!</p>';
                    }
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to load categories!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Create category button
        document.getElementById('create-category').addEventListener('click', async function() {
            const resultElement = document.getElementById('categories-result');
            
            resultElement.innerHTML = 'Creating test category...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/categories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        name: `Test Category ${Date.now()}`
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Category created successfully!</p>`;
                    
                    // Reload categories
                    document.getElementById('load-categories').click();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to create category!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Load products button
        document.getElementById('load-products').addEventListener('click', async function() {
            const resultElement = document.getElementById('products-result');
            const listElement = document.getElementById('products-list');
            
            resultElement.innerHTML = 'Loading products...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            listElement.innerHTML = '';
            
            try {
                const response = await fetch('/api/my-products', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Loaded ${data.data.length} products</p>`;
                    
                    // Display products
                    if (data.data.length > 0) {
                        data.data.forEach(product => {
                            const productCard = document.createElement('div');
                            productCard.className = 'item-card';
                            productCard.innerHTML = `
                                <div class="item-title">${product.name}</div>
                                <div class="item-meta">
                                    ID: ${product.id} | 
                                    Category: ${product.category ? product.category.name : 'N/A'} | 
                                    Images: ${product.images ? product.images.length : 0} | 
                                    Created: ${new Date(product.created_at).toLocaleDateString()}
                                </div>
                                <div style="margin-top: 10px;">
                                    ${product.description || 'No description'}
                                </div>
                            `;
                            listElement.appendChild(productCard);
                        });
                    } else {
                        listElement.innerHTML = '<p>No products found. You need to be a vendor to create products.</p>';
                    }
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to load products!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Load auctions button
        document.getElementById('load-auctions').addEventListener('click', async function() {
            const resultElement = document.getElementById('auctions-result');
            const listElement = document.getElementById('auctions-list');
            
            resultElement.innerHTML = 'Loading auctions...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            listElement.innerHTML = '';
            
            try {
                const response = await fetch('/api/my-auctions', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Loaded ${data.data.length} auctions</p>`;
                    
                    // Display auctions
                    if (data.data.length > 0) {
                        data.data.forEach(auction => {
                            const auctionCard = document.createElement('div');
                            auctionCard.className = 'item-card';
                            auctionCard.innerHTML = `
                                <div class="item-title">${auction.product ? auction.product.name : 'Unknown Product'}</div>
                                <div class="item-meta">
                                    ID: ${auction.id} | 
                                    Status: ${auction.status} | 
                                    Current Price: $${auction.current_price} | 
                                    Start: ${new Date(auction.start_time).toLocaleDateString()}
                                </div>
                            `;
                            listElement.appendChild(auctionCard);
                        });
                    } else {
                        listElement.innerHTML = '<p>No auctions found. You need to be a vendor to create auctions.</p>';
                    }
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to load auctions!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
