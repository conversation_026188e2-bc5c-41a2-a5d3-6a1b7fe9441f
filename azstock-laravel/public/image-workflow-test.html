<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>New Image Upload Workflow Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            h1,
            h2,
            h3 {
                color: #333;
            }
            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #ddd;
            }
            .card {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .form-group {
                margin-bottom: 15px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            input,
            select,
            textarea {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            }
            button {
                padding: 10px 15px;
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 10px;
            }
            button:hover {
                background-color: #45a049;
            }
            button.delete {
                background-color: #f44336;
            }
            button.delete:hover {
                background-color: #d32f2f;
            }
            button:disabled {
                background-color: #cccccc;
                cursor: not-allowed;
            }
            .hidden {
                display: none;
            }
            pre {
                background-color: #f5f5f5;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                max-height: 400px;
                overflow-y: auto;
            }
            .error {
                color: red;
            }
            .success {
                color: green;
            }
            .warning {
                color: orange;
            }
            .file-input-wrapper {
                position: relative;
                display: inline-block;
                cursor: pointer;
                background-color: #f1f1f1;
                border: 2px dashed #ccc;
                border-radius: 5px;
                padding: 20px;
                text-align: center;
                width: 100%;
                box-sizing: border-box;
            }
            .file-input-wrapper:hover {
                background-color: #e9e9e9;
                border-color: #999;
            }
            .file-input-wrapper input[type="file"] {
                position: absolute;
                left: -9999px;
            }
            .uploaded-images {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }
            .uploaded-image {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                text-align: center;
                background-color: #f9f9f9;
            }
            .uploaded-image img {
                max-width: 100%;
                height: 100px;
                object-fit: cover;
                border-radius: 5px;
            }
            .uploaded-image .image-info {
                margin-top: 5px;
                font-size: 12px;
                color: #666;
            }
            .workflow-step {
                background-color: #e8f4fd;
                border-left: 4px solid #2196f3;
                padding: 15px;
                margin-bottom: 20px;
            }
            .workflow-step h3 {
                margin-top: 0;
                color: #1976d2;
            }
            .step-number {
                display: inline-block;
                background-color: #2196f3;
                color: white;
                border-radius: 50%;
                width: 25px;
                height: 25px;
                text-align: center;
                line-height: 25px;
                margin-right: 10px;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>New Image Upload Workflow Test</h1>
            <div>
                <span id="user-name"></span>
                <button id="logout-button" class="button delete">Logout</button>
            </div>
        </div>

        <div id="auth-section" class="card">
            <h2>Login</h2>
            <form id="login-form">
                <div class="form-group">
                    <label for="login-email">Email</label>
                    <input type="email" id="login-email" required />
                </div>
                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" required />
                </div>
                <button type="submit">Login</button>
            </form>
            <div id="login-result" class="hidden"></div>
        </div>

        <div id="main-section" class="hidden">
            <div class="workflow-step">
                <h3><span class="step-number">1</span>Upload Images First</h3>
                <p>
                    The new workflow requires uploading images first to get
                    their URLs, then using those URLs when creating or updating
                    products.
                </p>
            </div>

            <div class="card">
                <h2>Step 1: Upload Images</h2>
                <div class="form-group">
                    <label>Select Images (Max 10 files, 5MB each)</label>
                    <div class="file-input-wrapper">
                        <input
                            type="file"
                            id="image-files"
                            multiple
                            accept="image/*"
                        />
                        <div>
                            <p>Click to select images or drag and drop</p>
                            <p>Supported formats: JPEG, PNG, GIF, WebP</p>
                        </div>
                    </div>
                </div>
                <button id="upload-images" disabled>Upload Images</button>
                <div id="upload-result" class="hidden"></div>
                <div id="uploaded-images" class="uploaded-images"></div>
            </div>

            <div class="workflow-step">
                <h3>
                    <span class="step-number">2</span>Create Product with Image
                    URLs
                </h3>
                <p>
                    After uploading images, use the returned URLs to create a
                    product.
                </p>
            </div>

            <div class="card">
                <h2>Step 2: Create Product with Images</h2>
                <form id="product-form">
                    <div class="form-group">
                        <label for="product-name">Product Name</label>
                        <input type="text" id="product-name" required />
                    </div>
                    <div class="form-group">
                        <label for="product-description">Description</label>
                        <textarea id="product-description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="product-category">Category</label>
                        <select id="product-category" required>
                            <option value="">Select a category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Featured Image URL (Optional)</label>
                        <input
                            type="text"
                            id="featured-image-url"
                            placeholder="Select a featured image URL from uploaded images"
                        />
                        <small
                            >If not specified, the first additional image will
                            be used as featured image.</small
                        >
                    </div>
                    <div class="form-group">
                        <label>Additional Image URLs</label>
                        <textarea
                            id="image-urls"
                            rows="4"
                            placeholder="Additional image URLs will appear here after upload..."
                            readonly
                        ></textarea>
                        <small
                            >These URLs will be automatically filled when you
                            upload images above.</small
                        >
                    </div>
                    <button type="submit" id="create-product" disabled>
                        Create Product
                    </button>
                </form>
                <div id="product-result" class="hidden"></div>
            </div>

            <div class="workflow-step">
                <h3><span class="step-number">3</span>Update Product Images</h3>
                <p>
                    You can also update existing products by uploading new
                    images and using their URLs.
                </p>
            </div>

            <div class="card">
                <h2>Step 3: Update Existing Product</h2>
                <div class="form-group">
                    <label for="existing-product"
                        >Select Product to Update</label
                    >
                    <select id="existing-product">
                        <option value="">Select a product</option>
                    </select>
                    <button id="load-products">Load My Products</button>
                </div>
                <form id="update-form">
                    <div class="form-group">
                        <label for="update-name">Product Name</label>
                        <input type="text" id="update-name" required />
                    </div>
                    <div class="form-group">
                        <label for="update-description">Description</label>
                        <textarea id="update-description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="update-category">Category</label>
                        <select id="update-category" required>
                            <option value="">Select a category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Featured Image URL (Optional)</label>
                        <input
                            type="text"
                            id="update-featured-image-url"
                            placeholder="Select a featured image URL from uploaded images"
                        />
                        <small
                            >If provided, this will become the new featured
                            image. Leave empty to keep existing or use first
                            additional image.</small
                        >
                    </div>
                    <div class="form-group">
                        <label>Additional Image URLs (from Step 1)</label>
                        <textarea
                            id="update-image-urls"
                            rows="4"
                            placeholder="Copy image URLs from Step 1..."
                        ></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="replace-images" />
                            Replace all existing images
                        </label>
                        <small
                            >If unchecked, new images will be added to existing
                            ones.</small
                        >
                    </div>
                    <button type="submit" id="update-product" disabled>
                        Update Product
                    </button>
                </form>
                <div id="update-result" class="hidden"></div>
            </div>

            <div class="card">
                <h2>My Products</h2>
                <button id="view-products">View My Products</button>
                <div id="products-result" class="hidden"></div>
            </div>
        </div>

        <script>
            // Global variables
            let authToken = localStorage.getItem("authToken");
            let currentUser = null;
            let categories = [];
            let products = [];
            let uploadedImageUrls = [];

            // Check if user is already logged in
            if (authToken) {
                fetchUserProfile();
            }

            // Login form submission
            document
                .getElementById("login-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const resultElement =
                        document.getElementById("login-result");
                    resultElement.innerHTML = "Logging in...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/login", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                Accept: "application/json",
                            },
                            body: JSON.stringify({
                                email: document.getElementById("login-email")
                                    .value,
                                password:
                                    document.getElementById("login-password")
                                        .value,
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `<p>Login successful!</p>`;

                            // Save token to localStorage
                            localStorage.setItem("authToken", data.token);
                            authToken = data.token;

                            // Fetch user profile
                            fetchUserProfile();
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Login failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Logout button
            document
                .getElementById("logout-button")
                .addEventListener("click", async function () {
                    try {
                        await fetch("/api/logout", {
                            method: "POST",
                            headers: {
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                        });
                    } catch (error) {
                        console.error("Error logging out:", error);
                    }

                    // Clear token and user data
                    localStorage.removeItem("authToken");
                    authToken = null;
                    currentUser = null;

                    // Show auth section, hide main section
                    document
                        .getElementById("auth-section")
                        .classList.remove("hidden");
                    document
                        .getElementById("main-section")
                        .classList.add("hidden");

                    // Clear form fields
                    document.getElementById("login-form").reset();
                    document
                        .getElementById("login-result")
                        .classList.add("hidden");
                });

            // Fetch user profile
            async function fetchUserProfile() {
                try {
                    const response = await fetch("/api/user", {
                        method: "GET",
                        headers: {
                            Accept: "application/json",
                            Authorization: `Bearer ${authToken}`,
                        },
                    });

                    if (response.ok) {
                        const data = await response.json();
                        currentUser = data.data;

                        // Update user name in header
                        document.getElementById("user-name").textContent =
                            currentUser.name;

                        // Hide auth section, show main section
                        document
                            .getElementById("auth-section")
                            .classList.add("hidden");
                        document
                            .getElementById("main-section")
                            .classList.remove("hidden");

                        // Load categories
                        loadCategories();
                    } else {
                        // Token might be invalid, clear it
                        localStorage.removeItem("authToken");
                        authToken = null;
                    }
                } catch (error) {
                    console.error("Error fetching user profile:", error);
                }
            }

            // Load categories
            async function loadCategories() {
                try {
                    const response = await fetch("/api/categories", {
                        method: "GET",
                        headers: {
                            Accept: "application/json",
                        },
                    });

                    if (response.ok) {
                        const data = await response.json();
                        categories = data.data;

                        // Populate both category selects
                        const selects = ["product-category", "update-category"];
                        selects.forEach((selectId) => {
                            const categorySelect =
                                document.getElementById(selectId);
                            categorySelect.innerHTML =
                                '<option value="">Select a category</option>';

                            categories.forEach((category) => {
                                const option = document.createElement("option");
                                option.value = category.id;
                                option.textContent = category.name;
                                categorySelect.appendChild(option);
                            });
                        });
                    }
                } catch (error) {
                    console.error("Error loading categories:", error);
                }
            }

            // File input change handler
            document
                .getElementById("image-files")
                .addEventListener("change", function () {
                    const uploadButton =
                        document.getElementById("upload-images");
                    uploadButton.disabled = this.files.length === 0;
                });

            // Upload images button
            document
                .getElementById("upload-images")
                .addEventListener("click", async function () {
                    const files = document.getElementById("image-files").files;
                    const resultElement =
                        document.getElementById("upload-result");
                    const imagesContainer =
                        document.getElementById("uploaded-images");

                    if (files.length === 0) {
                        resultElement.className = "error";
                        resultElement.innerHTML =
                            "<p>Please select images first.</p>";
                        resultElement.classList.remove("hidden");
                        return;
                    }

                    resultElement.innerHTML = "Uploading images...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const formData = new FormData();
                        for (let i = 0; i < files.length; i++) {
                            formData.append("images[]", files[i]);
                        }

                        const response = await fetch("/api/images/upload", {
                            method: "POST",
                            headers: {
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                            body: formData,
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Images uploaded successfully!</p>
                        <p>Uploaded: ${data.images.length} images</p>
                        ${
                            data.upload_errors.length > 0
                                ? `<p class="error">Errors: ${data.upload_errors.length}</p>`
                                : ""
                        }
                    `;

                            // Store uploaded image URLs
                            uploadedImageUrls = data.images.map(
                                (img) => img.url
                            );

                            // Debug: Log the URLs to console
                            console.log(
                                "Uploaded image URLs:",
                                uploadedImageUrls
                            );

                            // Update the image URLs textarea
                            document.getElementById("image-urls").value =
                                JSON.stringify(uploadedImageUrls, null, 2);

                            // Enable create product button
                            document.getElementById(
                                "create-product"
                            ).disabled = false;

                            // Display uploaded images
                            imagesContainer.innerHTML = "";
                            data.images.forEach((image) => {
                                const imageDiv = document.createElement("div");
                                imageDiv.className = "uploaded-image";
                                imageDiv.innerHTML = `
                            <img src="${
                                image.thumbnail_url || image.url
                            }" alt="${image.original_name}">
                            <div class="image-info">
                                ${image.original_name}<br>
                                <small>${image.url}</small>
                            </div>
                        `;
                                imagesContainer.appendChild(imageDiv);
                            });

                            // Reset file input
                            document.getElementById("image-files").value = "";
                            document.getElementById(
                                "upload-images"
                            ).disabled = true;
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Failed to upload images!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Product form submission
            document
                .getElementById("product-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const resultElement =
                        document.getElementById("product-result");
                    resultElement.innerHTML = "Creating product...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/products", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                            body: JSON.stringify({
                                name: document.getElementById("product-name")
                                    .value,
                                description: document.getElementById(
                                    "product-description"
                                ).value,
                                category_id:
                                    document.getElementById("product-category")
                                        .value,
                                image_url:
                                    document.getElementById(
                                        "featured-image-url"
                                    ).value || undefined,
                                image_urls: uploadedImageUrls,
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Product created successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;

                            // Reset form
                            document.getElementById("product-form").reset();
                            document.getElementById("image-urls").value = "";
                            document.getElementById(
                                "featured-image-url"
                            ).value = "";
                            document.getElementById(
                                "create-product"
                            ).disabled = true;
                            uploadedImageUrls = [];
                            document.getElementById(
                                "uploaded-images"
                            ).innerHTML = "";
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Failed to create product!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Load products button
            document
                .getElementById("load-products")
                .addEventListener("click", async function () {
                    try {
                        const response = await fetch("/api/my-products", {
                            method: "GET",
                            headers: {
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                        });

                        if (response.ok) {
                            const data = await response.json();
                            products = data.data;

                            const productSelect =
                                document.getElementById("existing-product");
                            productSelect.innerHTML =
                                '<option value="">Select a product</option>';

                            products.forEach((product) => {
                                const option = document.createElement("option");
                                option.value = product.id;
                                option.textContent = product.name;
                                productSelect.appendChild(option);
                            });
                        }
                    } catch (error) {
                        console.error("Error loading products:", error);
                    }
                });

            // Product selection change
            document
                .getElementById("existing-product")
                .addEventListener("change", function () {
                    const productId = this.value;
                    const updateButton =
                        document.getElementById("update-product");

                    if (productId) {
                        const product = products.find((p) => p.id == productId);
                        if (product) {
                            document.getElementById("update-name").value =
                                product.name;
                            document.getElementById(
                                "update-description"
                            ).value = product.description || "";
                            document.getElementById("update-category").value =
                                product.category ? product.category.id : "";
                            updateButton.disabled = false;
                        }
                    } else {
                        document.getElementById("update-form").reset();
                        updateButton.disabled = true;
                    }
                });

            // Update form submission
            document
                .getElementById("update-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const productId =
                        document.getElementById("existing-product").value;
                    const resultElement =
                        document.getElementById("update-result");

                    if (!productId) {
                        resultElement.className = "error";
                        resultElement.innerHTML =
                            "<p>Please select a product first.</p>";
                        resultElement.classList.remove("hidden");
                        return;
                    }

                    resultElement.innerHTML = "Updating product...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const imageUrls = document
                            .getElementById("update-image-urls")
                            .value.trim();
                        const imageUrlsArray = imageUrls
                            ? imageUrls
                                  .split("\n")
                                  .map((url) => url.trim())
                                  .filter((url) => url)
                            : [];

                        const response = await fetch(
                            `/api/products/${productId}`,
                            {
                                method: "PUT",
                                headers: {
                                    "Content-Type": "application/json",
                                    Accept: "application/json",
                                    Authorization: `Bearer ${authToken}`,
                                },
                                body: JSON.stringify({
                                    name: document.getElementById("update-name")
                                        .value,
                                    description:
                                        document.getElementById(
                                            "update-description"
                                        ).value,
                                    category_id:
                                        document.getElementById(
                                            "update-category"
                                        ).value,
                                    image_urls: imageUrlsArray,
                                    replace_images:
                                        document.getElementById(
                                            "replace-images"
                                        ).checked,
                                }),
                            }
                        );

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Product updated successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Failed to update product!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // View products button
            document
                .getElementById("view-products")
                .addEventListener("click", async function () {
                    const resultElement =
                        document.getElementById("products-result");

                    resultElement.innerHTML = "Loading products...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/my-products", {
                            method: "GET",
                            headers: {
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Loaded ${data.data.length} products</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Failed to load products!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });
        </script>
    </body>
</html>
