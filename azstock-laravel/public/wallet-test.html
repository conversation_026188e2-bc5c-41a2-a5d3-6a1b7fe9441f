<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Wallet System Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }
            h1,
            h2 {
                text-align: center;
            }
            .card {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 20px;
                margin-bottom: 20px;
            }
            .form-group {
                margin-bottom: 15px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            input[type="text"],
            input[type="number"],
            input[type="email"],
            input[type="password"] {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            }
            button {
                padding: 10px 15px;
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background-color: #45a049;
            }
            pre {
                background-color: #f5f5f5;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
                max-height: 300px;
                overflow-y: auto;
            }
            .error {
                color: red;
            }
            .success {
                color: green;
            }
            .hidden {
                display: none;
            }
            .wallet-info {
                background-color: #f0f8ff;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .balance {
                font-size: 24px;
                font-weight: bold;
                text-align: center;
                margin-bottom: 10px;
            }
            .balance-details {
                display: flex;
                justify-content: space-around;
            }
            .balance-item {
                text-align: center;
            }
            .balance-label {
                font-size: 12px;
                color: #666;
            }
            .balance-value {
                font-size: 18px;
                font-weight: bold;
            }
            .tabs {
                display: flex;
                margin-bottom: 20px;
            }
            .tab {
                flex: 1;
                text-align: center;
                padding: 10px;
                background-color: #f1f1f1;
                cursor: pointer;
            }
            .tab.active {
                background-color: #4caf50;
                color: white;
            }
            .tab-content {
                display: none;
            }
            .tab-content.active {
                display: block;
            }
        </style>
    </head>
    <body>
        <h1>Wallet System Test</h1>

        <div class="card">
            <h2>Authentication</h2>
            <div class="tabs">
                <div class="tab active" data-tab="login">Login</div>
                <div class="tab" data-tab="register">Register</div>
            </div>

            <div class="tab-content active" id="login-tab">
                <form id="login-form">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" required />
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required />
                    </div>
                    <button type="submit">Login</button>
                </form>
                <div id="login-result" class="hidden"></div>
            </div>

            <div class="tab-content" id="register-tab">
                <form id="register-form">
                    <div class="form-group">
                        <label for="register-name">Name</label>
                        <input type="text" id="register-name" required />
                    </div>
                    <div class="form-group">
                        <label for="register-email">Email</label>
                        <input type="email" id="register-email" required />
                    </div>
                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <input
                            type="password"
                            id="register-password"
                            required
                        />
                    </div>
                    <div class="form-group">
                        <label for="register-password-confirmation"
                            >Confirm Password</label
                        >
                        <input
                            type="password"
                            id="register-password-confirmation"
                            required
                        />
                    </div>
                    <div class="form-group">
                        <label for="register-role">Role</label>
                        <select id="register-role" required>
                            <option value="buyer">Buyer</option>
                            <option value="vendor">Vendor</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <button type="submit">Register</button>
                </form>
                <div id="register-result" class="hidden"></div>
            </div>
        </div>

        <div id="wallet-section" class="hidden">
            <div class="wallet-info">
                <div class="balance">Wallet Balance</div>
                <div class="balance-details">
                    <div class="balance-item">
                        <div class="balance-label">Total</div>
                        <div class="balance-value" id="total-balance">
                            $0.00
                        </div>
                    </div>
                    <div class="balance-item">
                        <div class="balance-label">Available</div>
                        <div class="balance-value" id="available-balance">
                            $0.00
                        </div>
                    </div>
                    <div class="balance-item">
                        <div class="balance-label">On Hold</div>
                        <div class="balance-value" id="held-balance">$0.00</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h2>Deposit Funds</h2>
                <form id="deposit-form">
                    <div class="form-group">
                        <label for="deposit-amount">Amount</label>
                        <input
                            type="number"
                            id="deposit-amount"
                            min="0.01"
                            step="0.01"
                            required
                        />
                    </div>
                    <div class="form-group">
                        <label for="payment-method">Payment Method</label>
                        <input
                            type="text"
                            id="payment-method"
                            value="credit_card"
                            required
                        />
                    </div>
                    <button type="submit">Deposit</button>
                </form>
                <div id="deposit-result" class="hidden"></div>
            </div>

            <div class="card">
                <h2>Place Bid</h2>
                <form id="bid-form">
                    <div class="form-group">
                        <label for="auction-id">Auction ID</label>
                        <input type="number" id="auction-id" required />
                    </div>
                    <div class="form-group">
                        <label for="bid-amount">Bid Amount</label>
                        <input
                            type="number"
                            id="bid-amount"
                            min="0.01"
                            step="0.01"
                            required
                        />
                    </div>
                    <button type="submit">Place Bid</button>
                </form>
                <div id="bid-result" class="hidden"></div>
            </div>

            <div class="card">
                <h2>My Bids</h2>
                <button id="load-bids">Load My Bids</button>
                <div id="bids-result" class="hidden"></div>
            </div>

            <div class="card">
                <h2>Wallet Transactions</h2>
                <button id="load-transactions">Load Transactions</button>
                <div id="transactions-result" class="hidden"></div>
            </div>
        </div>

        <script>
            // Global variables
            let authToken = localStorage.getItem("authToken");

            // Check if user is already logged in
            if (authToken) {
                document
                    .getElementById("wallet-section")
                    .classList.remove("hidden");
                loadWalletInfo();
            }

            // Tab functionality
            document.querySelectorAll(".tab").forEach((tab) => {
                tab.addEventListener("click", function () {
                    // Remove active class from all tabs
                    document
                        .querySelectorAll(".tab")
                        .forEach((t) => t.classList.remove("active"));
                    // Add active class to clicked tab
                    this.classList.add("active");

                    // Hide all tab content
                    document
                        .querySelectorAll(".tab-content")
                        .forEach((content) =>
                            content.classList.remove("active")
                        );
                    // Show the corresponding tab content
                    document
                        .getElementById(this.dataset.tab + "-tab")
                        .classList.add("active");
                });
            });

            // Login form submission
            document
                .getElementById("login-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const resultElement =
                        document.getElementById("login-result");
                    resultElement.innerHTML = "Logging in...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/login", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                Accept: "application/json",
                            },
                            body: JSON.stringify({
                                email: document.getElementById("login-email")
                                    .value,
                                password:
                                    document.getElementById("login-password")
                                        .value,
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `<p>Login successful!</p>`;

                            // Save token to localStorage
                            localStorage.setItem("authToken", data.token);
                            authToken = data.token;

                            // Show wallet section
                            document
                                .getElementById("wallet-section")
                                .classList.remove("hidden");

                            // Load wallet info
                            loadWalletInfo();
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Login failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Register form submission
            document
                .getElementById("register-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const resultElement =
                        document.getElementById("register-result");
                    resultElement.innerHTML = "Registering...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/register", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                Accept: "application/json",
                            },
                            body: JSON.stringify({
                                name: document.getElementById("register-name")
                                    .value,
                                email: document.getElementById("register-email")
                                    .value,
                                password:
                                    document.getElementById("register-password")
                                        .value,
                                password_confirmation: document.getElementById(
                                    "register-password-confirmation"
                                ).value,
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `<p>Registration successful!</p>`;

                            // Save token to localStorage
                            localStorage.setItem("authToken", data.token);
                            authToken = data.token;

                            // Show wallet section
                            document
                                .getElementById("wallet-section")
                                .classList.remove("hidden");

                            // Load wallet info
                            loadWalletInfo();
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Registration failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Load wallet info
            async function loadWalletInfo() {
                try {
                    const response = await fetch("/api/wallet", {
                        method: "GET",
                        headers: {
                            Accept: "application/json",
                            Authorization: `Bearer ${authToken}`,
                        },
                    });

                    const data = await response.json();

                    if (response.ok) {
                        document.getElementById(
                            "total-balance"
                        ).textContent = `$${data.data.balance}`;
                        document.getElementById(
                            "available-balance"
                        ).textContent = `$${data.data.available_balance}`;
                        document.getElementById(
                            "held-balance"
                        ).textContent = `$${data.data.held_balance}`;
                    } else {
                        console.error("Failed to load wallet info:", data);
                    }
                } catch (error) {
                    console.error("Error loading wallet info:", error);
                }
            }

            // Deposit form submission
            document
                .getElementById("deposit-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const resultElement =
                        document.getElementById("deposit-result");
                    resultElement.innerHTML = "Processing deposit...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/wallet/deposit", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                            body: JSON.stringify({
                                amount: document.getElementById(
                                    "deposit-amount"
                                ).value,
                                payment_method:
                                    document.getElementById("payment-method")
                                        .value,
                                payment_details: {
                                    card_number: "****************",
                                    expiry_month: 12,
                                    expiry_year: 2025,
                                    cvc: "123",
                                },
                            }),
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Deposit successful!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;

                            // Update wallet info
                            document.getElementById(
                                "total-balance"
                            ).textContent = `$${data.wallet_balance.total}`;
                            document.getElementById(
                                "available-balance"
                            ).textContent = `$${data.wallet_balance.available}`;
                            document.getElementById(
                                "held-balance"
                            ).textContent = `$${data.wallet_balance.held}`;
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Deposit failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Bid form submission
            document
                .getElementById("bid-form")
                .addEventListener("submit", async function (e) {
                    e.preventDefault();

                    const resultElement = document.getElementById("bid-result");
                    resultElement.innerHTML = "Placing bid...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const auctionId =
                            document.getElementById("auction-id").value;
                        const response = await fetch(
                            `/api/auctions/${auctionId}/bids`,
                            {
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json",
                                    Accept: "application/json",
                                    Authorization: `Bearer ${authToken}`,
                                },
                                body: JSON.stringify({
                                    bid_amount:
                                        document.getElementById("bid-amount")
                                            .value,
                                }),
                            }
                        );

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Bid placed successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;

                            // Update wallet info if wallet_balance is included in the response
                            if (data.wallet_balance) {
                                document.getElementById(
                                    "total-balance"
                                ).textContent = `$${data.wallet_balance.total}`;
                                document.getElementById(
                                    "available-balance"
                                ).textContent = `$${data.wallet_balance.available}`;
                                document.getElementById(
                                    "held-balance"
                                ).textContent = `$${data.wallet_balance.held}`;
                            }
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Bid failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Load bids
            document
                .getElementById("load-bids")
                .addEventListener("click", async function () {
                    const resultElement =
                        document.getElementById("bids-result");
                    resultElement.innerHTML = "Loading bids...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch("/api/my-bids", {
                            method: "GET",
                            headers: {
                                Accept: "application/json",
                                Authorization: `Bearer ${authToken}`,
                            },
                        });

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Bids loaded successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;

                            // Update wallet info if wallet_balance is included in the response
                            if (data.wallet_balance) {
                                document.getElementById(
                                    "total-balance"
                                ).textContent = `$${data.wallet_balance.total}`;
                                document.getElementById(
                                    "available-balance"
                                ).textContent = `$${data.wallet_balance.available}`;
                                document.getElementById(
                                    "held-balance"
                                ).textContent = `$${data.wallet_balance.held}`;
                            }
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Failed to load bids!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });

            // Load transactions
            document
                .getElementById("load-transactions")
                .addEventListener("click", async function () {
                    const resultElement = document.getElementById(
                        "transactions-result"
                    );
                    resultElement.innerHTML = "Loading transactions...";
                    resultElement.className = "";
                    resultElement.classList.remove("hidden");

                    try {
                        const response = await fetch(
                            "/api/wallet/transactions",
                            {
                                method: "GET",
                                headers: {
                                    Accept: "application/json",
                                    Authorization: `Bearer ${authToken}`,
                                },
                            }
                        );

                        const data = await response.json();

                        if (response.ok) {
                            resultElement.className = "success";
                            resultElement.innerHTML = `
                        <p>Transactions loaded successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        } else {
                            resultElement.className = "error";
                            resultElement.innerHTML = `
                        <p>Failed to load transactions!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                        }
                    } catch (error) {
                        resultElement.className = "error";
                        resultElement.innerHTML = `Error: ${error.message}`;
                    }
                });
        </script>
    </body>
</html>
