<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role-Based Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            text-align: center;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"], select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .hidden {
            display: none;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            background-color: #f1f1f1;
            cursor: pointer;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .user-info {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .role-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            margin-left: 10px;
        }
        .role-buyer {
            background-color: #4CAF50;
            color: white;
        }
        .role-vendor {
            background-color: #2196F3;
            color: white;
        }
        .role-admin {
            background-color: #f44336;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Role-Based Authentication Test</h1>
    
    <div id="auth-section">
        <div class="card">
            <h2>Authentication</h2>
            <div class="tabs">
                <div class="tab active" data-tab="login">Login</div>
                <div class="tab" data-tab="register">Register</div>
            </div>
            
            <div class="tab-content active" id="login-tab">
                <form id="login-form">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit">Login</button>
                </form>
                <div id="login-result" class="hidden"></div>
            </div>
            
            <div class="tab-content" id="register-tab">
                <form id="register-form">
                    <div class="form-group">
                        <label for="register-name">Name</label>
                        <input type="text" id="register-name" required>
                    </div>
                    <div class="form-group">
                        <label for="register-email">Email</label>
                        <input type="email" id="register-email" required>
                    </div>
                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <input type="password" id="register-password" required>
                    </div>
                    <div class="form-group">
                        <label for="register-password-confirmation">Confirm Password</label>
                        <input type="password" id="register-password-confirmation" required>
                    </div>
                    <div class="form-group">
                        <label for="register-role">Role</label>
                        <select id="register-role" required>
                            <option value="buyer">Buyer</option>
                            <option value="vendor">Vendor</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <button type="submit">Register</button>
                </form>
                <div id="register-result" class="hidden"></div>
            </div>
        </div>
    </div>
    
    <div id="user-section" class="hidden">
        <div class="user-info">
            <h2>User Profile</h2>
            <div id="user-details"></div>
            <button id="logout-button">Logout</button>
        </div>
        
        <div class="card" id="buyer-section">
            <h2>Buyer Actions</h2>
            <p>As a buyer, you can:</p>
            <ul>
                <li>View your wallet balance</li>
                <li>Place bids on auctions</li>
                <li>View your bid history</li>
            </ul>
            <button id="view-wallet-button">View Wallet</button>
            <div id="wallet-result" class="hidden"></div>
        </div>
        
        <div class="card hidden" id="vendor-section">
            <h2>Vendor Actions</h2>
            <p>As a vendor, you can:</p>
            <ul>
                <li>Create and manage products</li>
                <li>Create and manage auctions</li>
                <li>View your auctions</li>
            </ul>
            <button id="view-auctions-button">View My Auctions</button>
            <div id="auctions-result" class="hidden"></div>
        </div>
        
        <div class="card hidden" id="admin-section">
            <h2>Admin Actions</h2>
            <p>As an admin, you can:</p>
            <ul>
                <li>Manage all users</li>
                <li>Manage all products and auctions</li>
                <li>View system statistics</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        
        // Check if user is already logged in
        if (authToken) {
            fetchUserProfile();
        }
        
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Hide all tab content
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                // Show the corresponding tab content
                document.getElementById(this.dataset.tab + '-tab').classList.add('active');
            });
        });
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('login-result');
            resultElement.innerHTML = 'Logging in...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('login-email').value,
                        password: document.getElementById('login-password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Login successful!</p>`;
                    
                    // Save token to localStorage
                    localStorage.setItem('authToken', data.token);
                    authToken = data.token;
                    
                    // Fetch user profile
                    fetchUserProfile();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Login failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Register form submission
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('register-result');
            resultElement.innerHTML = 'Registering...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        name: document.getElementById('register-name').value,
                        email: document.getElementById('register-email').value,
                        password: document.getElementById('register-password').value,
                        password_confirmation: document.getElementById('register-password-confirmation').value,
                        role: document.getElementById('register-role').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Registration successful!</p>`;
                    
                    // Save token to localStorage
                    localStorage.setItem('authToken', data.token);
                    authToken = data.token;
                    
                    // Fetch user profile
                    fetchUserProfile();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Registration failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Logout button
        document.getElementById('logout-button').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                // Clear token and user data
                localStorage.removeItem('authToken');
                authToken = null;
                currentUser = null;
                
                // Show auth section, hide user section
                document.getElementById('auth-section').classList.remove('hidden');
                document.getElementById('user-section').classList.add('hidden');
                
                // Clear form fields
                document.getElementById('login-form').reset();
                document.getElementById('register-form').reset();
                
                // Hide results
                document.getElementById('login-result').classList.add('hidden');
                document.getElementById('register-result').classList.add('hidden');
            } catch (error) {
                console.error('Error logging out:', error);
            }
        });
        
        // Fetch user profile
        async function fetchUserProfile() {
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.data;
                    
                    // Update UI based on user role
                    updateUserInterface();
                    
                    // Hide auth section, show user section
                    document.getElementById('auth-section').classList.add('hidden');
                    document.getElementById('user-section').classList.remove('hidden');
                } else {
                    // Token might be invalid, clear it
                    localStorage.removeItem('authToken');
                    authToken = null;
                }
            } catch (error) {
                console.error('Error fetching user profile:', error);
            }
        }
        
        // Update user interface based on role
        function updateUserInterface() {
            if (!currentUser) return;
            
            // Update user details
            const userDetailsElement = document.getElementById('user-details');
            const roleBadgeClass = `role-badge role-${currentUser.role}`;
            
            userDetailsElement.innerHTML = `
                <p><strong>Name:</strong> ${currentUser.name}</p>
                <p><strong>Email:</strong> ${currentUser.email}</p>
                <p><strong>Role:</strong> <span class="${roleBadgeClass}">${currentUser.role.toUpperCase()}</span></p>
            `;
            
            // Show/hide role-specific sections
            document.getElementById('buyer-section').classList.add('hidden');
            document.getElementById('vendor-section').classList.add('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            
            if (currentUser.is_buyer) {
                document.getElementById('buyer-section').classList.remove('hidden');
            }
            
            if (currentUser.is_vendor) {
                document.getElementById('vendor-section').classList.remove('hidden');
            }
            
            if (currentUser.is_admin) {
                document.getElementById('admin-section').classList.remove('hidden');
            }
        }
        
        // View wallet button
        document.getElementById('view-wallet-button').addEventListener('click', async function() {
            const resultElement = document.getElementById('wallet-result');
            resultElement.innerHTML = 'Loading wallet...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/wallet', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `
                        <h3>Wallet Balance</h3>
                        <p><strong>Total:</strong> $${data.data.balance}</p>
                        <p><strong>Available:</strong> $${data.data.available_balance}</p>
                        <p><strong>On Hold:</strong> $${data.data.held_balance}</p>
                    `;
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to load wallet!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // View auctions button
        document.getElementById('view-auctions-button').addEventListener('click', async function() {
            const resultElement = document.getElementById('auctions-result');
            resultElement.innerHTML = 'Loading auctions...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/my-auctions', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    
                    if (data.data.length === 0) {
                        resultElement.innerHTML = `<p>You don't have any auctions yet.</p>`;
                    } else {
                        let auctionsHtml = `<h3>Your Auctions</h3><ul>`;
                        
                        data.data.forEach(auction => {
                            auctionsHtml += `
                                <li>
                                    <strong>${auction.product.name}</strong> - 
                                    Current Price: $${auction.current_price} - 
                                    Status: ${auction.status}
                                </li>
                            `;
                        });
                        
                        auctionsHtml += `</ul>`;
                        resultElement.innerHTML = auctionsHtml;
                    }
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to load auctions!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
