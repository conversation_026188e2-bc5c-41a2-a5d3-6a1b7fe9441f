<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .endpoint {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .endpoint h2 {
            margin-top: 0;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .loading {
            display: none;
            margin-left: 10px;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div class="endpoint">
        <h2>Get Categories</h2>
        <button onclick="testEndpoint('/api/categories')">Test</button>
        <span class="loading" id="categories-loading">Loading...</span>
        <div id="categories-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Products</h2>
        <button onclick="testEndpoint('/api/products')">Test</button>
        <span class="loading" id="products-loading">Loading...</span>
        <div id="products-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Auctions</h2>
        <button onclick="testEndpoint('/api/auctions')">Test</button>
        <span class="loading" id="auctions-loading">Loading...</span>
        <div id="auctions-result"></div>
    </div>
    
    <script>
        async function testEndpoint(url) {
            const endpointName = url.split('/').pop();
            const loadingElement = document.getElementById(`${endpointName}-loading`);
            const resultElement = document.getElementById(`${endpointName}-result`);
            
            loadingElement.style.display = 'inline';
            resultElement.innerHTML = '';
            
            try {
                const startTime = performance.now();
                const response = await fetch(url);
                const endTime = performance.now();
                const responseTime = (endTime - startTime).toFixed(2);
                
                const data = await response.json();
                
                let resultHtml = `<p>Status: ${response.status} ${response.statusText}</p>`;
                resultHtml += `<p>Response Time: ${responseTime}ms</p>`;
                resultHtml += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                resultElement.innerHTML = resultHtml;
            } catch (error) {
                resultElement.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            } finally {
                loadingElement.style.display = 'none';
            }
        }
    </script>
</body>
</html>
