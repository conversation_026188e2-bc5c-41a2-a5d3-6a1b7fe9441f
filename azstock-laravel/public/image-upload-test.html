<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.delete {
            background-color: #f44336;
        }
        button.delete:hover {
            background-color: #d32f2f;
        }
        .hidden {
            display: none;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
        }
        .image-item .image-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        .file-input-wrapper:hover {
            background-color: #e9e9e9;
            border-color: #999;
        }
        .file-input-wrapper input[type="file"] {
            position: absolute;
            left: -9999px;
        }
        .selected-files {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .product-list {
            display: grid;
            gap: 15px;
        }
        .product-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .product-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .product-meta {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Image Upload Test</h1>
        <div>
            <span id="user-name"></span>
            <button id="logout-button" class="button delete">Logout</button>
        </div>
    </div>
    
    <div id="auth-section" class="card">
        <h2>Login</h2>
        <form id="login-form">
            <div class="form-group">
                <label for="login-email">Email</label>
                <input type="email" id="login-email" required>
            </div>
            <div class="form-group">
                <label for="login-password">Password</label>
                <input type="password" id="login-password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="login-result" class="hidden"></div>
    </div>
    
    <div id="main-section" class="hidden">
        <div class="card">
            <h2>Create Product with Images</h2>
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">Product Name</label>
                    <input type="text" id="product-name" required>
                </div>
                <div class="form-group">
                    <label for="product-description">Description</label>
                    <textarea id="product-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="product-category">Category</label>
                    <select id="product-category" required>
                        <option value="">Select a category</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Product Images (Max 10 files, 5MB each)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="product-images" multiple accept="image/*">
                        <div>
                            <p>Click to select images or drag and drop</p>
                            <p>Supported formats: JPEG, PNG, GIF, WebP</p>
                        </div>
                    </div>
                    <div id="selected-files" class="selected-files hidden"></div>
                </div>
                <button type="submit">Create Product</button>
            </form>
            <div id="product-result" class="hidden"></div>
        </div>
        
        <div class="card">
            <h2>My Products</h2>
            <button id="load-products">Load My Products</button>
            <div id="products-result" class="hidden"></div>
            <div id="products-list" class="product-list"></div>
        </div>
        
        <div class="card">
            <h2>Upload Images to Existing Product</h2>
            <div class="form-group">
                <label for="existing-product">Select Product</label>
                <select id="existing-product">
                    <option value="">Select a product</option>
                </select>
            </div>
            <div class="form-group">
                <label>Additional Images</label>
                <div class="file-input-wrapper">
                    <input type="file" id="additional-images" multiple accept="image/*">
                    <div>
                        <p>Click to select additional images</p>
                    </div>
                </div>
                <div id="additional-selected-files" class="selected-files hidden"></div>
            </div>
            <button id="upload-additional">Upload Images</button>
            <div id="additional-result" class="hidden"></div>
        </div>
    </div>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let categories = [];
        let products = [];
        
        // Check if user is already logged in
        if (authToken) {
            fetchUserProfile();
        }
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('login-result');
            resultElement.innerHTML = 'Logging in...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('login-email').value,
                        password: document.getElementById('login-password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Login successful!</p>`;
                    
                    // Save token to localStorage
                    localStorage.setItem('authToken', data.token);
                    authToken = data.token;
                    
                    // Fetch user profile
                    fetchUserProfile();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Login failed!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Logout button
        document.getElementById('logout-button').addEventListener('click', async function() {
            try {
                await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
            } catch (error) {
                console.error('Error logging out:', error);
            }
            
            // Clear token and user data
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            
            // Show auth section, hide main section
            document.getElementById('auth-section').classList.remove('hidden');
            document.getElementById('main-section').classList.add('hidden');
            
            // Clear form fields
            document.getElementById('login-form').reset();
            document.getElementById('login-result').classList.add('hidden');
        });
        
        // Fetch user profile
        async function fetchUserProfile() {
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.data;
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Hide auth section, show main section
                    document.getElementById('auth-section').classList.add('hidden');
                    document.getElementById('main-section').classList.remove('hidden');
                    
                    // Load categories
                    loadCategories();
                    
                    // Load products for the dropdown
                    loadProductsForDropdown();
                } else {
                    // Token might be invalid, clear it
                    localStorage.removeItem('authToken');
                    authToken = null;
                }
            } catch (error) {
                console.error('Error fetching user profile:', error);
            }
        }
        
        // Load categories
        async function loadCategories() {
            try {
                const response = await fetch('/api/categories', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    categories = data.data;
                    
                    const categorySelect = document.getElementById('product-category');
                    categorySelect.innerHTML = '<option value="">Select a category</option>';
                    
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        categorySelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }
        
        // Load products for dropdown
        async function loadProductsForDropdown() {
            try {
                const response = await fetch('/api/my-products', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    products = data.data;
                    
                    const productSelect = document.getElementById('existing-product');
                    productSelect.innerHTML = '<option value="">Select a product</option>';
                    
                    products.forEach(product => {
                        const option = document.createElement('option');
                        option.value = product.id;
                        option.textContent = product.name;
                        productSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading products:', error);
            }
        }
        
        // File input handlers
        document.getElementById('product-images').addEventListener('change', function() {
            displaySelectedFiles(this.files, 'selected-files');
        });
        
        document.getElementById('additional-images').addEventListener('change', function() {
            displaySelectedFiles(this.files, 'additional-selected-files');
        });
        
        // Display selected files
        function displaySelectedFiles(files, containerId) {
            const container = document.getElementById(containerId);
            
            if (files.length === 0) {
                container.classList.add('hidden');
                return;
            }
            
            container.classList.remove('hidden');
            container.innerHTML = '<h4>Selected Files:</h4>';
            
            Array.from(files).forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    <span>${file.type}</span>
                `;
                container.appendChild(fileItem);
            });
        }
        
        // Product form submission
        document.getElementById('product-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultElement = document.getElementById('product-result');
            resultElement.innerHTML = 'Creating product...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const formData = new FormData();
                formData.append('name', document.getElementById('product-name').value);
                formData.append('description', document.getElementById('product-description').value);
                formData.append('category_id', document.getElementById('product-category').value);
                
                // Add images
                const images = document.getElementById('product-images').files;
                for (let i = 0; i < images.length; i++) {
                    formData.append('images[]', images[i]);
                }
                
                const response = await fetch('/api/products', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `
                        <p>Product created successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    // Reset form
                    document.getElementById('product-form').reset();
                    document.getElementById('selected-files').classList.add('hidden');
                    
                    // Reload products
                    loadProductsForDropdown();
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to create product!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Load products button
        document.getElementById('load-products').addEventListener('click', async function() {
            const resultElement = document.getElementById('products-result');
            const listElement = document.getElementById('products-list');
            
            resultElement.innerHTML = 'Loading products...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            listElement.innerHTML = '';
            
            try {
                const response = await fetch('/api/my-products', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `<p>Loaded ${data.data.length} products</p>`;
                    
                    // Display products
                    if (data.data.length > 0) {
                        data.data.forEach(product => {
                            const productCard = document.createElement('div');
                            productCard.className = 'product-item';
                            
                            let imagesHtml = '';
                            if (product.images && product.images.length > 0) {
                                imagesHtml = `
                                    <div class="image-gallery">
                                        ${product.images.map(image => `
                                            <div class="image-item">
                                                <img src="${image.thumbnail_url || image.url}" alt="${image.original_name || 'Product image'}">
                                                <div class="image-info">
                                                    ${image.original_name || 'Unknown'}<br>
                                                    ${image.file_size ? (image.file_size / 1024 / 1024).toFixed(2) + ' MB' : ''}
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                `;
                            } else {
                                imagesHtml = '<p>No images uploaded</p>';
                            }
                            
                            productCard.innerHTML = `
                                <div class="product-title">${product.name}</div>
                                <div class="product-meta">
                                    ID: ${product.id} | 
                                    Category: ${product.category ? product.category.name : 'N/A'} | 
                                    Images: ${product.images ? product.images.length : 0} | 
                                    Created: ${new Date(product.created_at).toLocaleDateString()}
                                </div>
                                <div style="margin-top: 10px;">
                                    ${product.description || 'No description'}
                                </div>
                                ${imagesHtml}
                            `;
                            listElement.appendChild(productCard);
                        });
                    } else {
                        listElement.innerHTML = '<p>No products found. You need to be a vendor to create products.</p>';
                    }
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to load products!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Upload additional images
        document.getElementById('upload-additional').addEventListener('click', async function() {
            const productId = document.getElementById('existing-product').value;
            const images = document.getElementById('additional-images').files;
            const resultElement = document.getElementById('additional-result');
            
            if (!productId) {
                resultElement.className = 'error';
                resultElement.innerHTML = '<p>Please select a product first.</p>';
                resultElement.classList.remove('hidden');
                return;
            }
            
            if (images.length === 0) {
                resultElement.className = 'error';
                resultElement.innerHTML = '<p>Please select images to upload.</p>';
                resultElement.classList.remove('hidden');
                return;
            }
            
            resultElement.innerHTML = 'Uploading images...';
            resultElement.className = '';
            resultElement.classList.remove('hidden');
            
            try {
                const formData = new FormData();
                formData.append('product_id', productId);
                
                for (let i = 0; i < images.length; i++) {
                    formData.append('images[]', images[i]);
                }
                
                const response = await fetch('/api/images/products', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'success';
                    resultElement.innerHTML = `
                        <p>Images uploaded successfully!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    // Reset form
                    document.getElementById('additional-images').value = '';
                    document.getElementById('additional-selected-files').classList.add('hidden');
                } else {
                    resultElement.className = 'error';
                    resultElement.innerHTML = `
                        <p>Failed to upload images!</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultElement.className = 'error';
                resultElement.innerHTML = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
