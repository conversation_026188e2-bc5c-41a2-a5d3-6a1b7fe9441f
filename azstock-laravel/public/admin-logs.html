<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Activity Logs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button.delete {
            background-color: #f44336;
        }
        .button.delete:hover {
            background-color: #d32f2f;
        }
        .nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav a {
            padding: 10px 15px;
            background-color: #f1f1f1;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
        }
        .nav a:hover, .nav a.active {
            background-color: #4CAF50;
            color: white;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th, .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background-color: #f5f5f5;
        }
        .table tr:hover {
            background-color: #f9f9f9;
        }
        .filter-section {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }
        .filter-section select, .filter-section input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            margin: 0 5px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            background-color: #f5f5f5;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .hidden {
            display: none;
        }
        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .badge-auth {
            background-color: #2196F3;
        }
        .badge-crud {
            background-color: #4CAF50;
        }
        .badge-system {
            background-color: #FF9800;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 70%;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Activity Logs</h1>
        <div>
            <span id="user-name"></span>
            <button id="logout-button" class="button delete">Logout</button>
        </div>
    </div>
    
    <div id="login-section" class="card">
        <h2>Admin Login</h2>
        <form id="login-form">
            <div>
                <label for="email">Email</label>
                <input type="email" id="email" required>
            </div>
            <div>
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <button type="submit" class="button">Login</button>
            <div id="login-error" class="error hidden"></div>
        </form>
    </div>
    
    <div id="logs-section" class="hidden">
        <div class="nav">
            <a href="admin-dashboard.html">Dashboard</a>
            <a href="admin-panel.html">User Management</a>
            <a href="admin-auctions.html">Auctions</a>
            <a href="admin-products.html">Products</a>
            <a href="admin-logs.html" class="active">Activity Logs</a>
        </div>
        
        <div class="card">
            <h2>Activity Logs</h2>
            <div class="filter-section">
                <div>
                    <label for="user-filter">User:</label>
                    <select id="user-filter">
                        <option value="">All Users</option>
                    </select>
                </div>
                <div>
                    <label for="action-type-filter">Action Type:</label>
                    <select id="action-type-filter">
                        <option value="">All Actions</option>
                        <option value="auth">Authentication</option>
                        <option value="crud">CRUD Operations</option>
                        <option value="system">System</option>
                    </select>
                </div>
                <div>
                    <label for="entity-type-filter">Entity Type:</label>
                    <select id="entity-type-filter">
                        <option value="">All Entities</option>
                        <option value="user">User</option>
                        <option value="product">Product</option>
                        <option value="auction">Auction</option>
                        <option value="bid">Bid</option>
                    </select>
                </div>
                <div>
                    <label for="start-date">From:</label>
                    <input type="date" id="start-date">
                </div>
                <div>
                    <label for="end-date">To:</label>
                    <input type="date" id="end-date">
                </div>
                <button id="filter-button" class="button">Apply Filter</button>
            </div>
            
            <div id="logs-loading" class="loading">Loading activity logs...</div>
            
            <div id="logs-table-container" class="hidden">
                <table class="table" id="logs-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Action</th>
                            <th>Type</th>
                            <th>Entity</th>
                            <th>Description</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="logs-table-body">
                        <!-- Log rows will be added here -->
                    </tbody>
                </table>
                <div class="pagination" id="logs-pagination">
                    <!-- Pagination buttons will be added here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Log Details Modal -->
    <div id="log-details-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Log Details</h2>
            <div id="log-details-content">
                <!-- Log details will be added here -->
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('adminToken');
        let currentUser = null;
        let currentPage = 1;
        let totalPages = 1;
        let logsPerPage = 15;
        let currentFilters = {
            user_id: '',
            action_type: '',
            entity_type: '',
            start_date: '',
            end_date: ''
        };
        
        // Set default date range (last 30 days)
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        
        document.getElementById('start-date').value = formatDate(thirtyDaysAgo);
        document.getElementById('end-date').value = formatDate(today);
        
        // Format date as YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // Check if admin is already logged in
        if (authToken) {
            fetchCurrentUser();
        } else {
            document.getElementById('login-section').classList.remove('hidden');
        }
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const errorElement = document.getElementById('login-error');
            errorElement.classList.add('hidden');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('email').value,
                        password: document.getElementById('password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Check if user is admin
                    if (data.data.role !== 'admin') {
                        errorElement.textContent = 'Access denied. Admin privileges required.';
                        errorElement.classList.remove('hidden');
                        return;
                    }
                    
                    // Save token and user data
                    localStorage.setItem('adminToken', data.token);
                    authToken = data.token;
                    currentUser = data.data;
                    
                    // Show logs section, hide login section
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('logs-section').classList.remove('hidden');
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Load users for filter
                    loadUsers();
                    
                    // Load logs
                    loadLogs();
                } else {
                    errorElement.textContent = data.message || 'Login failed. Please check your credentials.';
                    errorElement.classList.remove('hidden');
                }
            } catch (error) {
                errorElement.textContent = 'An error occurred. Please try again.';
                errorElement.classList.remove('hidden');
                console.error('Login error:', error);
            }
        });
        
        // Logout button
        document.getElementById('logout-button').addEventListener('click', function() {
            // Clear token and user data
            localStorage.removeItem('adminToken');
            authToken = null;
            currentUser = null;
            
            // Show login section, hide logs section
            document.getElementById('login-section').classList.remove('hidden');
            document.getElementById('logs-section').classList.add('hidden');
            
            // Clear login form
            document.getElementById('login-form').reset();
            document.getElementById('login-error').classList.add('hidden');
        });
        
        // Filter button
        document.getElementById('filter-button').addEventListener('click', function() {
            currentFilters = {
                user_id: document.getElementById('user-filter').value,
                action_type: document.getElementById('action-type-filter').value,
                entity_type: document.getElementById('entity-type-filter').value,
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value
            };
            
            loadLogs(1);
        });
        
        // Fetch current user
        async function fetchCurrentUser() {
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.data;
                    
                    // Check if user is admin
                    if (currentUser.role !== 'admin') {
                        // Not an admin, show login section
                        document.getElementById('login-section').classList.remove('hidden');
                        return;
                    }
                    
                    // Show logs section, hide login section
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('logs-section').classList.remove('hidden');
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Load users for filter
                    loadUsers();
                    
                    // Load logs
                    loadLogs();
                } else {
                    // Token might be invalid, show login section
                    document.getElementById('login-section').classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error fetching current user:', error);
                document.getElementById('login-section').classList.remove('hidden');
            }
        }
        
        // Load users for filter
        async function loadUsers() {
            try {
                const response = await fetch('/api/admin/users', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const userFilter = document.getElementById('user-filter');
                    
                    // Clear existing options except the first one
                    while (userFilter.options.length > 1) {
                        userFilter.remove(1);
                    }
                    
                    // Add user options
                    data.data.forEach(user => {
                        const option = document.createElement('option');
                        option.value = user.id;
                        option.textContent = `${user.name} (${user.email})`;
                        userFilter.appendChild(option);
                    });
                } else {
                    console.error('Error loading users:', await response.json());
                }
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }
        
        // Load logs
        async function loadLogs(page = 1) {
            currentPage = page;
            
            document.getElementById('logs-loading').classList.remove('hidden');
            document.getElementById('logs-table-container').classList.add('hidden');
            
            try {
                let url = `/api/admin/activity-logs?page=${page}&per_page=${logsPerPage}`;
                
                // Add filters
                if (currentFilters.user_id) {
                    url += `&user_id=${currentFilters.user_id}`;
                }
                
                if (currentFilters.action_type) {
                    url += `&action_type=${currentFilters.action_type}`;
                }
                
                if (currentFilters.entity_type) {
                    url += `&entity_type=${currentFilters.entity_type}`;
                }
                
                if (currentFilters.start_date && currentFilters.end_date) {
                    url += `&start_date=${currentFilters.start_date}&end_date=${currentFilters.end_date}`;
                }
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Update pagination
                    totalPages = Math.ceil(data.meta.total / data.meta.per_page);
                    updatePagination();
                    
                    // Render logs
                    renderLogs(data.data);
                    
                    document.getElementById('logs-loading').classList.add('hidden');
                    document.getElementById('logs-table-container').classList.remove('hidden');
                } else {
                    console.error('Error loading logs:', await response.json());
                }
            } catch (error) {
                console.error('Error loading logs:', error);
            }
        }
        
        // Render logs
        function renderLogs(logs) {
            const tableBody = document.getElementById('logs-table-body');
            tableBody.innerHTML = '';
            
            if (logs.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="8">No logs found</td>';
                tableBody.appendChild(row);
                return;
            }
            
            logs.forEach(log => {
                const row = document.createElement('tr');
                
                const badgeClass = `badge badge-${log.action_type}`;
                
                row.innerHTML = `
                    <td>${log.id}</td>
                    <td>${log.user ? log.user.name : 'System'}</td>
                    <td>${log.action}</td>
                    <td><span class="${badgeClass}">${log.action_type}</span></td>
                    <td>${log.entity_type ? `${log.entity_type} #${log.entity_id}` : 'N/A'}</td>
                    <td>${log.description}</td>
                    <td>${new Date(log.created_at).toLocaleString()}</td>
                    <td>
                        <button class="button view-log" data-id="${log.id}">View</button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to view buttons
            document.querySelectorAll('.view-log').forEach(button => {
                button.addEventListener('click', function() {
                    viewLogDetails(this.dataset.id, logs);
                });
            });
        }
        
        // View log details
        function viewLogDetails(logId, logs) {
            const log = logs.find(l => l.id == logId);
            
            if (!log) {
                console.error('Log not found:', logId);
                return;
            }
            
            const badgeClass = `badge badge-${log.action_type}`;
            
            let oldValuesHtml = '';
            if (log.old_values) {
                oldValuesHtml = `
                    <h3>Old Values</h3>
                    <pre>${JSON.stringify(log.old_values, null, 2)}</pre>
                `;
            }
            
            let newValuesHtml = '';
            if (log.new_values) {
                newValuesHtml = `
                    <h3>New Values</h3>
                    <pre>${JSON.stringify(log.new_values, null, 2)}</pre>
                `;
            }
            
            document.getElementById('log-details-content').innerHTML = `
                <div>
                    <p><strong>ID:</strong> ${log.id}</p>
                    <p><strong>User:</strong> ${log.user ? log.user.name : 'System'}</p>
                    <p><strong>Action:</strong> ${log.action}</p>
                    <p><strong>Type:</strong> <span class="${badgeClass}">${log.action_type}</span></p>
                    <p><strong>Entity:</strong> ${log.entity_type ? `${log.entity_type} #${log.entity_id}` : 'N/A'}</p>
                    <p><strong>Description:</strong> ${log.description}</p>
                    <p><strong>IP Address:</strong> ${log.ip_address || 'N/A'}</p>
                    <p><strong>User Agent:</strong> ${log.user_agent || 'N/A'}</p>
                    <p><strong>Date:</strong> ${new Date(log.created_at).toLocaleString()}</p>
                    ${oldValuesHtml}
                    ${newValuesHtml}
                </div>
            `;
            
            // Show the modal
            document.getElementById('log-details-modal').style.display = 'block';
        }
        
        // Update pagination
        function updatePagination() {
            const paginationElement = document.getElementById('logs-pagination');
            paginationElement.innerHTML = '';
            
            // Previous button
            const prevButton = document.createElement('button');
            prevButton.textContent = 'Previous';
            prevButton.disabled = currentPage === 1;
            prevButton.addEventListener('click', function() {
                if (currentPage > 1) {
                    loadLogs(currentPage - 1);
                }
            });
            paginationElement.appendChild(prevButton);
            
            // Page buttons
            for (let i = 1; i <= totalPages; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.classList.toggle('active', i === currentPage);
                pageButton.addEventListener('click', function() {
                    loadLogs(i);
                });
                paginationElement.appendChild(pageButton);
            }
            
            // Next button
            const nextButton = document.createElement('button');
            nextButton.textContent = 'Next';
            nextButton.disabled = currentPage === totalPages;
            nextButton.addEventListener('click', function() {
                if (currentPage < totalPages) {
                    loadLogs(currentPage + 1);
                }
            });
            paginationElement.appendChild(nextButton);
        }
        
        // Close modals when clicking on the close button or outside the modal
        document.querySelectorAll('.modal .close').forEach(closeButton => {
            closeButton.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });
        
        window.addEventListener('click', function(event) {
            document.querySelectorAll('.modal').forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
