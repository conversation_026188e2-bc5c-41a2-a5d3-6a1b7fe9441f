<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .endpoint {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .endpoint h2 {
            margin-top: 0;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: none;
            margin-left: 10px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .stats {
            margin-top: 10px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 60px;
            padding: 5px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>Fixed API Test</h1>
    
    <div class="endpoint">
        <h2>Get Categories</h2>
        <button onclick="testEndpoint('/api/categories')">Test</button>
        <span class="loading" id="categories-loading">Loading...</span>
        <div id="categories-stats" class="stats"></div>
        <div id="categories-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Category by ID</h2>
        <div>
            <label for="category-id">Category ID:</label>
            <input type="number" id="category-id" value="1" min="1">
            <button onclick="testEndpoint(`/api/categories/${document.getElementById('category-id').value}`)">Test</button>
        </div>
        <span class="loading" id="category-loading">Loading...</span>
        <div id="category-stats" class="stats"></div>
        <div id="category-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Products</h2>
        <button onclick="testEndpoint('/api/products')">Test</button>
        <span class="loading" id="products-loading">Loading...</span>
        <div id="products-stats" class="stats"></div>
        <div id="products-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Product by ID</h2>
        <div>
            <label for="product-id">Product ID:</label>
            <input type="number" id="product-id" value="1" min="1">
            <button onclick="testEndpoint(`/api/products/${document.getElementById('product-id').value}`)">Test</button>
        </div>
        <span class="loading" id="product-loading">Loading...</span>
        <div id="product-stats" class="stats"></div>
        <div id="product-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Auctions</h2>
        <button onclick="testEndpoint('/api/auctions')">Test</button>
        <span class="loading" id="auctions-loading">Loading...</span>
        <div id="auctions-stats" class="stats"></div>
        <div id="auctions-result"></div>
    </div>
    
    <div class="endpoint">
        <h2>Get Auction by ID</h2>
        <div>
            <label for="auction-id">Auction ID:</label>
            <input type="number" id="auction-id" value="1" min="1">
            <button onclick="testEndpoint(`/api/auctions/${document.getElementById('auction-id').value}`)">Test</button>
        </div>
        <span class="loading" id="auction-loading">Loading...</span>
        <div id="auction-stats" class="stats"></div>
        <div id="auction-result"></div>
    </div>
    
    <script>
        async function testEndpoint(url) {
            const endpointParts = url.split('/');
            const endpointName = endpointParts[endpointParts.length - 1];
            const isDetail = !isNaN(parseInt(endpointName));
            const baseEndpoint = isDetail ? endpointParts[endpointParts.length - 2] : endpointName;
            
            const loadingElement = document.getElementById(`${isDetail ? baseEndpoint : baseEndpoint + 's'}-loading`);
            const resultElement = document.getElementById(`${isDetail ? baseEndpoint : baseEndpoint + 's'}-result`);
            const statsElement = document.getElementById(`${isDetail ? baseEndpoint : baseEndpoint + 's'}-stats`);
            
            loadingElement.style.display = 'inline';
            resultElement.innerHTML = '';
            statsElement.innerHTML = '';
            
            try {
                const startTime = performance.now();
                const response = await fetch(url);
                const endTime = performance.now();
                const responseTime = (endTime - startTime).toFixed(2);
                
                const data = await response.json();
                const jsonString = JSON.stringify(data);
                const responseSize = (new TextEncoder().encode(jsonString).length / 1024).toFixed(2);
                
                // Calculate stats
                statsElement.innerHTML = `
                    <p>Response Time: ${responseTime}ms</p>
                    <p>Response Size: ${responseSize} KB</p>
                    <p>Status: ${response.status} ${response.statusText}</p>
                `;
                
                // Show a preview of the data
                let resultHtml = '';
                if (data.data && Array.isArray(data.data)) {
                    resultHtml += `<p>Total Items: ${data.data.length}</p>`;
                    if (data.data.length > 0) {
                        resultHtml += `<p>First Item Preview:</p>`;
                        resultHtml += `<pre>${JSON.stringify(data.data[0], null, 2)}</pre>`;
                    }
                } else {
                    resultHtml += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
                
                resultElement.innerHTML = resultHtml;
                resultElement.classList.add('success');
                resultElement.classList.remove('error');
            } catch (error) {
                resultElement.innerHTML = `<p>Error: ${error.message}</p>`;
                resultElement.classList.add('error');
                resultElement.classList.remove('success');
            } finally {
                loadingElement.style.display = 'none';
            }
        }
    </script>
</body>
</html>
