<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        .button {
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button.delete {
            background-color: #f44336;
        }
        .button.delete:hover {
            background-color: #d32f2f;
        }
        .button.edit {
            background-color: #2196F3;
        }
        .button.edit:hover {
            background-color: #0b7dda;
        }
        .button.view {
            background-color: #ff9800;
        }
        .button.view:hover {
            background-color: #e68a00;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 60%;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .filter-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .filter-section select, .filter-section input {
            width: auto;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            margin: 0 5px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            background-color: #f5f5f5;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        .role-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .role-buyer {
            background-color: #4CAF50;
        }
        .role-vendor {
            background-color: #2196F3;
        }
        .role-admin {
            background-color: #f44336;
        }
        .hidden {
            display: none;
        }
        .user-details {
            margin-top: 20px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #f5f5f5;
            border-color: #ddd;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .error {
            color: #f44336;
            margin-top: 10px;
        }
        .success {
            color: #4CAF50;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Admin Panel</h1>
        <div>
            <span id="user-name"></span>
            <button id="logout-button" class="button delete">Logout</button>
        </div>
    </div>
    
    <div id="login-section" class="card">
        <h2>Admin Login</h2>
        <form id="login-form">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <button type="submit" class="button">Login</button>
            <div id="login-error" class="error hidden"></div>
        </form>
    </div>
    
    <div id="admin-section" class="hidden">
        <div class="tabs">
            <div class="tab active" data-tab="users">Users</div>
            <div class="tab" data-tab="auctions">Auctions</div>
            <div class="tab" data-tab="products">Products</div>
        </div>
        
        <div id="users-tab" class="tab-content active">
            <div class="card">
                <h2>User Management</h2>
                <div class="filter-section">
                    <div>
                        <label for="role-filter">Filter by Role:</label>
                        <select id="role-filter">
                            <option value="">All Roles</option>
                            <option value="buyer">Buyers</option>
                            <option value="vendor">Vendors</option>
                            <option value="admin">Admins</option>
                        </select>
                    </div>
                    <div>
                        <label for="search-input">Search:</label>
                        <input type="text" id="search-input" placeholder="Search by name or email">
                    </div>
                    <button id="search-button" class="button">Search</button>
                </div>
                
                <div id="users-loading" class="loading">Loading users...</div>
                <div id="users-table-container" class="hidden">
                    <table id="users-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body">
                            <!-- User rows will be added here -->
                        </tbody>
                    </table>
                    <div class="pagination" id="users-pagination">
                        <!-- Pagination buttons will be added here -->
                    </div>
                </div>
            </div>
        </div>
        
        <div id="auctions-tab" class="tab-content">
            <div class="card">
                <h2>Auction Management</h2>
                <p>Auction management features will be added here.</p>
            </div>
        </div>
        
        <div id="products-tab" class="tab-content">
            <div class="card">
                <h2>Product Management</h2>
                <p>Product management features will be added here.</p>
            </div>
        </div>
    </div>
    
    <!-- User Edit Modal -->
    <div id="edit-user-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Edit User</h2>
            <form id="edit-user-form">
                <input type="hidden" id="edit-user-id">
                <div class="form-group">
                    <label for="edit-name">Name</label>
                    <input type="text" id="edit-name" required>
                </div>
                <div class="form-group">
                    <label for="edit-email">Email</label>
                    <input type="email" id="edit-email" required>
                </div>
                <div class="form-group">
                    <label for="edit-role">Role</label>
                    <select id="edit-role" required>
                        <option value="buyer">Buyer</option>
                        <option value="vendor">Vendor</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-password">New Password (leave blank to keep current)</label>
                    <input type="password" id="edit-password">
                </div>
                <button type="submit" class="button">Save Changes</button>
                <div id="edit-user-error" class="error hidden"></div>
                <div id="edit-user-success" class="success hidden"></div>
            </form>
        </div>
    </div>
    
    <!-- User View Modal -->
    <div id="view-user-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>User Details</h2>
            <div id="user-details-content">
                <!-- User details will be added here -->
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('adminToken');
        let currentUser = null;
        let currentPage = 1;
        let totalPages = 1;
        let usersPerPage = 10;
        let currentRole = '';
        let currentSearch = '';
        
        // Check if admin is already logged in
        if (authToken) {
            fetchCurrentUser();
        } else {
            document.getElementById('login-section').classList.remove('hidden');
        }
        
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Hide all tab content
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                // Show the corresponding tab content
                document.getElementById(this.dataset.tab + '-tab').classList.add('active');
            });
        });
        
        // Login form submission
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const errorElement = document.getElementById('login-error');
            errorElement.classList.add('hidden');
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: document.getElementById('email').value,
                        password: document.getElementById('password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Check if user is admin
                    if (data.data.role !== 'admin') {
                        errorElement.textContent = 'Access denied. Admin privileges required.';
                        errorElement.classList.remove('hidden');
                        return;
                    }
                    
                    // Save token and user data
                    localStorage.setItem('adminToken', data.token);
                    authToken = data.token;
                    currentUser = data.data;
                    
                    // Show admin section, hide login section
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('admin-section').classList.remove('hidden');
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Load users
                    loadUsers();
                } else {
                    errorElement.textContent = data.message || 'Login failed. Please check your credentials.';
                    errorElement.classList.remove('hidden');
                }
            } catch (error) {
                errorElement.textContent = 'An error occurred. Please try again.';
                errorElement.classList.remove('hidden');
                console.error('Login error:', error);
            }
        });
        
        // Logout button
        document.getElementById('logout-button').addEventListener('click', function() {
            // Clear token and user data
            localStorage.removeItem('adminToken');
            authToken = null;
            currentUser = null;
            
            // Show login section, hide admin section
            document.getElementById('login-section').classList.remove('hidden');
            document.getElementById('admin-section').classList.add('hidden');
            
            // Clear login form
            document.getElementById('login-form').reset();
            document.getElementById('login-error').classList.add('hidden');
        });
        
        // Fetch current user
        async function fetchCurrentUser() {
            try {
                const response = await fetch('/api/user', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data.data;
                    
                    // Check if user is admin
                    if (currentUser.role !== 'admin') {
                        // Not an admin, show login section
                        document.getElementById('login-section').classList.remove('hidden');
                        return;
                    }
                    
                    // Show admin section, hide login section
                    document.getElementById('login-section').classList.add('hidden');
                    document.getElementById('admin-section').classList.remove('hidden');
                    
                    // Update user name in header
                    document.getElementById('user-name').textContent = currentUser.name;
                    
                    // Load users
                    loadUsers();
                } else {
                    // Token might be invalid, show login section
                    document.getElementById('login-section').classList.remove('hidden');
                }
            } catch (error) {
                console.error('Error fetching current user:', error);
                document.getElementById('login-section').classList.remove('hidden');
            }
        }
        
        // Load users
        async function loadUsers(page = 1, role = '', search = '') {
            currentPage = page;
            currentRole = role;
            currentSearch = search;
            
            document.getElementById('users-loading').classList.remove('hidden');
            document.getElementById('users-table-container').classList.add('hidden');
            
            try {
                let url = `/api/admin/users?page=${page}&per_page=${usersPerPage}`;
                
                if (role) {
                    url += `&role=${role}`;
                }
                
                // TODO: Add search functionality when backend supports it
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Update pagination
                    totalPages = Math.ceil(data.meta.total / data.meta.per_page);
                    updatePagination();
                    
                    // Render users
                    renderUsers(data.data);
                    
                    document.getElementById('users-loading').classList.add('hidden');
                    document.getElementById('users-table-container').classList.remove('hidden');
                } else {
                    console.error('Error loading users:', await response.json());
                }
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }
        
        // Render users
        function renderUsers(users) {
            const tableBody = document.getElementById('users-table-body');
            tableBody.innerHTML = '';
            
            users.forEach(user => {
                const row = document.createElement('tr');
                
                const roleBadgeClass = `role-badge role-${user.role}`;
                
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td><span class="${roleBadgeClass}">${user.role.toUpperCase()}</span></td>
                    <td>${new Date(user.created_at).toLocaleDateString()}</td>
                    <td>
                        <button class="button view" data-id="${user.id}">View</button>
                        <button class="button edit" data-id="${user.id}">Edit</button>
                        <button class="button delete" data-id="${user.id}">Delete</button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to buttons
            document.querySelectorAll('.button.view').forEach(button => {
                button.addEventListener('click', function() {
                    viewUser(this.dataset.id);
                });
            });
            
            document.querySelectorAll('.button.edit').forEach(button => {
                button.addEventListener('click', function() {
                    editUser(this.dataset.id);
                });
            });
            
            document.querySelectorAll('.button.delete').forEach(button => {
                button.addEventListener('click', function() {
                    deleteUser(this.dataset.id);
                });
            });
        }
        
        // Update pagination
        function updatePagination() {
            const paginationElement = document.getElementById('users-pagination');
            paginationElement.innerHTML = '';
            
            // Previous button
            const prevButton = document.createElement('button');
            prevButton.textContent = 'Previous';
            prevButton.disabled = currentPage === 1;
            prevButton.addEventListener('click', function() {
                if (currentPage > 1) {
                    loadUsers(currentPage - 1, currentRole, currentSearch);
                }
            });
            paginationElement.appendChild(prevButton);
            
            // Page buttons
            for (let i = 1; i <= totalPages; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.classList.toggle('active', i === currentPage);
                pageButton.addEventListener('click', function() {
                    loadUsers(i, currentRole, currentSearch);
                });
                paginationElement.appendChild(pageButton);
            }
            
            // Next button
            const nextButton = document.createElement('button');
            nextButton.textContent = 'Next';
            nextButton.disabled = currentPage === totalPages;
            nextButton.addEventListener('click', function() {
                if (currentPage < totalPages) {
                    loadUsers(currentPage + 1, currentRole, currentSearch);
                }
            });
            paginationElement.appendChild(nextButton);
        }
        
        // View user
        async function viewUser(userId) {
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const user = data.data;
                    
                    const roleBadgeClass = `role-badge role-${user.role}`;
                    
                    let vendorInfo = '';
                    if (user.vendor) {
                        vendorInfo = `
                            <h3>Vendor Information</h3>
                            <p><strong>Vendor Name:</strong> ${user.vendor.name}</p>
                            <p><strong>Vendor Email:</strong> ${user.vendor.email}</p>
                            <p><strong>Status:</strong> ${user.vendor.is_active ? 'Active' : 'Inactive'}</p>
                        `;
                    }
                    
                    let walletInfo = '';
                    if (user.wallet) {
                        walletInfo = `
                            <h3>Wallet Information</h3>
                            <p><strong>Balance:</strong> $${user.wallet.balance}</p>
                            <p><strong>Available Balance:</strong> $${user.wallet.available_balance}</p>
                            <p><strong>Held Balance:</strong> $${user.wallet.held_balance}</p>
                        `;
                    }
                    
                    document.getElementById('user-details-content').innerHTML = `
                        <div>
                            <p><strong>ID:</strong> ${user.id}</p>
                            <p><strong>Name:</strong> ${user.name}</p>
                            <p><strong>Email:</strong> ${user.email}</p>
                            <p><strong>Role:</strong> <span class="${roleBadgeClass}">${user.role.toUpperCase()}</span></p>
                            <p><strong>Created:</strong> ${new Date(user.created_at).toLocaleString()}</p>
                            <p><strong>Updated:</strong> ${new Date(user.updated_at).toLocaleString()}</p>
                            ${vendorInfo}
                            ${walletInfo}
                        </div>
                    `;
                    
                    // Show the modal
                    document.getElementById('view-user-modal').style.display = 'block';
                } else {
                    console.error('Error viewing user:', await response.json());
                }
            } catch (error) {
                console.error('Error viewing user:', error);
            }
        }
        
        // Edit user
        async function editUser(userId) {
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const user = data.data;
                    
                    // Populate form fields
                    document.getElementById('edit-user-id').value = user.id;
                    document.getElementById('edit-name').value = user.name;
                    document.getElementById('edit-email').value = user.email;
                    document.getElementById('edit-role').value = user.role;
                    document.getElementById('edit-password').value = '';
                    
                    // Clear messages
                    document.getElementById('edit-user-error').classList.add('hidden');
                    document.getElementById('edit-user-success').classList.add('hidden');
                    
                    // Show the modal
                    document.getElementById('edit-user-modal').style.display = 'block';
                } else {
                    console.error('Error editing user:', await response.json());
                }
            } catch (error) {
                console.error('Error editing user:', error);
            }
        }
        
        // Delete user
        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    alert('User deleted successfully.');
                    loadUsers(currentPage, currentRole, currentSearch);
                } else {
                    const data = await response.json();
                    alert(`Error deleting user: ${data.message}`);
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                alert('An error occurred while deleting the user.');
            }
        }
        
        // Edit user form submission
        document.getElementById('edit-user-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const userId = document.getElementById('edit-user-id').value;
            const errorElement = document.getElementById('edit-user-error');
            const successElement = document.getElementById('edit-user-success');
            
            errorElement.classList.add('hidden');
            successElement.classList.add('hidden');
            
            const data = {
                name: document.getElementById('edit-name').value,
                email: document.getElementById('edit-email').value,
                role: document.getElementById('edit-role').value
            };
            
            // Add password if provided
            const password = document.getElementById('edit-password').value;
            if (password) {
                data.password = password;
            }
            
            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(data)
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    successElement.textContent = 'User updated successfully.';
                    successElement.classList.remove('hidden');
                    
                    // Reload users after a short delay
                    setTimeout(() => {
                        loadUsers(currentPage, currentRole, currentSearch);
                    }, 1000);
                } else {
                    errorElement.textContent = responseData.message || 'Error updating user.';
                    errorElement.classList.remove('hidden');
                }
            } catch (error) {
                errorElement.textContent = 'An error occurred. Please try again.';
                errorElement.classList.remove('hidden');
                console.error('Error updating user:', error);
            }
        });
        
        // Role filter change
        document.getElementById('role-filter').addEventListener('change', function() {
            loadUsers(1, this.value, currentSearch);
        });
        
        // Search button click
        document.getElementById('search-button').addEventListener('click', function() {
            const searchValue = document.getElementById('search-input').value.trim();
            loadUsers(1, currentRole, searchValue);
        });
        
        // Close modals when clicking on the close button or outside the modal
        document.querySelectorAll('.modal .close').forEach(closeButton => {
            closeButton.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });
        
        window.addEventListener('click', function(event) {
            document.querySelectorAll('.modal').forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
