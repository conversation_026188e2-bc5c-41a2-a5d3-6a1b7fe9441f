{"info": {"_postman_id": "azstock-api-collection", "name": "AZStock Auction Platform API", "description": "Complete API collection for AZStock auction platform with auction-based structure", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"roles\": [\"buyer\", \"vendor\"]\n}"}, "url": {"raw": "{{base_url}}/api/register", "host": ["{{base_url}}"], "path": ["api", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('auth_token', response.token);", "    pm.environment.set('user_id', response.user.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}}}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/logout", "host": ["{{base_url}}"], "path": ["api", "logout"]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/user", "host": ["{{base_url}}"], "path": ["api", "user"]}}}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/categories?has_auctions=true&search=electronics", "host": ["{{base_url}}"], "path": ["api", "categories"], "query": [{"key": "has_auctions", "value": "true"}, {"key": "search", "value": "electronics"}]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/categories/1", "host": ["{{base_url}}"], "path": ["api", "categories", "1"]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Electronics\"\n}"}, "url": {"raw": "{{base_url}}/api/categories", "host": ["{{base_url}}"], "path": ["api", "categories"]}}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Electronics\"\n}"}, "url": {"raw": "{{base_url}}/api/categories/1", "host": ["{{base_url}}"], "path": ["api", "categories", "1"]}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/categories/1", "host": ["{{base_url}}"], "path": ["api", "categories", "1"]}}}]}, {"name": "Auctions", "item": [{"name": "Get All Auctions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auctions?status=active&category_id=1&ending_soon=true", "host": ["{{base_url}}"], "path": ["api", "auctions"], "query": [{"key": "status", "value": "active"}, {"key": "category_id", "value": "1"}, {"key": "ending_soon", "value": "true"}]}}}, {"name": "Get Auction by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auctions/1", "host": ["{{base_url}}"], "path": ["api", "auctions", "1"]}}}, {"name": "Create Auction with Multiple Items", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Electronics Bundle Auction\",\n    \"description\": \"High-quality electronics including iPhone and MacBook\",\n    \"category_id\": 1,\n    \"auction_type\": \"online\",\n    \"start_time\": \"2024-12-18T10:00:00Z\",\n    \"end_time\": \"2024-12-25T10:00:00Z\",\n    \"starting_price\": 500.00,\n    \"reserve_price\": 1000.00,\n    \"items\": [\n        {\n            \"item_name\": \"iPhone 15 Pro Max\",\n            \"item_description\": \"Brand new iPhone 15 Pro Max 256GB in Space Black\",\n            \"images\": [\n                \"https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com/iphone1.jpg\",\n                \"https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com/iphone2.jpg\"\n            ]\n        },\n        {\n            \"item_name\": \"MacBook Pro 16-inch\",\n            \"item_description\": \"2024 MacBook Pro 16-inch with M4 Pro chip\",\n            \"images\": [\n                \"https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com/macbook1.jpg\"\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/auctions", "host": ["{{base_url}}"], "path": ["api", "auctions"]}}}, {"name": "Get My Auctions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/my-auctions?status=active", "host": ["{{base_url}}"], "path": ["api", "my-auctions"], "query": [{"key": "status", "value": "active"}]}}}, {"name": "Update Auction", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Auction Title\",\n    \"description\": \"Updated description\",\n    \"end_time\": \"2024-12-30T10:00:00Z\",\n    \"reserve_price\": 1200.00\n}"}, "url": {"raw": "{{base_url}}/api/auctions/1", "host": ["{{base_url}}"], "path": ["api", "auctions", "1"]}}}, {"name": "Delete Auction", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/auctions/1", "host": ["{{base_url}}"], "path": ["api", "auctions", "1"]}}}]}, {"name": "Watchlist", "item": [{"name": "Get User Watchlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/watchlist", "host": ["{{base_url}}"], "path": ["api", "watchlist"]}}}, {"name": "Add Auction to Watchlist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"auction_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/watchlist", "host": ["{{base_url}}"], "path": ["api", "watchlist"]}}}, {"name": "Remove from Watchlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/watchlist/1", "host": ["{{base_url}}"], "path": ["api", "watchlist", "1"]}}}, {"name": "Check Watchlist Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/watchlist/check/1", "host": ["{{base_url}}"], "path": ["api", "watchlist", "check", "1"]}}}, {"name": "Get Watchlist Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/watchlist/stats", "host": ["{{base_url}}"], "path": ["api", "watchlist", "stats"]}}}]}, {"name": "Vendor Statistics", "item": [{"name": "Get Comprehensive Vendor Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/vendor/stats", "host": ["{{base_url}}"], "path": ["api", "vendor", "stats"]}}}, {"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/vendor/dashboard-stats", "host": ["{{base_url}}"], "path": ["api", "vendor", "dashboard-stats"]}}}]}, {"name": "Bids", "item": [{"name": "Place Bid", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"bid_amount\": 750.00\n}"}, "url": {"raw": "{{base_url}}/api/auctions/1/bids", "host": ["{{base_url}}"], "path": ["api", "auctions", "1", "bids"]}}}, {"name": "Get My Bids", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/my-bids?status=active", "host": ["{{base_url}}"], "path": ["api", "my-bids"], "query": [{"key": "status", "value": "active"}]}}}]}, {"name": "Images", "item": [{"name": "Upload Images (Vercel Blob)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"images\": [\n        {\n            \"url\": \"https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com/image1.jpg\",\n            \"original_name\": \"product-image.jpg\",\n            \"size\": 1024000\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/images/upload", "host": ["{{base_url}}"], "path": ["api", "images", "upload"]}}}, {"name": "Upload Auction Item Images", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"auction_item_id\": 1,\n    \"images\": [\n        {\n            \"url\": \"https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com/item1.jpg\",\n            \"original_name\": \"item-image.jpg\",\n            \"size\": 1024000\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/images/auction-items", "host": ["{{base_url}}"], "path": ["api", "images", "auction-items"]}}}, {"name": "Get Auction Item Images", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auction-items/1/images", "host": ["{{base_url}}"], "path": ["api", "auction-items", "1", "images"]}}}, {"name": "Delete Image", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/images/1", "host": ["{{base_url}}"], "path": ["api", "images", "1"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}]}