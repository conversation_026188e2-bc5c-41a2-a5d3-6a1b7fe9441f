<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Product;
use App\Models\Auction;
use App\Models\Bid;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class FilteringPaginationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vendor;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->user = User::factory()->create(['role' => 'buyer']);
        $this->vendor = User::factory()->create(['role' => 'vendor']);
        $this->admin = User::factory()->create(['role' => 'admin']);
        
        // Create vendor profile
        $this->vendor->vendor()->create([
            'name' => 'Test Vendor',
            'slug' => 'test-vendor',
            'email' => $this->vendor->email,
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_filters_products_by_category()
    {
        $category1 = Category::factory()->create(['name' => 'Electronics']);
        $category2 = Category::factory()->create(['name' => 'Books']);
        
        Product::factory()->create(['category_id' => $category1->id, 'vendor_id' => $this->vendor->vendor->id]);
        Product::factory()->create(['category_id' => $category2->id, 'vendor_id' => $this->vendor->vendor->id]);

        $response = $this->getJson("/api/products?category_id={$category1->id}");

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.category.id', $category1->id);
    }

    /** @test */
    public function it_searches_products_by_name_and_description()
    {
        Product::factory()->create([
            'name' => 'iPhone 13',
            'description' => 'Latest smartphone',
            'vendor_id' => $this->vendor->vendor->id
        ]);
        Product::factory()->create([
            'name' => 'Samsung Galaxy',
            'description' => 'Android phone',
            'vendor_id' => $this->vendor->vendor->id
        ]);

        $response = $this->getJson('/api/products?search=iPhone');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.name', 'iPhone 13');
    }

    /** @test */
    public function it_filters_products_by_auction_existence()
    {
        $productWithAuction = Product::factory()->create(['vendor_id' => $this->vendor->vendor->id]);
        $productWithoutAuction = Product::factory()->create(['vendor_id' => $this->vendor->vendor->id]);
        
        Auction::factory()->create([
            'product_id' => $productWithAuction->id,
            'vendor_id' => $this->vendor->vendor->id,
            'status' => 'active'
        ]);

        $response = $this->getJson('/api/products?has_auction=true');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.id', $productWithAuction->id);
    }

    /** @test */
    public function it_paginates_products_correctly()
    {
        Product::factory()->count(25)->create(['vendor_id' => $this->vendor->vendor->id]);

        $response = $this->getJson('/api/products?per_page=10');

        $response->assertStatus(200)
                ->assertJsonCount(10, 'data')
                ->assertJsonStructure([
                    'data',
                    'links',
                    'meta' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total'
                    ]
                ]);
    }

    /** @test */
    public function it_filters_auctions_by_status()
    {
        $activeAuction = Auction::factory()->create([
            'vendor_id' => $this->vendor->vendor->id,
            'status' => 'active'
        ]);
        $endedAuction = Auction::factory()->create([
            'vendor_id' => $this->vendor->vendor->id,
            'status' => 'ended'
        ]);

        $response = $this->getJson('/api/auctions?status=active');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.status', 'active');
    }

    /** @test */
    public function it_filters_auctions_ending_soon()
    {
        $endingSoon = Auction::factory()->create([
            'vendor_id' => $this->vendor->vendor->id,
            'status' => 'active',
            'end_time' => now()->addHours(12)
        ]);
        $endingLater = Auction::factory()->create([
            'vendor_id' => $this->vendor->vendor->id,
            'status' => 'active',
            'end_time' => now()->addDays(2)
        ]);

        $response = $this->getJson('/api/auctions?ending_soon=true');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.id', $endingSoon->id);
    }

    /** @test */
    public function it_filters_categories_with_products()
    {
        $categoryWithProducts = Category::factory()->create();
        $categoryWithoutProducts = Category::factory()->create();
        
        Product::factory()->create([
            'category_id' => $categoryWithProducts->id,
            'vendor_id' => $this->vendor->vendor->id
        ]);

        $response = $this->getJson('/api/categories?has_products=true');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.id', $categoryWithProducts->id);
    }

    /** @test */
    public function admin_can_filter_users_by_role()
    {
        $this->actingAs($this->admin);

        $response = $this->getJson('/api/admin/users?role=vendor');

        $response->assertStatus(200);
        // All returned users should have vendor role
        $users = $response->json('data');
        foreach ($users as $user) {
            $this->assertEquals('vendor', $user['role']);
        }
    }

    /** @test */
    public function admin_can_search_users_by_name_and_email()
    {
        $this->actingAs($this->admin);
        
        $testUser = User::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'role' => 'buyer'
        ]);

        $response = $this->getJson('/api/admin/users?search=John');

        $response->assertStatus(200);
        $this->assertTrue(
            collect($response->json('data'))->contains('id', $testUser->id)
        );
    }

    /** @test */
    public function it_filters_my_bids_by_status()
    {
        $this->actingAs($this->user);
        
        $auction = Auction::factory()->create(['vendor_id' => $this->vendor->vendor->id]);
        
        $activeBid = Bid::factory()->create([
            'user_id' => $this->user->id,
            'auction_id' => $auction->id,
            'status' => 'active'
        ]);
        $lostBid = Bid::factory()->create([
            'user_id' => $this->user->id,
            'auction_id' => $auction->id,
            'status' => 'lost'
        ]);

        $response = $this->getJson('/api/my-bids?status=active');

        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.status', 'active');
    }

    /** @test */
    public function it_validates_filter_parameters()
    {
        // Test invalid category_id
        $response = $this->getJson('/api/products?category_id=999999');
        $response->assertStatus(422);

        // Test invalid per_page (too high)
        $response = $this->getJson('/api/products?per_page=200');
        $response->assertStatus(422);

        // Test invalid date format
        $this->actingAs($this->admin);
        $response = $this->getJson('/api/admin/users?created_after=invalid-date');
        $response->assertStatus(422);
    }

    /** @test */
    public function it_respects_pagination_limits()
    {
        Product::factory()->count(50)->create(['vendor_id' => $this->vendor->vendor->id]);

        // Test default pagination
        $response = $this->getJson('/api/products');
        $response->assertStatus(200)
                ->assertJsonCount(15, 'data'); // Default per_page

        // Test custom pagination within limits
        $response = $this->getJson('/api/products?per_page=25');
        $response->assertStatus(200)
                ->assertJsonCount(25, 'data');

        // Test pagination limit enforcement (should cap at 100)
        $response = $this->getJson('/api/products?per_page=150');
        $response->assertStatus(422); // Should be validated and rejected
    }
}
