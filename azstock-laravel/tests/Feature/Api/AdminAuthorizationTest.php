<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Product;
use App\Models\Auction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AdminAuthorizationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $vendor;
    protected $vendorUser;
    protected $category;
    protected $product;
    protected $auction;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create(['role' => 'admin']);
        
        // Create vendor user and vendor profile
        $this->vendorUser = User::factory()->create(['role' => 'vendor']);
        $this->vendor = $this->vendorUser->vendor()->create([
            'name' => 'Test Vendor',
            'slug' => 'test-vendor',
            'email' => $this->vendorUser->email,
            'is_active' => true
        ]);
        
        // Create test data
        $this->category = Category::factory()->create();
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id
        ]);
        $this->auction = Auction::factory()->create([
            'vendor_id' => $this->vendor->id,
            'product_id' => $this->product->id,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function admin_can_update_any_product()
    {
        $this->actingAs($this->admin);

        $response = $this->putJson("/api/products/{$this->product->id}", [
            'name' => 'Updated by Admin',
            'description' => 'Updated description',
            'category_id' => $this->category->id
        ]);

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Product updated successfully')
                ->assertJsonPath('data.name', 'Updated by Admin');
    }

    /** @test */
    public function admin_can_delete_any_product()
    {
        $this->actingAs($this->admin);

        $response = $this->deleteJson("/api/products/{$this->product->id}");

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Product deleted successfully');
        
        $this->assertDatabaseMissing('products', ['id' => $this->product->id]);
    }

    /** @test */
    public function admin_can_update_any_auction()
    {
        $this->actingAs($this->admin);

        $response = $this->putJson("/api/auctions/{$this->auction->id}", [
            'end_time' => now()->addDays(2)->toISOString(),
            'reserve_price' => 200.0,
            'status' => 'active'
        ]);

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Auction updated successfully');
    }

    /** @test */
    public function admin_can_delete_any_auction()
    {
        $this->actingAs($this->admin);

        $response = $this->deleteJson("/api/auctions/{$this->auction->id}");

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Auction deleted successfully');
        
        $this->assertDatabaseMissing('auctions', ['id' => $this->auction->id]);
    }

    /** @test */
    public function admin_can_end_any_auction()
    {
        $this->actingAs($this->admin);

        $response = $this->postJson("/api/auctions/{$this->auction->id}/end");

        $response->assertStatus(200)
                ->assertJsonFragment(['status' => 'ended']);
    }

    /** @test */
    public function admin_can_update_any_vendor()
    {
        $this->actingAs($this->admin);

        $response = $this->putJson("/api/vendors/{$this->vendor->id}", [
            'name' => 'Updated by Admin',
            'email' => $this->vendor->email
        ]);

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Vendor updated successfully')
                ->assertJsonPath('data.name', 'Updated by Admin');
    }

    /** @test */
    public function admin_can_delete_any_vendor()
    {
        $this->actingAs($this->admin);

        $response = $this->deleteJson("/api/vendors/{$this->vendor->id}");

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Vendor deleted successfully');
        
        $this->assertDatabaseMissing('vendors', ['id' => $this->vendor->id]);
    }

    /** @test */
    public function non_admin_cannot_modify_others_products()
    {
        $otherUser = User::factory()->create(['role' => 'vendor']);
        $otherVendor = $otherUser->vendor()->create([
            'name' => 'Other Vendor',
            'slug' => 'other-vendor',
            'email' => $otherUser->email,
            'is_active' => true
        ]);

        $this->actingAs($otherUser);

        // Try to update another vendor's product
        $response = $this->putJson("/api/products/{$this->product->id}", [
            'name' => 'Unauthorized Update',
            'description' => 'Should fail',
            'category_id' => $this->category->id
        ]);

        $response->assertStatus(403)
                ->assertJsonPath('message', 'You are not authorized to update this product.');
    }

    /** @test */
    public function non_admin_cannot_modify_others_auctions()
    {
        $otherUser = User::factory()->create(['role' => 'vendor']);
        $otherVendor = $otherUser->vendor()->create([
            'name' => 'Other Vendor',
            'slug' => 'other-vendor',
            'email' => $otherUser->email,
            'is_active' => true
        ]);

        $this->actingAs($otherUser);

        // Try to end another vendor's auction
        $response = $this->postJson("/api/auctions/{$this->auction->id}/end");

        $response->assertStatus(403)
                ->assertJsonPath('message', 'You are not authorized to end this auction.');
    }

    /** @test */
    public function vendor_can_still_modify_own_resources()
    {
        $this->actingAs($this->vendorUser);

        // Vendor should still be able to update their own product
        $response = $this->putJson("/api/products/{$this->product->id}", [
            'name' => 'Updated by Owner',
            'description' => 'Updated by the actual owner',
            'category_id' => $this->category->id
        ]);

        $response->assertStatus(200)
                ->assertJsonPath('message', 'Product updated successfully')
                ->assertJsonPath('data.name', 'Updated by Owner');
    }

    /** @test */
    public function buyer_cannot_modify_any_resources()
    {
        $buyer = User::factory()->create(['role' => 'buyer']);
        $this->actingAs($buyer);

        // Buyer should not be able to update any product
        $response = $this->putJson("/api/products/{$this->product->id}", [
            'name' => 'Unauthorized Update',
            'description' => 'Should fail',
            'category_id' => $this->category->id
        ]);

        $response->assertStatus(403)
                ->assertJsonPath('message', 'You are not authorized to update this product.');
    }
}
