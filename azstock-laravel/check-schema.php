<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "Checking database schema...\n\n";

// Check if tables exist
$tables = ['wallets', 'wallet_transactions', 'wallet_holds', 'bids', 'auctions'];
foreach ($tables as $table) {
    if (Schema::hasTable($table)) {
        echo "Table '{$table}' exists.\n";
        
        // Get column information
        $columns = Schema::getColumnListing($table);
        echo "  Columns: " . implode(', ', $columns) . "\n";
        
        // Check for foreign keys
        $foreignKeys = [];
        try {
            $foreignKeys = DB::select(
                "SELECT 
                    tc.constraint_name, 
                    kcu.column_name, 
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name 
                FROM 
                    information_schema.table_constraints AS tc 
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage AS ccu 
                      ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name='{$table}';"
            );
        } catch (\Exception $e) {
            echo "  Error getting foreign keys: " . $e->getMessage() . "\n";
        }
        
        if (!empty($foreignKeys)) {
            echo "  Foreign keys:\n";
            foreach ($foreignKeys as $fk) {
                echo "    {$fk->column_name} -> {$fk->foreign_table_name}.{$fk->foreign_column_name}\n";
            }
        } else {
            echo "  No foreign keys found.\n";
        }
    } else {
        echo "Table '{$table}' does not exist!\n";
    }
    echo "\n";
}

// Check for specific issues
if (Schema::hasTable('wallet_holds')) {
    // Check if transaction_id column exists and is nullable
    if (Schema::hasColumn('wallet_holds', 'transaction_id')) {
        $column = DB::select("SELECT is_nullable FROM information_schema.columns WHERE table_name = 'wallet_holds' AND column_name = 'transaction_id'")[0];
        echo "wallet_holds.transaction_id is " . ($column->is_nullable === 'YES' ? 'nullable' : 'NOT nullable') . "\n";
    }
    
    // Check if there are any records in the wallet_holds table
    $count = DB::table('wallet_holds')->count();
    echo "wallet_holds table has {$count} records.\n";
}

echo "\nDone checking schema.\n";
