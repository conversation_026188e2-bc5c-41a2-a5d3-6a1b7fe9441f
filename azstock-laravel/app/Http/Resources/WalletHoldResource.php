<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WalletHoldResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'wallet_id' => $this->wallet_id,
            'user_id' => $this->user_id,
            'amount' => $this->amount,
            'reason' => $this->reason,
            'status' => $this->status,
            'reference_type' => $this->reference_type,
            'reference_id' => $this->reference_id,
            'transaction_id' => $this->transaction_id,
            'expires_at' => $this->expires_at,
            'released_at' => $this->released_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'wallet' => $this->when($this->relationLoaded('wallet'), new WalletResource($this->wallet)),
            'user' => $this->when($this->relationLoaded('user'), new UserResource($this->user)),
            'transaction' => $this->when($this->relationLoaded('transaction'), new WalletTransactionResource($this->transaction)),
            'is_expired' => $this->isExpired(),
        ];
    }
}
