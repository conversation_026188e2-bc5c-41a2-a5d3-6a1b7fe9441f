<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'description' => $this->description,
            'website' => $this->website,
            'logo' => $this->logo ? $this->getLogoUrl() : null,
            'is_active' => (bool) $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relationships
            'user' => $this->when($this->relationLoaded('user'), new UserResource($this->user)),
            'auctions' => $this->when($this->relationLoaded('auctions'), AuctionResource::collection($this->auctions)),
            'reviews' => $this->when($this->relationLoaded('reviews'), ReviewResource::collection($this->reviews)),

            // Counts
            'auctions_count' => $this->when(isset($this->auctions_count), $this->auctions_count),
            'total_auctions_count' => $this->when(isset($this->total_auctions_count), $this->total_auctions_count),
            'active_auctions_count' => $this->when(isset($this->active_auctions_count), $this->active_auctions_count),
            'completed_auctions_count' => $this->when(isset($this->completed_auctions_count), $this->completed_auctions_count),
            'total_reviews_count' => $this->when(isset($this->total_reviews_count), $this->total_reviews_count),

            // Computed fields
            'average_rating' => $this->when(isset($this->average_rating), $this->average_rating),

            // Member since (formatted)
            'member_since' => $this->created_at ? $this->created_at->format('F Y') : null,
        ];
    }
}
