<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuctionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'starting_price' => $this->starting_price,
            'current_price' => $this->current_price,
            'reserve_price' => $this->reserve_price,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'product' => $this->when($this->relationLoaded('product'), new SimpleProductResource($this->product)),
            'vendor' => $this->when($this->relationLoaded('vendor'), new SimpleVendorResource($this->vendor)),
            'bids' => $this->when($this->relationLoaded('bids'), SimpleBidResource::collection($this->bids)),
            'bids_count' => $this->when(isset($this->bids_count), $this->bids_count),
            'time_remaining' => $this->when(
                $this->end_time && $this->end_time > now(),
                $this->end_time ? $this->end_time->diffForHumans() : null
            ),
            'is_active' => $this->status === 'active' &&
                           ($this->start_time ? $this->start_time <= now() : true) &&
                           ($this->end_time ? $this->end_time > now() : true),
        ];
    }
}
