<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuctionItemImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'url' => $this->getImageUrl(),
            'thumbnail_url' => $this->getThumbnailUrl(),
            'original_name' => $this->original_name,
            'file_size' => $this->file_size,
            'created_at' => $this->created_at,
        ];
    }
}
