<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,

            'image' => $this->image ? $this->getImageUrl() : null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'category' => $this->when($this->relationLoaded('category'), new SimpleCategoryResource($this->category)),
            'vendor' => $this->when($this->relationLoaded('vendor'), new SimpleVendorResource($this->vendor)),
            'images' => $this->when($this->relationLoaded('images'), ProductImageResource::collection($this->images)),
            'auction' => $this->when($this->relationLoaded('auction'), new AuctionResource($this->auction)),
        ];
    }
}
