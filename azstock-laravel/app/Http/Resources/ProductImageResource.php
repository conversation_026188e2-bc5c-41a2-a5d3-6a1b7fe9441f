<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $imageUrl = $this->image_path;
        $thumbnailUrl = null;

        // Check if it's a Vercel Blob URL
        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            // It's already a full URL (Vercel Blob)
            $imageUrl = $this->image_path;
            // For Vercel Blob, we'll use the same URL for thumbnail for now
            // In the future, you could implement thumbnail generation
            $thumbnailUrl = $this->image_path;
        } else {
            // It's a local storage path
            $imageUrl = asset('storage/' . $this->image_path);

            // Generate thumbnail URL for local storage
            $pathInfo = pathinfo($this->image_path);
            $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

            if (\Illuminate\Support\Facades\Storage::disk('public')->exists($thumbnailPath)) {
                $thumbnailUrl = asset('storage/' . $thumbnailPath);
            }
        }

        return [
            'id' => $this->id,
            'url' => $imageUrl,
            'thumbnail_url' => $thumbnailUrl,
            'original_name' => $this->original_name ?? null,
            'file_size' => $this->file_size ?? null,
            'mime_type' => $this->mime_type ?? null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
