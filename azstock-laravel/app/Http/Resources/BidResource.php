<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'bid_amount' => $this->bid_amount,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'user' => $this->when($this->relationLoaded('user'), new UserResource($this->user)),
            'auction' => $this->when($this->relationLoaded('auction'), new AuctionResource($this->auction)),
            'wallet_hold' => $this->when($this->relationLoaded('walletHold'), new WalletHoldResource($this->walletHold)),
        ];
    }
}
