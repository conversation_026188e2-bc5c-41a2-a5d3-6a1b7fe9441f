<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'user' => $this->when($this->relationLoaded('user'), new UserResource($this->user)),
            'vendor' => $this->when($this->relationLoaded('vendor'), new SimpleVendorResource($this->vendor)),
            'auction_item' => $this->when($this->relationLoaded('auctionItem'), new AuctionItemResource($this->auctionItem)),
            
            // Formatted date
            'created_at_human' => $this->created_at ? $this->created_at->diffForHumans() : null,
        ];
    }
}
