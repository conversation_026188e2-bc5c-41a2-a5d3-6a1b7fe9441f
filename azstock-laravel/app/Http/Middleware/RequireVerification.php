<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RequireVerification
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role  The role that needs to be verified (buyer, vendor, admin)
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Authentication required.'
            ], 401);
        }

        // Check if user has the required role
        $hasRole = false;
        switch ($role) {
            case 'buyer':
                $hasRole = $user->isBuyer();
                break;
            case 'vendor':
                $hasRole = $user->isVendor();
                break;
            case 'admin':
                $hasRole = $user->isAdmin();
                break;
        }

        if (!$hasRole) {
            return response()->json([
                'message' => "You must have the {$role} role to access this resource."
            ], 403);
        }

        // Check if user is verified for the required role
        $isVerified = false;
        switch ($role) {
            case 'buyer':
                $isVerified = $user->isVerifiedBuyer();
                break;
            case 'vendor':
                $isVerified = $user->isVerifiedVendor();
                break;
            case 'admin':
                $isVerified = $user->isVerifiedAdmin();
                break;
        }

        if (!$isVerified) {
            return response()->json([
                'message' => "Your {$role} account is not verified. Please contact an administrator to verify your account.",
                'verification_required' => true,
                'required_role' => $role,
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        return $next($request);
    }
}
