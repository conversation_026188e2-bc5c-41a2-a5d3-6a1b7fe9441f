<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Auction; 
use App\Models\Vendor;
use Illuminate\Http\Request;

class AuctionController extends Controller
{
    // Display all auctions
    public function index()
    {
        $auctions = Auction::with('product')->get(); // Eager load product details for each auction
        return view('website.all-auctions', compact('auctions'));
    }

    // Display auctions for the authenticated vendor
    public function myAuctions()
    {
        $vendor = auth()->user()->vendor; // Get the authenticated vendor
        if (!$vendor) {
            return redirect()->route('dashboard')->withErrors(['error' => 'You do not have a vendor account.']);
        }

        $auctions = Auction::where('vendor_id', $vendor->id)->with('product')->get(); // Fetch only auctions belonging to the current user
        return view('dashboard.auctions.index', compact('auctions'));
    }

    // Show form to create an auction
    public function create()
    {
        $products = Product::all(); // Get all products for auction creation
        return view('dashboard.auctions.create', compact('products'));
    }

    // Store auction details
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'starting_price' => 'required|numeric|min:0',
            'reserve_price' => 'nullable|numeric|min:0',
        ]);

        $vendor = auth()->user()->vendor; // Get the authenticated vendor
        if (!$vendor) {
            return redirect()->back()->withErrors(['error' => 'You do not have a vendor account.']);
        }

        Auction::create([
            'vendor_id' => $vendor->id,
            'product_id' => $request->product_id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'starting_price' => $request->starting_price,
            'current_price' => $request->starting_price,
            'reserve_price' => $request->reserve_price,
            'status' => 'active',
        ]);

        return redirect()->route('auctions.myAuctions')->with('success', 'Auction created successfully.');
    }

    // Show auction details and bids
    public function show(Auction $auction)
    {
        $auction->load('product');
        return view('dashboard.auctions.show', compact('auction'));
    }

     public function myAuction(Auction $auction)
    {
        $auction->load('product');

        return view('website.show', compact('auction'));
    }
}
