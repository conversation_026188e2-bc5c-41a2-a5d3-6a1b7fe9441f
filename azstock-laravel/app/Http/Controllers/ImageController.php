<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ImageController extends Controller
{
    // Show the upload form
    public function create()
    {
        return view('images.create');
    }

    // Handle the image upload
    public function store(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:2048',  // Ensure it's an image and restrict size
        ]);

        // Store image in the user's directory
        $path = $request->file('image')->store('media/' . Auth::id(), 'public');

        // Save image record in the database
        $image = Image::create([
            'user_id' => Auth::id(),
            'file_path' => $path,
            'title' => $request->input('title'),
            'description' => $request->input('description'),
        ]);

        return redirect()->route('images.index')->with('success', 'Image uploaded successfully');
    }

    // List all images for the user
    public function index()
    {
        $images = Auth::user()->images; // Assuming the User model has a 'images' relationship
        return view('images.index', compact('images'));
    }
}
