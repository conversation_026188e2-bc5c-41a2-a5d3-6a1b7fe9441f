<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Bid;
use App\Models\Auction;
use Illuminate\Support\Facades\Auth;

class BidController extends Controller
{
    public function store(Request $request, Auction $auction)
    {
        $request->validate([
            'bid_amount' => 'required|numeric|min:' . max($auction->current_price, $auction->starting_price),
        ]);

        $bid = Bid::create([
            'auction_id' => $auction->id,
            'user_id' => auth()->id(),
            'bid_amount' => $request->bid_amount,
        ]);

        $auction->update(['current_price' => $bid->bid_amount]);

        return back()->with('success', 'Your bid has been placed.');
    }
}
