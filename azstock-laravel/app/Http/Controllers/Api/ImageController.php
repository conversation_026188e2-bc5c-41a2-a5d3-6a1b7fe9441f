<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AuctionItemImage;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ImageController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Store image URLs from frontend Vercel Blob uploads.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImages(Request $request)
    {
        $request->validate([
            'images' => 'required|array|min:1|max:10',
            'images.*.url' => 'required|url',
            'images.*.original_name' => 'required|string',
            'images.*.size' => 'nullable|integer',
        ]);

        try {
            $user = $request->user();

            // Check if user is a vendor
            if (!$user->vendor) {
                return response()->json([
                    'message' => 'You must be a vendor to upload images.'
                ], 403);
            }

            // Process the Vercel Blob URLs
            $uploadedImages = [];
            $errors = [];

            foreach ($request->input('images') as $imageData) {
                try {
                    // Validate that the URL is from Vercel Blob Storage
                    $vercelBlobBaseUrl = 'https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com';
                    if (!str_starts_with($imageData['url'], $vercelBlobBaseUrl)) {
                        $errors[] = "Invalid image URL: {$imageData['url']}";
                        continue;
                    }

                    $uploadedImages[] = [
                        'url' => $imageData['url'],
                        'thumbnail_url' => $imageData['url'], // Use same URL for thumbnail for now
                        'original_name' => $imageData['original_name'],
                        'path' => $imageData['url'], // Store URL as path for Vercel Blob
                    ];
                } catch (\Exception $e) {
                    $errors[] = "Error processing image {$imageData['original_name']}: " . $e->getMessage();
                }
            }

            return response()->json([
                'message' => 'Images processed successfully',
                'images' => $uploadedImages,
                'upload_errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error processing images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload images for an auction item.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadAuctionItemImages(Request $request)
    {
        $request->validate([
            'auction_item_id' => 'required|exists:auction_items,id',
            'images' => 'required|array|min:1|max:10',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        try {
            $auctionItemId = $request->input('auction_item_id');
            $user = $request->user();

            // Check if user owns the auction item (through vendor)
            $auctionItem = \App\Models\AuctionItem::with('auction.vendor')->findOrFail($auctionItemId);

            if (!$user->vendor || $auctionItem->auction->vendor_id !== $user->vendor->id) {
                return response()->json([
                    'message' => 'You are not authorized to upload images for this auction item.'
                ], 403);
            }

            $directory = "auction_items/{$auctionItemId}";
            $uploadResult = $this->imageService->uploadMultipleImages(
                $request->file('images'),
                $directory,
                ['create_thumbnail' => true]
            );

            // Save image records to database
            $savedImages = [];
            foreach ($uploadResult['uploaded'] as $imageInfo) {
                $auctionItemImage = AuctionItemImage::create([
                    'auction_item_id' => $auctionItemId,
                    'image_path' => $imageInfo['path'],
                    'original_name' => $imageInfo['original_name'],
                    'file_size' => $imageInfo['size'],
                ]);

                $savedImages[] = [
                    'id' => $auctionItemImage->id,
                    'url' => $imageInfo['url'],
                    'thumbnail_url' => $imageInfo['thumbnail']['url'] ?? null,
                    'original_name' => $imageInfo['original_name'],
                ];
            }

            return response()->json([
                'message' => 'Images uploaded successfully',
                'uploaded_images' => $savedImages,
                'upload_errors' => $uploadResult['errors']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error uploading images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an auction item image.
     *
     * @param Request $request
     * @param int $imageId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAuctionItemImage(Request $request, $imageId)
    {
        try {
            $user = $request->user();
            $auctionItemImage = AuctionItemImage::with('auctionItem.auction.vendor')->findOrFail($imageId);

            // Check if user owns the auction item (through vendor)
            if (!$user->vendor || $auctionItemImage->auctionItem->auction->vendor_id !== $user->vendor->id) {
                return response()->json([
                    'message' => 'You are not authorized to delete this image.'
                ], 403);
            }

            // Delete file from storage
            $this->imageService->deleteImage($auctionItemImage->image_path);

            // Delete thumbnail if exists
            $thumbnailPath = $this->getThumbnailPath($auctionItemImage->image_path);
            if ($thumbnailPath) {
                $this->imageService->deleteImage($thumbnailPath);
            }

            // Delete database record
            $auctionItemImage->delete();

            return response()->json([
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get auction item images.
     *
     * @param Request $request
     * @param int $auctionItemId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAuctionItemImages(Request $request, $auctionItemId)
    {
        try {
            $images = AuctionItemImage::where('auction_item_id', $auctionItemId)
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'url' => $this->imageService->getImageUrl($image->image_path),
                        'thumbnail_url' => $this->getThumbnailUrl($image->image_path),
                        'original_name' => $image->original_name,
                        'file_size' => $image->file_size,
                        'created_at' => $image->created_at,
                    ];
                });

            return response()->json([
                'images' => $images
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get thumbnail path from original image path.
     *
     * @param string $imagePath
     * @return string|null
     */
    private function getThumbnailPath(string $imagePath): ?string
    {
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

        return $this->imageService->imageExists($thumbnailPath) ? $thumbnailPath : null;
    }

    /**
     * Get thumbnail URL from original image path.
     *
     * @param string $imagePath
     * @return string|null
     */
    private function getThumbnailUrl(string $imagePath): ?string
    {
        $thumbnailPath = $this->getThumbnailPath($imagePath);
        return $thumbnailPath ? $this->imageService->getImageUrl($thumbnailPath) : null;
    }

    /**
     * Debug URL extraction (for development only).
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugUrl(Request $request)
    {
        $request->validate([
            'url' => 'required|string'
        ]);

        $url = $request->input('url');

        // Use reflection to access private method for debugging
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('extractPathFromUrl');
        $method->setAccessible(true);
        $extractedPath = $method->invoke($this->imageService, $url);

        return response()->json([
            'original_url' => $url,
            'extracted_path' => $extractedPath,
            'file_exists' => $extractedPath ? $this->imageService->imageExists($extractedPath) : false,
            'storage_base_url' => \Illuminate\Support\Facades\Storage::disk('public')->url(''),
            'app_url' => config('app.url'),
        ]);
    }

    /**
     * Debug auction item images (for development only).
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugAuctionItemImages(Request $request)
    {
        $auctionItemId = $request->input('auction_item_id', 1); // Default to auction item 1

        $auctionItem = \App\Models\AuctionItem::with('images')->find($auctionItemId);

        if (!$auctionItem) {
            return response()->json([
                'error' => 'Auction item not found',
                'auction_item_id' => $auctionItemId
            ]);
        }

        $allImages = \App\Models\AuctionItemImage::all();

        // Check temp directory
        $tempPath = storage_path('app/public/temp/11/2025-06-01');
        $tempFiles = [];
        if (is_dir($tempPath)) {
            $tempFiles = array_diff(scandir($tempPath), ['.', '..']);
        }

        // Check auction_items directory
        $auctionItemsPath = storage_path('app/public/auction_items');
        $auctionItemDirs = [];
        if (is_dir($auctionItemsPath)) {
            $auctionItemDirs = array_diff(scandir($auctionItemsPath), ['.', '..']);
        }

        return response()->json([
            'auction_item_id' => $auctionItemId,
            'auction_item_name' => $auctionItem->item_name,
            'auction_item_images_count' => $auctionItem->images->count(),
            'auction_item_images' => $auctionItem->images->toArray(),
            'all_auction_item_images_in_db' => $allImages->toArray(),
            'total_images_in_db' => $allImages->count(),
            'temp_files' => $tempFiles,
            'auction_item_directories' => $auctionItemDirs,
            'storage_paths' => [
                'temp' => $tempPath,
                'auction_items' => $auctionItemsPath,
            ]
        ]);
    }

    /**
     * Test image moving (for development only).
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testImageMove(Request $request)
    {
        $request->validate([
            'url' => 'required|string',
            'auction_item_id' => 'required|integer'
        ]);

        $url = $request->input('url');
        $auctionItemId = $request->input('auction_item_id');

        try {
            // Test the image moving process
            $moveResult = $this->imageService->moveImagesToAuctionItem([$url], $auctionItemId);

            return response()->json([
                'url' => $url,
                'auction_item_id' => $auctionItemId,
                'move_result' => $moveResult,
                'storage_base_url' => \Illuminate\Support\Facades\Storage::disk('public')->url(''),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
