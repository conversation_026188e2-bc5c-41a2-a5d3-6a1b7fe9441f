<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AuctionItemImage;
use Illuminate\Http\Request;

class ImageController extends Controller
{
    /**
     * Store image URLs from frontend Vercel Blob uploads.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImages(Request $request)
    {
        $request->validate([
            'images' => 'required|array|min:1|max:10',
            'images.*.url' => 'required|url',
            'images.*.original_name' => 'required|string',
            'images.*.size' => 'nullable|integer',
        ]);

        try {
            $user = $request->user();

            // Check if user is a vendor
            if (!$user->vendor) {
                return response()->json([
                    'message' => 'You must be a vendor to upload images.'
                ], 403);
            }

            // Process the Vercel Blob URLs
            $uploadedImages = [];
            $errors = [];

            foreach ($request->input('images') as $imageData) {
                try {
                    // Validate that the URL is from Vercel Blob Storage
                    $vercelBlobBaseUrl = 'https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com';
                    if (!str_starts_with($imageData['url'], $vercelBlobBaseUrl)) {
                        $errors[] = "Invalid image URL: {$imageData['url']}";
                        continue;
                    }

                    $uploadedImages[] = [
                        'url' => $imageData['url'],
                        'thumbnail_url' => $imageData['url'], // Use same URL for thumbnail for now
                        'original_name' => $imageData['original_name'],
                        'path' => $imageData['url'], // Store URL as path for Vercel Blob
                    ];
                } catch (\Exception $e) {
                    $errors[] = "Error processing image {$imageData['original_name']}: " . $e->getMessage();
                }
            }

            return response()->json([
                'message' => 'Images processed successfully',
                'images' => $uploadedImages,
                'upload_errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error processing images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get auction item images.
     *
     * @param Request $request
     * @param int $auctionItemId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAuctionItemImages(Request $request, $auctionItemId)
    {
        try {
            $images = AuctionItemImage::where('auction_item_id', $auctionItemId)
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'url' => $image->getImageUrl(),
                        'thumbnail_url' => $image->getImageUrl(), // Use same URL for now
                        'original_name' => $image->original_name,
                        'file_size' => $image->file_size,
                        'created_at' => $image->created_at,
                    ];
                });

            return response()->json([
                'images' => $images
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an auction item image.
     *
     * @param Request $request
     * @param int $imageId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAuctionItemImage(Request $request, $imageId)
    {
        try {
            $user = $request->user();
            $auctionItemImage = AuctionItemImage::with('auctionItem.auction.vendor')->findOrFail($imageId);

            // Check if user owns the auction item (through vendor)
            if (!$user->vendor || $auctionItemImage->auctionItem->auction->vendor_id !== $user->vendor->id) {
                return response()->json([
                    'message' => 'You are not authorized to delete this image.'
                ], 403);
            }

            // Delete database record (file deletion handled by frontend for Vercel Blob)
            $auctionItemImage->delete();

            return response()->json([
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload multiple images for an auction item.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadAuctionItemImages(Request $request)
    {
        $request->validate([
            'auction_item_id' => 'required|exists:auction_items,id',
            'images' => 'required|array|min:1|max:10',
            'images.*.url' => 'required|url',
            'images.*.original_name' => 'required|string',
            'images.*.size' => 'nullable|integer',
        ]);

        try {
            $auctionItemId = $request->input('auction_item_id');
            $user = $request->user();

            // Check if user owns the auction item (through vendor)
            $auctionItem = \App\Models\AuctionItem::with('auction.vendor')->findOrFail($auctionItemId);

            if (!$user->vendor || $auctionItem->auction->vendor_id !== $user->vendor->id) {
                return response()->json([
                    'message' => 'You are not authorized to upload images for this auction item.'
                ], 403);
            }

            // Save image records to database
            $savedImages = [];
            foreach ($request->input('images') as $imageData) {
                $auctionItemImage = AuctionItemImage::create([
                    'auction_item_id' => $auctionItemId,
                    'image_path' => $imageData['url'],
                    'original_name' => $imageData['original_name'],
                    'file_size' => $imageData['size'] ?? null,
                ]);

                $savedImages[] = [
                    'id' => $auctionItemImage->id,
                    'url' => $auctionItemImage->getImageUrl(),
                    'thumbnail_url' => $auctionItemImage->getImageUrl(),
                    'original_name' => $auctionItemImage->original_name,
                ];
            }

            return response()->json([
                'message' => 'Images uploaded successfully',
                'uploaded_images' => $savedImages,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error uploading images',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
