<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductImage;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ImageController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Store image URLs from frontend Vercel Blob uploads.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImages(Request $request)
    {
        $request->validate([
            'images' => 'required|array|min:1|max:10',
            'images.*.url' => 'required|url',
            'images.*.original_name' => 'required|string',
            'images.*.size' => 'nullable|integer',
        ]);

        try {
            $user = $request->user();

            // Check if user is a vendor
            if (!$user->vendor) {
                return response()->json([
                    'message' => 'You must be a vendor to upload images.'
                ], 403);
            }

            // Process the Vercel Blob URLs
            $uploadedImages = [];
            $errors = [];

            foreach ($request->input('images') as $imageData) {
                try {
                    // Validate that the URL is from Vercel Blob Storage
                    $vercelBlobBaseUrl = 'https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com';
                    if (!str_starts_with($imageData['url'], $vercelBlobBaseUrl)) {
                        $errors[] = "Invalid image URL: {$imageData['url']}";
                        continue;
                    }

                    $uploadedImages[] = [
                        'url' => $imageData['url'],
                        'thumbnail_url' => $imageData['url'], // Use same URL for thumbnail for now
                        'original_name' => $imageData['original_name'],
                        'path' => $imageData['url'], // Store URL as path for Vercel Blob
                    ];
                } catch (\Exception $e) {
                    $errors[] = "Error processing image {$imageData['original_name']}: " . $e->getMessage();
                }
            }

            return response()->json([
                'message' => 'Images processed successfully',
                'images' => $uploadedImages,
                'upload_errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error processing images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload images for a product.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadProductImages(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'images' => 'required|array|min:1|max:10',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        try {
            $productId = $request->input('product_id');
            $user = $request->user();

            // Check if user owns the product (through vendor)
            $product = \App\Models\Product::with('vendor')->findOrFail($productId);

            if (!$user->vendor || $product->vendor_id !== $user->vendor->id) {
                return response()->json([
                    'message' => 'You are not authorized to upload images for this product.'
                ], 403);
            }

            $directory = "products/{$productId}";
            $uploadResult = $this->imageService->uploadMultipleImages(
                $request->file('images'),
                $directory,
                ['create_thumbnail' => true]
            );

            // Save image records to database
            $savedImages = [];
            foreach ($uploadResult['uploaded'] as $imageInfo) {
                $productImage = ProductImage::create([
                    'product_id' => $productId,
                    'image_path' => $imageInfo['path'],
                    'original_name' => $imageInfo['original_name'],
                    'file_size' => $imageInfo['size'],
                    'mime_type' => $imageInfo['mime_type'],
                ]);

                $savedImages[] = [
                    'id' => $productImage->id,
                    'url' => $imageInfo['url'],
                    'thumbnail_url' => $imageInfo['thumbnail']['url'] ?? null,
                    'original_name' => $imageInfo['original_name'],
                ];
            }

            return response()->json([
                'message' => 'Images uploaded successfully',
                'uploaded_images' => $savedImages,
                'upload_errors' => $uploadResult['errors']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error uploading images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a product image.
     *
     * @param Request $request
     * @param int $imageId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteProductImage(Request $request, $imageId)
    {
        try {
            $user = $request->user();
            $productImage = ProductImage::with('product.vendor')->findOrFail($imageId);

            // Check if user owns the product (through vendor)
            if (!$user->vendor || $productImage->product->vendor_id !== $user->vendor->id) {
                return response()->json([
                    'message' => 'You are not authorized to delete this image.'
                ], 403);
            }

            // Delete file from storage
            $this->imageService->deleteImage($productImage->image_path);

            // Delete thumbnail if exists
            $thumbnailPath = $this->getThumbnailPath($productImage->image_path);
            if ($thumbnailPath) {
                $this->imageService->deleteImage($thumbnailPath);
            }

            // Delete database record
            $productImage->delete();

            return response()->json([
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product images.
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductImages(Request $request, $productId)
    {
        try {
            $images = ProductImage::where('product_id', $productId)
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'url' => $this->imageService->getImageUrl($image->image_path),
                        'thumbnail_url' => $this->getThumbnailUrl($image->image_path),
                        'original_name' => $image->original_name,
                        'file_size' => $image->file_size,
                        'created_at' => $image->created_at,
                    ];
                });

            return response()->json([
                'images' => $images
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get thumbnail path from original image path.
     *
     * @param string $imagePath
     * @return string|null
     */
    private function getThumbnailPath(string $imagePath): ?string
    {
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

        return $this->imageService->imageExists($thumbnailPath) ? $thumbnailPath : null;
    }

    /**
     * Get thumbnail URL from original image path.
     *
     * @param string $imagePath
     * @return string|null
     */
    private function getThumbnailUrl(string $imagePath): ?string
    {
        $thumbnailPath = $this->getThumbnailPath($imagePath);
        return $thumbnailPath ? $this->imageService->getImageUrl($thumbnailPath) : null;
    }

    /**
     * Debug URL extraction (for development only).
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugUrl(Request $request)
    {
        $request->validate([
            'url' => 'required|string'
        ]);

        $url = $request->input('url');

        // Use reflection to access private method for debugging
        $reflection = new \ReflectionClass($this->imageService);
        $method = $reflection->getMethod('extractPathFromUrl');
        $method->setAccessible(true);
        $extractedPath = $method->invoke($this->imageService, $url);

        return response()->json([
            'original_url' => $url,
            'extracted_path' => $extractedPath,
            'file_exists' => $extractedPath ? $this->imageService->imageExists($extractedPath) : false,
            'storage_base_url' => \Illuminate\Support\Facades\Storage::disk('public')->url(''),
            'app_url' => config('app.url'),
        ]);
    }

    /**
     * Debug product images (for development only).
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugProductImages(Request $request)
    {
        $productId = $request->input('product_id', 33); // Default to product 33

        $product = \App\Models\Product::with('images')->find($productId);

        if (!$product) {
            return response()->json([
                'error' => 'Product not found',
                'product_id' => $productId
            ]);
        }

        $allImages = \App\Models\ProductImage::all();

        // Check temp directory
        $tempPath = storage_path('app/public/temp/11/2025-06-01');
        $tempFiles = [];
        if (is_dir($tempPath)) {
            $tempFiles = array_diff(scandir($tempPath), ['.', '..']);
        }

        // Check products directory
        $productsPath = storage_path('app/public/products');
        $productDirs = [];
        if (is_dir($productsPath)) {
            $productDirs = array_diff(scandir($productsPath), ['.', '..']);
        }

        return response()->json([
            'product_id' => $productId,
            'product_name' => $product->name,
            'product_images_count' => $product->images->count(),
            'product_images' => $product->images->toArray(),
            'all_product_images_in_db' => $allImages->toArray(),
            'total_images_in_db' => $allImages->count(),
            'temp_files' => $tempFiles,
            'product_directories' => $productDirs,
            'storage_paths' => [
                'temp' => $tempPath,
                'products' => $productsPath,
            ]
        ]);
    }

    /**
     * Test image moving (for development only).
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testImageMove(Request $request)
    {
        $request->validate([
            'url' => 'required|string',
            'product_id' => 'required|integer'
        ]);

        $url = $request->input('url');
        $productId = $request->input('product_id');

        try {
            // Test the image moving process
            $moveResult = $this->imageService->moveImagesToProduct([$url], $productId);

            return response()->json([
                'url' => $url,
                'product_id' => $productId,
                'move_result' => $moveResult,
                'storage_base_url' => \Illuminate\Support\Facades\Storage::disk('public')->url(''),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
