<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ActivityLogResource;
use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ActivityLogController extends Controller
{
    /**
     * Display a listing of activity logs.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Check if user is admin
        $user = $request->user();
        if (!$user->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Get query parameters
        $userId = $request->query('user_id');
        $actionType = $request->query('action_type');
        $entityType = $request->query('entity_type');
        $entityId = $request->query('entity_id');
        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');
        $perPage = $request->query('per_page', 15);

        // Build query
        $query = ActivityLog::with('user');

        // Apply filters
        if ($userId) {
            $query->forUser($userId);
        }

        if ($actionType) {
            $query->ofType($actionType);
        }

        if ($entityType && $entityId) {
            $query->forEntity($entityType, $entityId);
        } elseif ($entityType) {
            $query->where('entity_type', $entityType);
        }

        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $logs = $query->paginate($perPage);

        return ActivityLogResource::collection($logs);
    }

    /**
     * Get activity log statistics.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function statistics(Request $request)
    {
        // Check if user is admin
        $user = $request->user();
        if (!$user->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Get date range
        $startDate = $request->query('start_date', now()->subDays(30)->toDateString());
        $endDate = $request->query('end_date', now()->toDateString() . ' 23:59:59');

        // Get total logs count
        $totalLogs = ActivityLog::whereBetween('created_at', [$startDate, $endDate])->count();

        // Get logs by action type
        $logsByActionType = ActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->select('action_type', DB::raw('count(*) as count'))
            ->groupBy('action_type')
            ->get();

        // Get logs by entity type
        $logsByEntityType = ActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->select('entity_type', DB::raw('count(*) as count'))
            ->groupBy('entity_type')
            ->get();

        // Get top users by activity
        $topUsers = ActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id', DB::raw('count(*) as count'))
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();

        // Get user details for top users
        $topUsersWithDetails = [];
        foreach ($topUsers as $userStat) {
            $user = User::find($userStat->user_id);
            if ($user) {
                $topUsersWithDetails[] = [
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'count' => $userStat->count
                ];
            }
        }

        // Get daily activity counts for the past 30 days
        $dailyActivity = ActivityLog::whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'total_logs' => $totalLogs,
            'logs_by_action_type' => $logsByActionType,
            'logs_by_entity_type' => $logsByEntityType,
            'top_users' => $topUsersWithDetails,
            'daily_activity' => $dailyActivity,
            'date_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ]);
    }
}
