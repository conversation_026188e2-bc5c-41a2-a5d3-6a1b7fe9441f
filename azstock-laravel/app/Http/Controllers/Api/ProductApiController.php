<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProductResource;
use App\Models\Product;
use App\Services\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ProductApiController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            // Validate filter parameters
            $request->validate([
                'category_id' => 'nullable|integer|exists:categories,id',
                'vendor_id' => 'nullable|integer|exists:vendors,id',
                'search' => 'nullable|string|max:255',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
                'has_auction' => 'nullable|boolean',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $query = Product::query();

            // Add basic relationships with select to limit data
            $query->with([
                'category:id,name',
                'vendor:id,name,slug,email,logo,is_active',
                'auction:id,product_id,vendor_id,start_time,end_time,starting_price,current_price,status'
            ]);

            // Apply filters using when() for conditional filtering
            $query->when($request->filled('category_id'), function ($q) use ($request) {
                return $q->where('category_id', $request->category_id);
            });

            $query->when($request->filled('vendor_id'), function ($q) use ($request) {
                return $q->where('vendor_id', $request->vendor_id);
            });

            // Search in name and description (case-insensitive)
            $query->when($request->filled('search'), function ($q) use ($request) {
                $search = $request->search;
                return $q->where(function ($query) use ($search) {
                    $query->where('name', 'like', '%' . $search . '%')
                          ->orWhere('description', 'like', '%' . $search . '%');
                });
            });

            // Filter by auction existence
            $query->when($request->filled('has_auction'), function ($q) use ($request) {
                if ($request->boolean('has_auction')) {
                    return $q->whereHas('auction', function ($auctionQuery) {
                        $auctionQuery->where('status', 'active');
                    });
                } else {
                    return $q->whereDoesntHave('auction');
                }
            });

            // Price filters (based on auction prices for future integration)
            $query->when($request->filled('min_price'), function ($q) use ($request) {
                return $q->whereHas('auction', function ($auctionQuery) use ($request) {
                    $auctionQuery->where('current_price', '>=', $request->min_price)
                                 ->orWhere('starting_price', '>=', $request->min_price);
                });
            });

            $query->when($request->filled('max_price'), function ($q) use ($request) {
                return $q->whereHas('auction', function ($auctionQuery) use ($request) {
                    $auctionQuery->where('current_price', '<=', $request->max_price)
                                 ->orWhere('starting_price', '<=', $request->max_price);
                });
            });

            // Default ordering
            $query->orderBy('created_at', 'desc');

            // Log complex queries for performance monitoring
            if ($request->filled(['search', 'min_price', 'max_price', 'has_auction'])) {
                \Log::info('Complex product filter query executed', [
                    'filters' => $request->only(['category_id', 'vendor_id', 'search', 'min_price', 'max_price', 'has_auction']),
                    'user_id' => auth()->id()
                ]);
            }

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $products = $query->paginate($perPage);

            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving products',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Check if user is verified vendor
        $user = $request->user();
        if (!$user->canSell()) {
            return response()->json([
                'message' => 'You must be a verified vendor to create products.',
                'verification_required' => true,
                'required_role' => 'vendor',
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'image_url' => 'nullable|url',
            'image_urls' => 'nullable|array|max:10',
            'image_urls.*' => 'url',
        ]);

        $vendor = auth()->user()->vendor;
        if (!$vendor) {
            return response()->json([
                'message' => 'You do not have a vendor account.'
            ], 403);
        }

        try {
            $product = Product::create([
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
                'vendor_id' => $vendor->id,
            ]);

            // Handle images
            $processedImages = [];
            $imageErrors = [];
            $featuredImagePath = null;

            // Step 1: Handle dedicated featured image if provided
            if ($request->has('image_url') && !empty($request->image_url)) {
                \Log::info('Processing dedicated featured image', [
                    'product_id' => $product->id,
                    'image_url' => $request->image_url
                ]);

                $featuredMoveResult = $this->imageService->moveImagesToProduct(
                    [$request->image_url],
                    $product->id
                );

                if (!empty($featuredMoveResult['moved'])) {
                    $featuredImagePath = $featuredMoveResult['moved'][0]['new_path'];
                    \Log::info('Featured image processed', ['path' => $featuredImagePath]);
                } else {
                    $imageErrors = array_merge($imageErrors, $featuredMoveResult['errors']);
                }
            }

            // Step 2: Handle additional product images if provided
            if ($request->has('image_urls') && !empty($request->image_urls)) {
                \Log::info('Processing additional product images', [
                    'product_id' => $product->id,
                    'image_urls' => $request->image_urls
                ]);

                // Move images from temporary storage to product storage
                $moveResult = $this->imageService->moveImagesToProduct(
                    $request->image_urls,
                    $product->id
                );

                \Log::info('Additional images move result', $moveResult);

                // Save image records to database
                foreach ($moveResult['moved'] as $index => $imageInfo) {
                    \Log::info('Creating product image record', $imageInfo);

                    $productImage = $product->images()->create([
                        'image_path' => $imageInfo['new_path'],
                        'original_name' => basename($imageInfo['new_path']),
                        'file_size' => null, // We don't have this info from URLs
                        'mime_type' => null, // We don't have this info from URLs
                    ]);

                    \Log::info('Created product image', ['id' => $productImage->id]);

                    $processedImages[] = [
                        'id' => $productImage->id,
                        'url' => $imageInfo['url'],
                        'thumbnail_url' => $imageInfo['thumbnail_url'],
                    ];

                    // If no dedicated featured image was provided, use the first additional image
                    if (!$featuredImagePath && $index === 0) {
                        $featuredImagePath = $imageInfo['new_path'];
                        \Log::info('Using first additional image as featured image', [
                            'path' => $featuredImagePath
                        ]);
                    }
                }

                $imageErrors = array_merge($imageErrors, $moveResult['errors']);
            }

            // Step 3: Set the featured image
            if ($featuredImagePath) {
                $product->update(['image' => $featuredImagePath]);
                \Log::info('Set featured image for product', [
                    'product_id' => $product->id,
                    'featured_image' => $featuredImagePath,
                    'source' => $request->has('image_url') ? 'dedicated' : 'first_additional'
                ]);
            }

            if (!empty($imageErrors)) {
                \Log::error('Image processing errors', $imageErrors);
            }

            $product->load(['category', 'vendor', 'images']);

            $response = [
                'message' => 'Product created successfully',
                'data' => new ProductResource($product)
            ];

            if (!empty($processedImages)) {
                $response['processed_images'] = $processedImages;
            }

            if (!empty($imageErrors)) {
                $response['image_errors'] = $imageErrors;
            }

            return response()->json($response, 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error creating product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function show(Product $product)
    {
        try {
            $product->load([
                'category:id,name',
                'vendor:id,name,slug,email,logo,is_active',
                'images',
                'auction:id,product_id,vendor_id,start_time,end_time,starting_price,current_price,status'
            ]);

            return new ProductResource($product);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving product details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'image_url' => 'nullable|url',
            'image_urls' => 'nullable|array|max:10',
            'image_urls.*' => 'url',
            'replace_images' => 'boolean', // Whether to replace all images or add to existing
        ]);

        $user = auth()->user();
        $vendor = $user->vendor;

        // Allow access if user is admin OR if user is the vendor who owns the product
        if (!$user->isAdmin() && (!$vendor || $product->vendor_id !== $vendor->id)) {
            return response()->json([
                'message' => 'You are not authorized to update this product.'
            ], 403);
        }

        try {
            $product->update([
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
            ]);

            // Handle images
            $processedImages = [];
            $imageErrors = [];
            $featuredImagePath = null;
            $replaceImages = $request->input('replace_images', false);

            // Check if we have any image operations to perform
            $hasImageUrl = $request->has('image_url') && !empty($request->image_url);
            $hasImageUrls = $request->has('image_urls') && !empty($request->image_urls);

            if ($hasImageUrl || $hasImageUrls) {
                // If replace_images is true, delete existing images first
                if ($replaceImages) {
                    foreach ($product->images as $image) {
                        // Delete main image
                        $this->imageService->deleteImage($image->image_path);

                        // Delete thumbnail if exists
                        $pathInfo = pathinfo($image->image_path);
                        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
                        $this->imageService->deleteImage($thumbnailPath);

                        $image->delete();
                    }

                    // Clear the featured image if we're replacing all images
                    $product->update(['image' => null]);
                }

                // Step 1: Handle dedicated featured image if provided
                if ($hasImageUrl) {
                    \Log::info('Processing dedicated featured image for update', [
                        'product_id' => $product->id,
                        'image_url' => $request->image_url
                    ]);

                    $featuredMoveResult = $this->imageService->moveImagesToProduct(
                        [$request->image_url],
                        $product->id
                    );

                    if (!empty($featuredMoveResult['moved'])) {
                        $featuredImagePath = $featuredMoveResult['moved'][0]['new_path'];
                        \Log::info('Featured image processed for update', ['path' => $featuredImagePath]);
                    } else {
                        $imageErrors = array_merge($imageErrors, $featuredMoveResult['errors']);
                    }
                }

                // Step 2: Handle additional product images if provided
                if ($hasImageUrls) {
                    \Log::info('Processing additional product images for update', [
                        'product_id' => $product->id,
                        'image_urls' => $request->image_urls
                    ]);

                    // Move new images from temporary storage to product storage
                    $moveResult = $this->imageService->moveImagesToProduct(
                        $request->image_urls,
                        $product->id
                    );

                    // Save image records to database
                    foreach ($moveResult['moved'] as $index => $imageInfo) {
                        $productImage = $product->images()->create([
                            'image_path' => $imageInfo['new_path'],
                            'original_name' => basename($imageInfo['new_path']),
                            'file_size' => null,
                            'mime_type' => null,
                        ]);

                        $processedImages[] = [
                            'id' => $productImage->id,
                            'url' => $imageInfo['url'],
                            'thumbnail_url' => $imageInfo['thumbnail_url'],
                        ];

                        // If no dedicated featured image was provided, use the first additional image
                        if (!$featuredImagePath && $index === 0) {
                            $featuredImagePath = $imageInfo['new_path'];
                            \Log::info('Using first additional image as featured image for update', [
                                'path' => $featuredImagePath
                            ]);
                        }
                    }

                    $imageErrors = array_merge($imageErrors, $moveResult['errors']);
                }

                // Step 3: Set the featured image
                if ($featuredImagePath) {
                    $shouldUpdateFeatured = false;
                    $updateReason = '';

                    if ($hasImageUrl) {
                        // If a dedicated featured image was provided, always use it
                        $shouldUpdateFeatured = true;
                        $updateReason = 'dedicated_image_provided';
                    } elseif ($replaceImages) {
                        // If replacing all images, use the first new image as featured
                        $shouldUpdateFeatured = true;
                        $updateReason = 'replace_mode_first_image';
                    } elseif (!$product->image) {
                        // If adding images and no featured image exists, set the first one
                        $shouldUpdateFeatured = true;
                        $updateReason = 'no_existing_featured_image';
                    }

                    if ($shouldUpdateFeatured) {
                        $product->update(['image' => $featuredImagePath]);
                        \Log::info('Updated featured image', [
                            'product_id' => $product->id,
                            'featured_image' => $featuredImagePath,
                            'source' => $hasImageUrl ? 'dedicated' : 'first_additional',
                            'mode' => $replaceImages ? 'replace' : 'add',
                            'reason' => $updateReason
                        ]);
                    } else {
                        \Log::info('Featured image not updated', [
                            'product_id' => $product->id,
                            'existing_featured_image' => $product->image,
                            'new_featured_path' => $featuredImagePath,
                            'reason' => 'existing_featured_image_preserved_in_add_mode'
                        ]);
                    }
                }
            }

            $product->load(['category', 'vendor', 'images']);

            $response = [
                'message' => 'Product updated successfully',
                'data' => new ProductResource($product)
            ];

            if (!empty($processedImages)) {
                $response['processed_images'] = $processedImages;
            }

            if (!empty($imageErrors)) {
                $response['image_errors'] = $imageErrors;
            }

            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function destroy(Product $product)
    {
        $user = auth()->user();
        $vendor = $user->vendor;

        // Allow access if user is admin OR if user is the vendor who owns the product
        if (!$user->isAdmin() && (!$vendor || $product->vendor_id !== $vendor->id)) {
            return response()->json([
                'message' => 'You are not authorized to delete this product.'
            ], 403);
        }

        // Delete associated images
        foreach ($product->images as $image) {
            // Delete main image
            $this->imageService->deleteImage($image->image_path);

            // Delete thumbnail if exists
            $pathInfo = pathinfo($image->image_path);
            $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
            $this->imageService->deleteImage($thumbnailPath);

            $image->delete();
        }

        $product->delete();

        return response()->json([
            'message' => 'Product deleted successfully'
        ]);
    }

    /**
     * Display products created by the authenticated user's vendor.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function myProducts(Request $request)
    {
        try {
            $user = $request->user();
            $vendor = $user->vendor;

            if (!$vendor) {
                return response()->json([
                    'message' => 'You must be a vendor to view your products.',
                    'data' => []
                ], 403);
            }

            // Validate filter parameters
            $request->validate([
                'category_id' => 'nullable|integer|exists:categories,id',
                'search' => 'nullable|string|max:255',
                'has_auction' => 'nullable|boolean',
                'status' => 'nullable|string|in:active,inactive', // For future use
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            // Get products created by the authenticated user's vendor
            $query = Product::where('vendor_id', $vendor->id)
                ->with([
                    'category:id,name',
                    'vendor:id,name,slug,email,logo,is_active',
                    'images',
                    'auction:id,product_id,vendor_id,start_time,end_time,starting_price,current_price,status'
                ]);

            // Apply filters
            $query->when($request->filled('category_id'), function ($q) use ($request) {
                return $q->where('category_id', $request->category_id);
            });

            // Search in name and description
            $query->when($request->filled('search'), function ($q) use ($request) {
                $search = $request->search;
                return $q->where(function ($query) use ($search) {
                    $query->where('name', 'like', '%' . $search . '%')
                          ->orWhere('description', 'like', '%' . $search . '%');
                });
            });

            // Filter by auction existence
            $query->when($request->filled('has_auction'), function ($q) use ($request) {
                if ($request->boolean('has_auction')) {
                    return $q->whereHas('auction');
                } else {
                    return $q->whereDoesntHave('auction');
                }
            });

            // Apply status filter if provided (for future enhancement)
            $query->when($request->filled('status'), function ($q) use ($request) {
                // This can be extended when product status field is added
                // return $q->where('status', $request->status);
            });

            // Default ordering
            $query->orderBy('created_at', 'desc');

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $products = $query->paginate($perPage);

            return ProductResource::collection($products);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving your products',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
