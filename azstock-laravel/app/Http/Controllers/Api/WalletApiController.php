<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\WalletResource;
use App\Http\Resources\WalletTransactionResource;
use App\Http\Resources\WalletHoldResource;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\WalletHold;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WalletApiController extends Controller
{
    /**
     * Display the authenticated user's wallet.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        $user = auth()->user();
        $wallet = $user->getWallet();

        // Count active holds
        $activeHoldsCount = $wallet->activeHolds()->count();

        return (new WalletResource($wallet))
            ->additional([
                'active_holds_count' => $activeHoldsCount
            ]);
    }

    /**
     * Deposit funds into the authenticated user's wallet.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deposit(Request $request)
    {
        // Check if user is verified (either buyer or vendor can deposit)
        $user = $request->user();
        if (!$user->canBuy() && !$user->canSell()) {
            return response()->json([
                'message' => 'You must be verified to perform wallet operations.',
                'verification_required' => true,
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string',
            'payment_details' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = auth()->user();
        $wallet = $user->getWallet();

        // In a real application, you would process the payment here
        // For now, we'll just simulate a successful payment

        $transaction = $wallet->deposit(
            $request->amount,
            'Deposit via ' . $request->payment_method,
            $request->payment_details
        );

        return (new WalletTransactionResource($transaction))
            ->additional([
                'message' => 'Deposit successful',
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ]);
    }

    /**
     * Withdraw funds from the authenticated user's wallet.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function withdraw(Request $request)
    {
        // Check if user is verified (either buyer or vendor can withdraw)
        $user = $request->user();
        if (!$user->canBuy() && !$user->canSell()) {
            return response()->json([
                'message' => 'You must be verified to perform wallet operations.',
                'verification_required' => true,
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'withdrawal_method' => 'required|string',
            'withdrawal_details' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = auth()->user();
        $wallet = $user->getWallet();

        // Check if user has enough available balance
        if ($wallet->available_balance < $request->amount) {
            return response()->json([
                'message' => 'Insufficient funds',
                'available_balance' => $wallet->available_balance,
                'requested_amount' => $request->amount
            ], 400);
        }

        // In a real application, you would process the withdrawal here
        // For now, we'll just simulate a successful withdrawal

        $transaction = $wallet->withdraw(
            $request->amount,
            'Withdrawal via ' . $request->withdrawal_method,
            $request->withdrawal_details
        );

        if (!$transaction) {
            return response()->json([
                'message' => 'Withdrawal failed'
            ], 400);
        }

        return (new WalletTransactionResource($transaction))
            ->additional([
                'message' => 'Withdrawal successful',
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ]);
    }

    /**
     * Display the authenticated user's wallet transactions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function transactions(Request $request)
    {
        $user = auth()->user();
        $wallet = $user->getWallet();

        $type = $request->input('type');
        $perPage = $request->input('per_page', 15);

        $query = WalletTransaction::where('wallet_id', $wallet->id)
            ->orderBy('created_at', 'desc');

        // Filter by type if provided
        if ($type) {
            $query->where('type', $type);
        }

        $transactions = $query->paginate($perPage);

        return WalletTransactionResource::collection($transactions)
            ->additional([
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ]);
    }

    /**
     * Display the authenticated user's wallet holds.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function holds(Request $request)
    {
        $user = auth()->user();
        $wallet = $user->getWallet();

        $status = $request->input('status', 'active');
        $perPage = $request->input('per_page', 15);

        $query = WalletHold::where('wallet_id', $wallet->id);

        // Filter by status
        if ($status === 'active') {
            $query->where('status', 'active');
        } elseif ($status === 'released') {
            $query->where('status', 'released');
        } elseif ($status === 'applied') {
            $query->where('status', 'applied');
        }

        $holds = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return WalletHoldResource::collection($holds)
            ->additional([
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ]);
    }
}
