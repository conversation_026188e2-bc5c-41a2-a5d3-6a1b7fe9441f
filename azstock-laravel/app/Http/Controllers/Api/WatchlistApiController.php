<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Watchlist;
use App\Models\Auction;
use App\Http\Resources\AuctionResource;
use Illuminate\Http\Request;

class WatchlistApiController extends Controller
{
    /**
     * Display the user's watchlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            
            // Get user's watchlist with auction details
            $watchlistItems = Watchlist::where('user_id', $user->id)
                ->with([
                    'auction.vendor',
                    'auction.category',
                    'auction.auctionItems.images'
                ])
                ->orderBy('created_at', 'desc')
                ->paginate(15);

            return response()->json([
                'data' => $watchlistItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'auction' => new AuctionResource($item->auction),
                        'added_at' => $item->created_at,
                    ];
                }),
                'pagination' => [
                    'current_page' => $watchlistItems->currentPage(),
                    'last_page' => $watchlistItems->lastPage(),
                    'per_page' => $watchlistItems->perPage(),
                    'total' => $watchlistItems->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving watchlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add an auction to the user's watchlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'auction_id' => 'required|exists:auctions,id',
        ]);

        try {
            $user = $request->user();
            $auctionId = $request->auction_id;

            // Check if auction is already in watchlist
            $existingItem = Watchlist::where('user_id', $user->id)
                ->where('auction_id', $auctionId)
                ->first();

            if ($existingItem) {
                return response()->json([
                    'message' => 'Auction is already in your watchlist'
                ], 409);
            }

            // Add to watchlist
            $watchlistItem = Watchlist::create([
                'user_id' => $user->id,
                'auction_id' => $auctionId,
            ]);

            // Load the auction details
            $watchlistItem->load([
                'auction.vendor',
                'auction.category',
                'auction.auctionItems.images'
            ]);

            return response()->json([
                'message' => 'Auction added to watchlist successfully',
                'data' => [
                    'id' => $watchlistItem->id,
                    'auction' => new AuctionResource($watchlistItem->auction),
                    'added_at' => $watchlistItem->created_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error adding auction to watchlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove an auction from the user's watchlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $auctionId
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $auctionId)
    {
        try {
            $user = $request->user();

            $watchlistItem = Watchlist::where('user_id', $user->id)
                ->where('auction_id', $auctionId)
                ->first();

            if (!$watchlistItem) {
                return response()->json([
                    'message' => 'Auction not found in your watchlist'
                ], 404);
            }

            $watchlistItem->delete();

            return response()->json([
                'message' => 'Auction removed from watchlist successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error removing auction from watchlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if an auction is in the user's watchlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $auctionId
     * @return \Illuminate\Http\Response
     */
    public function check(Request $request, $auctionId)
    {
        try {
            $user = $request->user();

            $isInWatchlist = Watchlist::where('user_id', $user->id)
                ->where('auction_id', $auctionId)
                ->exists();

            return response()->json([
                'is_in_watchlist' => $isInWatchlist
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error checking watchlist status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get watchlist statistics for the user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function stats(Request $request)
    {
        try {
            $user = $request->user();

            $totalItems = Watchlist::where('user_id', $user->id)->count();
            
            $activeAuctions = Watchlist::where('user_id', $user->id)
                ->whereHas('auction', function ($query) {
                    $query->where('status', 'active')
                          ->where('end_time', '>', now());
                })
                ->count();

            $endingSoon = Watchlist::where('user_id', $user->id)
                ->whereHas('auction', function ($query) {
                    $query->where('status', 'active')
                          ->where('end_time', '>', now())
                          ->where('end_time', '<=', now()->addHours(24));
                })
                ->count();

            return response()->json([
                'total_items' => $totalItems,
                'active_auctions' => $activeAuctions,
                'ending_soon' => $endingSoon,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving watchlist statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
