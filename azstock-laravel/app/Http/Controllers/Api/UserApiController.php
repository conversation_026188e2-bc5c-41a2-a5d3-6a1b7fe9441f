<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserApiController extends Controller
{
    /**
     * Display the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        $user = $request->user();
        $user->load('vendor');

        return new UserResource($user);
    }

    /**
     * Update the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $user = $request->user();

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $user->id,
            'current_password' => 'required_with:password|string',
            'password' => 'sometimes|required|string|min:8|confirmed',
        ]);

        // Check current password if trying to update password
        if ($request->has('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'message' => 'The provided current password is incorrect.'
                ], 422);
            }
        }

        $data = $request->only(['name', 'email']);

        // Update password if provided
        if ($request->has('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return (new UserResource($user))
            ->additional(['message' => 'Profile updated successfully']);
    }

    /**
     * Display a listing of all users.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Check if user is admin
        $user = $request->user();
        if (!$user->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Validate filter parameters
        $request->validate([
            'role' => 'nullable|string|in:buyer,vendor,admin',
            'is_active' => 'nullable|boolean',
            'search' => 'nullable|string|max:255',
            'created_after' => 'nullable|date',
            'created_before' => 'nullable|date|after_or_equal:created_after',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        // Build query
        $query = User::query();

        // Load relationships
        $query->with(['vendor', 'wallet']);

        // Apply filters using when() for conditional filtering
        $query->when($request->filled('role'), function ($q) use ($request) {
            return $q->where('role', $request->role);
        });

        // Filter by active status (assuming there's an is_active field or using email_verified_at)
        $query->when($request->filled('is_active'), function ($q) use ($request) {
            if ($request->boolean('is_active')) {
                return $q->whereNotNull('email_verified_at');
            } else {
                return $q->whereNull('email_verified_at');
            }
        });

        // Search in name and email
        $query->when($request->filled('search'), function ($q) use ($request) {
            $search = $request->search;
            return $q->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                      ->orWhere('email', 'like', '%' . $search . '%');
            });
        });

        // Filter by registration date range
        $query->when($request->filled('created_after'), function ($q) use ($request) {
            return $q->where('created_at', '>=', $request->created_after);
        });

        $query->when($request->filled('created_before'), function ($q) use ($request) {
            return $q->where('created_at', '<=', $request->created_before . ' 23:59:59');
        });

        // Default ordering - newest first
        $query->orderBy('created_at', 'desc');

        // Log complex queries for performance monitoring
        if ($request->filled(['search', 'created_after', 'created_before'])) {
            \Log::info('Complex user filter query executed', [
                'filters' => $request->only(['role', 'is_active', 'search', 'created_after', 'created_before']),
                'admin_user_id' => $user->id
            ]);
        }

        // Add pagination with validation
        $perPage = min($request->input('per_page', 15), 100);
        $users = $query->paginate($perPage);

        return UserResource::collection($users);
    }

    /**
     * Display the specified user.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function showUser(Request $request, User $user)
    {
        // Check if user is admin
        $currentUser = $request->user();
        if (!$currentUser->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Load relationships
        $user->load('vendor', 'wallet');

        return new UserResource($user);
    }

    /**
     * Create a new user.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Check if user is admin
        $currentUser = $request->user();
        if (!$currentUser->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'sometimes|string|in:buyer,vendor,admin', // Admin can create any role
            'roles' => 'sometimes|array', // Multi-role support
            'roles.*' => 'string|in:buyer,vendor,admin',
            'is_verified_buyer' => 'sometimes|boolean',
            'is_verified_vendor' => 'sometimes|boolean',
            'is_verified_admin' => 'sometimes|boolean',
        ]);

        // Determine roles to assign
        $rolesToAssign = [];
        if ($request->has('roles') && is_array($request->roles)) {
            $rolesToAssign = $request->roles;
        } elseif ($request->has('role')) {
            $rolesToAssign = [$request->role];
        } else {
            $rolesToAssign = ['buyer']; // Default role
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $rolesToAssign[0], // Keep first role for backward compatibility
            // Admin can set verification status during creation
            'is_verified_buyer' => $request->get('is_verified_buyer', false),
            'is_verified_vendor' => $request->get('is_verified_vendor', false),
            'is_verified_admin' => $request->get('is_verified_admin', false),
        ]);

        // Assign Spatie roles for multi-role support
        foreach ($rolesToAssign as $role) {
            $user->assignRole($role);
        }

        // If user has vendor role, create vendor profile
        if (in_array('vendor', $rolesToAssign)) {
            Vendor::create([
                'user_id' => $user->id,
                'name' => $user->name,
                'slug' => \Illuminate\Support\Str::slug($user->name . '-' . $user->id),
                'email' => $user->email,
                'is_active' => true,
            ]);
        }

        // Load relationships
        $user->load('vendor', 'wallet');

        return (new UserResource($user))
            ->additional(['message' => 'User created successfully'])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Update the specified user.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function updateUser(Request $request, User $user)
    {
        // Check if user is admin
        $currentUser = $request->user();
        if (!$currentUser->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $user->id,
            'role' => 'sometimes|string|in:buyer,vendor,admin', // Single role for backward compatibility
            'roles' => 'sometimes|array', // Multi-role support
            'roles.*' => 'string|in:buyer,vendor,admin',
            'password' => 'sometimes|required|string|min:8',
            'is_verified_buyer' => 'sometimes|boolean',
            'is_verified_vendor' => 'sometimes|boolean',
            'is_verified_admin' => 'sometimes|boolean',
        ]);

        // Determine roles to assign
        $rolesToAssign = null;
        if ($request->has('roles') && is_array($request->roles)) {
            $rolesToAssign = $request->roles;
        } elseif ($request->has('role')) {
            $rolesToAssign = [$request->role];
        }

        // Prepare basic data for update
        $data = $request->only(['name', 'email']);

        // Update password if provided
        if ($request->has('password')) {
            $data['password'] = Hash::make($request->password);
        }

        // Update verification status if provided
        if ($request->has('is_verified_buyer')) {
            $data['is_verified_buyer'] = $request->is_verified_buyer;
        }
        if ($request->has('is_verified_vendor')) {
            $data['is_verified_vendor'] = $request->is_verified_vendor;
        }
        if ($request->has('is_verified_admin')) {
            $data['is_verified_admin'] = $request->is_verified_admin;
        }

        // Handle role updates
        if ($rolesToAssign !== null) {
            // Set primary role for backward compatibility
            $data['role'] = $rolesToAssign[0] ?? 'buyer';

            // Check if user needs vendor profile
            $needsVendorProfile = in_array('vendor', $rolesToAssign) && !$user->vendor;
        }

        // Update user data
        $user->update($data);

        // Update Spatie roles if roles were provided
        if ($rolesToAssign !== null) {
            // Remove all current roles and assign new ones
            $user->syncRoles($rolesToAssign);

            // Create vendor profile if needed
            if (isset($needsVendorProfile) && $needsVendorProfile) {
                Vendor::create([
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'slug' => \Illuminate\Support\Str::slug($user->name . '-' . $user->id),
                    'email' => $user->email,
                    'is_active' => true,
                ]);
            }
        }

        // Reload relationships
        $user->load('vendor', 'wallet', 'roles');

        return (new UserResource($user))
            ->additional(['message' => 'User updated successfully']);
    }

    /**
     * Remove the specified user.
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, User $user)
    {
        // Check if user is admin
        $currentUser = $request->user();
        if (!$currentUser->isAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        // Prevent admin from deleting themselves
        if ($user->id === $currentUser->id) {
            return response()->json([
                'message' => 'You cannot delete your own account.'
            ], 400);
        }

        // Delete the user
        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Verify a user for specific role(s).
     * Admin only endpoint.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function verifyUser(Request $request, User $user)
    {
        // Check if user is admin
        $currentUser = $request->user();
        if (!$currentUser->canAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $request->validate([
            'role' => 'required|string|in:buyer,vendor,admin',
            'verified' => 'required|boolean',
        ]);

        $role = $request->role;
        $verified = $request->verified;

        if ($verified) {
            $user->verifyForRole($role);
            $message = "User verified for {$role} role successfully";
        } else {
            $user->unverifyForRole($role);
            $message = "User unverified for {$role} role successfully";
        }

        // Load relationships
        $user->load('vendor', 'wallet', 'roles');

        return (new UserResource($user))
            ->additional(['message' => $message]);
    }

    /**
     * Get user verification status.
     * Admin only endpoint.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function getVerificationStatus(User $user)
    {
        // Check if user is admin
        $currentUser = request()->user();
        if (!$currentUser->canAdmin()) {
            return response()->json([
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        return response()->json([
            'user_id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'verification_status' => $user->getVerificationStatus(),
            'roles' => $user->getUserRoles(),
        ]);
    }
}
