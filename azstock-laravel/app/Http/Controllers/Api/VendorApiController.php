<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\VendorResource;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class VendorApiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $vendors = Vendor::with(['user'])->get();

        return VendorResource::collection($vendors);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:vendors,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'name', 'email', 'phone', 'address', 'description', 'website'
        ]);

        // Generate slug from name
        $data['slug'] = Str::slug($request->name);

        // Set user_id to authenticated user
        $data['user_id'] = auth()->id();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $data['logo'] = $request->file('logo')->store('uploads/vendors/logos', 'public');
        }

        $vendor = Vendor::create($data);

        return (new VendorResource($vendor))
            ->additional(['message' => 'Vendor created successfully'])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function show(Vendor $vendor)
    {
        $vendor->load(['auctions', 'user']);

        return new VendorResource($vendor);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Vendor $vendor)
    {
        // Check if user is authorized to update this vendor
        $user = auth()->user();
        if ($user->id !== $vendor->user_id && !$user->isAdmin()) {
            return response()->json([
                'message' => 'You are not authorized to update this vendor.'
            ], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:vendors,email,' . $vendor->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'sometimes|boolean',
        ]);

        $data = $request->only([
            'name', 'email', 'phone', 'address', 'description', 'website', 'is_active'
        ]);

        // Update slug if name is changed
        if ($request->has('name')) {
            $data['slug'] = Str::slug($request->name);
        }

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($vendor->logo) {
                Storage::disk('public')->delete($vendor->logo);
            }

            $data['logo'] = $request->file('logo')->store('uploads/vendors/logos', 'public');
        }

        $vendor->update($data);

        return (new VendorResource($vendor))
            ->additional(['message' => 'Vendor updated successfully']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function destroy(Vendor $vendor)
    {
        // Check if user is authorized to delete this vendor
        $user = auth()->user();
        if ($user->id !== $vendor->user_id && !$user->isAdmin()) {
            return response()->json([
                'message' => 'You are not authorized to delete this vendor.'
            ], 403);
        }

        // Delete logo if exists
        if ($vendor->logo) {
            Storage::disk('public')->delete($vendor->logo);
        }

        $vendor->delete();

        return response()->json([
            'message' => 'Vendor deleted successfully'
        ]);
    }
}
