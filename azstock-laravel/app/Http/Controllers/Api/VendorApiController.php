<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\VendorResource;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class VendorApiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $vendors = Vendor::with(['user'])->get();

        return VendorResource::collection($vendors);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:vendors,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'name', 'email', 'phone', 'address', 'description', 'website'
        ]);

        // Generate slug from name
        $data['slug'] = Str::slug($request->name);

        // Set user_id to authenticated user
        $data['user_id'] = auth()->id();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $data['logo'] = $request->file('logo')->store('uploads/vendors/logos', 'public');
        }

        $vendor = Vendor::create($data);

        return (new VendorResource($vendor))
            ->additional(['message' => 'Vendor created successfully'])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Display the specified resource (public view for buyers).
     *
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function show(Vendor $vendor)
    {
        // Load relationships for public view
        $vendor->load([
            'auctions' => function ($query) {
                $query->where('status', 'active')
                      ->orWhere('status', 'completed')
                      ->with(['category', 'auctionItems.images'])
                      ->orderBy('created_at', 'desc')
                      ->limit(10);
            },
            'reviews' => function ($query) {
                $query->with('user:id,name')
                      ->orderBy('created_at', 'desc')
                      ->limit(10);
            }
        ]);

        // Add computed statistics
        $vendor->loadCount([
            'auctions as total_auctions_count',
            'auctions as active_auctions_count' => function ($query) {
                $query->where('status', 'active');
            },
            'auctions as completed_auctions_count' => function ($query) {
                $query->where('status', 'completed');
            },
            'reviews as total_reviews_count'
        ]);

        // Calculate average rating
        $avgRating = $vendor->reviews()->avg('rating');
        $vendor->average_rating = $avgRating ? round($avgRating, 1) : null;

        return new VendorResource($vendor);
    }

    /**
     * Get vendor profile for dashboard (private view for vendor).
     *
     * @return \Illuminate\Http\Response
     */
    public function profile()
    {
        $user = auth()->user();

        if (!$user->isVendor()) {
            return response()->json([
                'message' => 'You are not authorized to access vendor profile.'
            ], 403);
        }

        $vendor = $user->vendor;

        if (!$vendor) {
            return response()->json([
                'message' => 'Vendor profile not found. Please create your vendor profile first.'
            ], 404);
        }

        // Load all relationships for private view
        $vendor->load([
            'auctions' => function ($query) {
                $query->with(['category', 'auctionItems.images'])
                      ->orderBy('created_at', 'desc');
            },
            'reviews' => function ($query) {
                $query->with('user:id,name')
                      ->orderBy('created_at', 'desc');
            },
            'user'
        ]);

        return new VendorResource($vendor);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Vendor $vendor)
    {
        // Check if user is authorized to update this vendor
        $user = auth()->user();
        if ($user->id !== $vendor->user_id && !$user->isAdmin()) {
            return response()->json([
                'message' => 'You are not authorized to update this vendor.'
            ], 403);
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:vendors,email,' . $vendor->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'sometimes|boolean',
        ]);

        $data = $request->only([
            'name', 'email', 'phone', 'address', 'description', 'website', 'is_active'
        ]);

        // Update slug if name is changed
        if ($request->has('name')) {
            $data['slug'] = Str::slug($request->name);
        }

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($vendor->logo) {
                Storage::disk('public')->delete($vendor->logo);
            }

            $data['logo'] = $request->file('logo')->store('uploads/vendors/logos', 'public');
        }

        $vendor->update($data);

        return (new VendorResource($vendor))
            ->additional(['message' => 'Vendor updated successfully']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function destroy(Vendor $vendor)
    {
        // Check if user is authorized to delete this vendor
        $user = auth()->user();
        if ($user->id !== $vendor->user_id && !$user->isAdmin()) {
            return response()->json([
                'message' => 'You are not authorized to delete this vendor.'
            ], 403);
        }

        // Delete logo if exists
        if ($vendor->logo) {
            Storage::disk('public')->delete($vendor->logo);
        }

        $vendor->delete();

        return response()->json([
            'message' => 'Vendor deleted successfully'
        ]);
    }

    /**
     * Get vendor statistics.
     *
     * @param  \App\Models\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function statistics(Vendor $vendor)
    {
        // Calculate comprehensive statistics
        $stats = [
            'auctions' => [
                'total' => $vendor->auctions()->count(),
                'active' => $vendor->auctions()->where('status', 'active')->count(),
                'completed' => $vendor->auctions()->where('status', 'completed')->count(),
                'draft' => $vendor->auctions()->where('status', 'draft')->count(),
                'cancelled' => $vendor->auctions()->where('status', 'cancelled')->count(),
            ],
            'revenue' => [
                'total' => $vendor->auctions()
                    ->where('status', 'completed')
                    ->sum('current_price'),
                'this_month' => $vendor->auctions()
                    ->where('status', 'completed')
                    ->whereMonth('end_time', now()->month)
                    ->whereYear('end_time', now()->year)
                    ->sum('current_price'),
                'last_month' => $vendor->auctions()
                    ->where('status', 'completed')
                    ->whereMonth('end_time', now()->subMonth()->month)
                    ->whereYear('end_time', now()->subMonth()->year)
                    ->sum('current_price'),
            ],
            'reviews' => [
                'total' => $vendor->reviews()->count(),
                'average_rating' => round($vendor->reviews()->avg('rating') ?: 0, 1),
                'rating_distribution' => [
                    '5' => $vendor->reviews()->where('rating', 5)->count(),
                    '4' => $vendor->reviews()->where('rating', 4)->count(),
                    '3' => $vendor->reviews()->where('rating', 3)->count(),
                    '2' => $vendor->reviews()->where('rating', 2)->count(),
                    '1' => $vendor->reviews()->where('rating', 1)->count(),
                ],
            ],
            'performance' => [
                'success_rate' => $this->calculateSuccessRate($vendor),
                'average_auction_duration' => $this->calculateAverageAuctionDuration($vendor),
                'repeat_bidders' => $this->calculateRepeatBidders($vendor),
            ],
            'recent_activity' => [
                'recent_auctions' => $vendor->auctions()
                    ->with(['category', 'auctionItems.images'])
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get(),
                'recent_reviews' => $vendor->reviews()
                    ->with('user:id,name')
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get(),
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Calculate vendor success rate (completed auctions vs total auctions).
     */
    private function calculateSuccessRate(Vendor $vendor): float
    {
        $totalAuctions = $vendor->auctions()->whereIn('status', ['completed', 'cancelled'])->count();
        $completedAuctions = $vendor->auctions()->where('status', 'completed')->count();

        return $totalAuctions > 0 ? round(($completedAuctions / $totalAuctions) * 100, 1) : 0;
    }

    /**
     * Calculate average auction duration in days.
     */
    private function calculateAverageAuctionDuration(Vendor $vendor): float
    {
        $auctions = $vendor->auctions()
            ->whereIn('status', ['completed', 'cancelled'])
            ->get(['start_time', 'end_time']);

        if ($auctions->isEmpty()) {
            return 0;
        }

        $totalDuration = $auctions->sum(function ($auction) {
            return \Carbon\Carbon::parse($auction->start_time)
                ->diffInDays(\Carbon\Carbon::parse($auction->end_time));
        });

        return round($totalDuration / $auctions->count(), 1);
    }

    /**
     * Calculate number of repeat bidders.
     */
    private function calculateRepeatBidders(Vendor $vendor): int
    {
        // Get all bids on this vendor's auctions
        $bidderCounts = \DB::table('bids')
            ->join('auctions', 'bids.auction_id', '=', 'auctions.id')
            ->where('auctions.vendor_id', $vendor->id)
            ->groupBy('bids.user_id')
            ->havingRaw('COUNT(DISTINCT auctions.id) > 1')
            ->count();

        return $bidderCounts;
    }
}
