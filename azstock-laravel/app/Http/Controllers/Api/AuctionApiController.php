<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionResource;
use App\Models\Auction;
use App\Models\Product;
use Illuminate\Http\Request;

class AuctionApiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            // Validate filter parameters
            $request->validate([
                'status' => 'nullable|string|in:active,ended,upcoming',
                'category_id' => 'nullable|integer|exists:categories,id',
                'vendor_id' => 'nullable|integer|exists:vendors,id',
                'search' => 'nullable|string|max:255',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
                'ending_soon' => 'nullable|boolean',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $query = Auction::query();

            // Add basic relationships with select to limit data
            $query->with([
                'product:id,name,description,image,category_id,vendor_id',
                'product.category:id,name',
                'product.images',
                'vendor:id,name,slug,email,logo,is_active'
            ]);

            // Apply filters using when() for conditional filtering
            $query->when($request->filled('status'), function ($q) use ($request) {
                return $q->where('status', $request->status);
            });

            $query->when($request->filled('vendor_id'), function ($q) use ($request) {
                return $q->where('vendor_id', $request->vendor_id);
            });

            // Filter by product category
            $query->when($request->filled('category_id'), function ($q) use ($request) {
                return $q->whereHas('product', function ($productQuery) use ($request) {
                    $productQuery->where('category_id', $request->category_id);
                });
            });

            // Search in product name and description
            $query->when($request->filled('search'), function ($q) use ($request) {
                $search = $request->search;
                return $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', '%' . $search . '%')
                                 ->orWhere('description', 'like', '%' . $search . '%');
                });
            });

            // Price filters
            $query->when($request->filled('min_price'), function ($q) use ($request) {
                return $q->where(function ($priceQuery) use ($request) {
                    $priceQuery->where('current_price', '>=', $request->min_price)
                               ->orWhere('starting_price', '>=', $request->min_price);
                });
            });

            $query->when($request->filled('max_price'), function ($q) use ($request) {
                return $q->where(function ($priceQuery) use ($request) {
                    $priceQuery->where('current_price', '<=', $request->max_price)
                               ->orWhere('starting_price', '<=', $request->max_price);
                });
            });

            // Filter for auctions ending soon (within 24 hours)
            $query->when($request->filled('ending_soon') && $request->boolean('ending_soon'), function ($q) {
                return $q->where('status', 'active')
                         ->where('end_time', '<=', now()->addHours(24));
            });

            // Default ordering - active auctions first, then by end time
            $query->orderByRaw("CASE WHEN status = 'active' THEN 1 ELSE 2 END")
                  ->orderBy('end_time', 'asc');

            // Log complex queries for performance monitoring
            if ($request->filled(['search', 'min_price', 'max_price', 'ending_soon'])) {
                \Log::info('Complex auction filter query executed', [
                    'filters' => $request->only(['status', 'category_id', 'vendor_id', 'search', 'min_price', 'max_price', 'ending_soon']),
                    'user_id' => auth()->id()
                ]);
            }

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $auctions = $query->paginate($perPage);

            return AuctionResource::collection($auctions);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving auctions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a listing of the authenticated user's auctions.
     *
     * @return \Illuminate\Http\Response
     */
    public function myAuctions(Request $request)
    {
        try {
            $vendor = auth()->user()->vendor;
            if (!$vendor) {
                return response()->json([
                    'message' => 'You do not have a vendor account.'
                ], 403);
            }

            // Validate filter parameters
            $request->validate([
                'status' => 'nullable|string|in:active,ended,upcoming',
                'category_id' => 'nullable|integer|exists:categories,id',
                'search' => 'nullable|string|max:255',
                'ending_soon' => 'nullable|boolean',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $query = Auction::where('vendor_id', $vendor->id)
                ->with([
                    'product:id,name,description,image,category_id,vendor_id',
                    'product.category:id,name',
                    'product.images'
                ]);

            // Apply filters
            $query->when($request->filled('status'), function ($q) use ($request) {
                return $q->where('status', $request->status);
            });

            // Filter by product category
            $query->when($request->filled('category_id'), function ($q) use ($request) {
                return $q->whereHas('product', function ($productQuery) use ($request) {
                    $productQuery->where('category_id', $request->category_id);
                });
            });

            // Search in product name and description
            $query->when($request->filled('search'), function ($q) use ($request) {
                $search = $request->search;
                return $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', '%' . $search . '%')
                                 ->orWhere('description', 'like', '%' . $search . '%');
                });
            });

            // Filter for auctions ending soon (within 24 hours)
            $query->when($request->filled('ending_soon') && $request->boolean('ending_soon'), function ($q) {
                return $q->where('status', 'active')
                         ->where('end_time', '<=', now()->addHours(24));
            });

            // Default ordering - active auctions first, then by end time
            $query->orderByRaw("CASE WHEN status = 'active' THEN 1 ELSE 2 END")
                  ->orderBy('end_time', 'asc');

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $auctions = $query->paginate($perPage);

            return AuctionResource::collection($auctions);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving your auctions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Check if user is verified vendor
        $user = $request->user();
        if (!$user->canSell()) {
            return response()->json([
                'message' => 'You must be a verified vendor to create auctions.',
                'verification_required' => true,
                'required_role' => 'vendor',
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        // Support both single product (legacy) and multiple products
        $request->validate([
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',

            // Legacy single product support
            'product_id' => 'nullable|exists:products,id',
            'starting_price' => 'nullable|numeric|min:0',
            'reserve_price' => 'nullable|numeric|min:0',

            // New multiple products support
            'products' => 'nullable|array|min:1',
            'products.*.product_id' => 'required_with:products|exists:products,id',
            'products.*.quantity' => 'required_with:products|integer|min:1',
            'products.*.starting_price' => 'required_with:products|numeric|min:0',
            'products.*.reserve_price' => 'nullable|numeric|min:0',
        ]);

        $vendor = auth()->user()->vendor;
        if (!$vendor) {
            return response()->json([
                'message' => 'You do not have a vendor account.'
            ], 403);
        }

        // Determine if this is single or multiple product auction
        $isMultiProduct = $request->has('products') && is_array($request->products);
        $isSingleProduct = $request->has('product_id');

        if (!$isMultiProduct && !$isSingleProduct) {
            return response()->json([
                'message' => 'Either product_id or products array must be provided.'
            ], 400);
        }

        // Validate product ownership
        if ($isSingleProduct) {
            $product = Product::findOrFail($request->product_id);
            if ($product->vendor_id !== $vendor->id) {
                return response()->json([
                    'message' => 'You can only create auctions for your own products.'
                ], 403);
            }
        }

        if ($isMultiProduct) {
            $productIds = collect($request->products)->pluck('product_id');
            $products = Product::whereIn('id', $productIds)->get();

            foreach ($products as $product) {
                if ($product->vendor_id !== $vendor->id) {
                    return response()->json([
                        'message' => "You can only create auctions for your own products. Product '{$product->name}' does not belong to you."
                    ], 403);
                }
            }
        }

        // Create the auction
        $auctionData = [
            'vendor_id' => $vendor->id,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'status' => 'active',
        ];

        // For single product (legacy support)
        if ($isSingleProduct) {
            $auctionData['product_id'] = $request->product_id;
            $auctionData['starting_price'] = $request->starting_price;
            $auctionData['current_price'] = $request->starting_price;
            $auctionData['reserve_price'] = $request->reserve_price;
        } else {
            // For multiple products
            $auctionData['title'] = $request->title;
            $auctionData['description'] = $request->description;
        }

        $auction = Auction::create($auctionData);

        // Attach products for multiple product auctions
        if ($isMultiProduct) {
            foreach ($request->products as $productData) {
                $auction->products()->attach($productData['product_id'], [
                    'quantity' => $productData['quantity'],
                    'starting_price' => $productData['starting_price'],
                    'current_price' => $productData['starting_price'],
                    'reserve_price' => $productData['reserve_price'] ?? null,
                ]);
            }
        }

        // Load relationships
        if ($isSingleProduct) {
            $auction->load([
                'product:id,name,description,image,category_id,vendor_id',
                'product.images',
                'vendor:id,name,slug,email,logo,is_active'
            ]);
        } else {
            $auction->load([
                'products:id,name,description,image,category_id,vendor_id',
                'products.images',
                'vendor:id,name,slug,email,logo,is_active'
            ]);
        }

        return (new AuctionResource($auction))
            ->additional(['message' => 'Auction created successfully'])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function show(Auction $auction)
    {
        try {
            $auction->load([
                'product:id,name,description,image,category_id,vendor_id',
                'product.images',
                'vendor:id,name,slug,email,logo,is_active',
                'bids' => function($query) {
                    $query->latest()->limit(10)->select('id', 'auction_id', 'user_id', 'bid_amount', 'created_at');
                },
                'bids.user:id,name,email'
            ]);

            return new AuctionResource($auction);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving auction details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Auction $auction)
    {
        $request->validate([
            'start_time' => 'sometimes|required|date',
            'end_time' => 'sometimes|required|date|after:start_time',
            'starting_price' => 'sometimes|required|numeric|min:0',
            'reserve_price' => 'nullable|numeric|min:0',
            'status' => 'sometimes|required|in:active,ended',
        ]);

        $user = auth()->user();
        $vendor = $user->vendor;

        // Allow access if user is admin OR if user is the vendor who owns the auction
        if (!$user->isAdmin() && (!$vendor || $auction->vendor_id !== $vendor->id)) {
            return response()->json([
                'message' => 'You are not authorized to update this auction.'
            ], 403);
        }

        // Only allow updating if no bids have been placed
        if ($auction->bids()->count() > 0 && ($request->has('starting_price') || $request->has('start_time'))) {
            return response()->json([
                'message' => 'Cannot update starting price or start time after bids have been placed.'
            ], 400);
        }

        $auction->update($request->only([
            'start_time', 'end_time', 'starting_price', 'reserve_price', 'status'
        ]));

        // If starting price is updated and no bids, update current price
        if ($request->has('starting_price') && $auction->bids()->count() === 0) {
            $auction->update(['current_price' => $request->starting_price]);
        }

        $auction->load([
            'product:id,name,description,image,category_id,vendor_id',
            'product.images',
            'vendor:id,name,slug,email,logo,is_active'
        ]);

        return (new AuctionResource($auction))
            ->additional(['message' => 'Auction updated successfully']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function destroy(Auction $auction)
    {
        $user = auth()->user();
        $vendor = $user->vendor;

        // Allow access if user is admin OR if user is the vendor who owns the auction
        if (!$user->isAdmin() && (!$vendor || $auction->vendor_id !== $vendor->id)) {
            return response()->json([
                'message' => 'You are not authorized to delete this auction.'
            ], 403);
        }

        // Only allow deletion if no bids have been placed
        if ($auction->bids()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete auction after bids have been placed.'
            ], 400);
        }

        $auction->delete();

        return response()->json([
            'message' => 'Auction deleted successfully'
        ]);
    }

    /**
     * End an auction manually.
     *
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function endAuction(Auction $auction)
    {
        try {
            // Check if user is authorized to end this auction
            $user = auth()->user();
            $vendor = $user->vendor;

            // Allow access if user is admin OR if user is the vendor who owns the auction
            if (!$user->isAdmin() && (!$vendor || $auction->vendor_id !== $vendor->id)) {
                return response()->json([
                    'message' => 'You are not authorized to end this auction.'
                ], 403);
            }

            // Check if auction can be ended
            if ($auction->status !== 'active') {
                return response()->json([
                    'message' => 'This auction is not active and cannot be ended.'
                ], 400);
            }

            // End the auction
            $result = $auction->end();

            if (!$result) {
                return response()->json([
                    'message' => 'Failed to end the auction. Please try again.'
                ], 500);
            }

            // Get the winning bid if there is one
            $winningBid = $auction->winningBid();

            // Load the auction with its relationships
            $auction->load([
                'product:id,name,description,image,category_id,vendor_id',
                'product.images',
                'vendor:id,name,slug,email,logo,is_active',
                'bids' => function($query) {
                    $query->latest()->limit(10)->select('id', 'auction_id', 'user_id', 'bid_amount', 'status', 'created_at');
                },
                'bids.user:id,name,email'
            ]);

            $message = $winningBid
                ? "Auction ended successfully. Won by user #{$winningBid->user_id} with bid amount {$winningBid->bid_amount}."
                : "Auction ended successfully with no winner.";

            return (new AuctionResource($auction))
                ->additional(['message' => $message]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error ending auction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process refunds for an auction.
     *
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function processRefunds(Auction $auction)
    {
        try {
            // Check if user is authorized
            $user = auth()->user();

            // Allow access if user is admin OR if user is the vendor who owns the auction
            $vendor = $user->vendor;
            if (!$user->isAdmin() && (!$vendor || $auction->vendor_id !== $vendor->id)) {
                return response()->json([
                    'message' => 'You are not authorized to process refunds for this auction.'
                ], 403);
            }

            // Check if auction is ended
            if ($auction->status !== 'ended') {
                return response()->json([
                    'message' => 'Refunds can only be processed for ended auctions.'
                ], 400);
            }

            // Get the winning bid if there is one
            $winningBid = $auction->winningBid();

            // Get all active bids that need to be refunded
            $activeBids = $auction->bids()
                ->where('status', 'active')
                ->get();

            $bidCount = $activeBids->count();

            if ($bidCount === 0) {
                return response()->json([
                    'message' => 'No active bids found that need refunds.'
                ]);
            }

            $processedBids = 0;
            $failedBids = 0;
            $refundedAmount = 0;

            foreach ($activeBids as $bid) {
                // Skip the winning bid if there is one
                if ($winningBid && $bid->id === $winningBid->id) {
                    continue;
                }

                // Determine the reason for the refund
                if ($winningBid) {
                    $reason = "Outbid by winning bid #{$winningBid->id} with amount {$winningBid->bid_amount}";
                } elseif ($auction->reserve_price && $auction->reserve_price > $bid->bid_amount) {
                    $reason = "Auction ended with no winner: reserve price of {$auction->reserve_price} not met";
                } else {
                    $reason = "Auction ended with no winner";
                }

                // Process the refund
                $result = $bid->markAsLost($reason);

                if ($result) {
                    $processedBids++;
                    $refundedAmount += $bid->bid_amount;
                } else {
                    $failedBids++;
                }
            }

            return response()->json([
                'message' => "Refunds processed successfully for auction #{$auction->id}",
                'processed_bids' => $processedBids,
                'failed_bids' => $failedBids,
                'refunded_amount' => $refundedAmount,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error processing refunds',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
