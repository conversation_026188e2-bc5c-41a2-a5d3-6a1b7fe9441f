<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\BidResource;
use App\Models\Auction;
use App\Models\Bid;
use Illuminate\Http\Request;

class BidApiController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Auction $auction)
    {
        // Check if user is verified buyer
        $user = $request->user();
        if (!$user->canBuy()) {
            return response()->json([
                'message' => 'You must be a verified buyer to place bids.',
                'verification_required' => true,
                'required_role' => 'buyer',
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        $request->validate([
            'bid_amount' => 'required|numeric|min:' . max($auction->current_price, $auction->starting_price),
        ]);

        $user = auth()->user();

        // Check if user has a wallet
        $wallet = $user->getWallet();

        // Check if user has enough available balance
        if ($wallet->available_balance < $request->bid_amount) {
            return response()->json([
                'message' => 'Insufficient funds in your wallet. Please add funds to place this bid.',
                'available_balance' => $wallet->available_balance,
                'required_amount' => $request->bid_amount
            ], 400);
        }

        // Use the placeBid method which handles all validation and wallet holds
        try {
            // Check auction status
            if ($auction->status !== 'active') {
                return response()->json([
                    'message' => 'This auction is not active.',
                    'status' => $auction->status
                ], 400);
            }

            // Check if auction has started
            if (now() < $auction->start_time) {
                return response()->json([
                    'message' => 'This auction has not started yet.',
                    'start_time' => $auction->start_time,
                    'current_time' => now()
                ], 400);
            }

            // Check if auction has ended
            if (now() > $auction->end_time) {
                return response()->json([
                    'message' => 'This auction has already ended.',
                    'end_time' => $auction->end_time,
                    'current_time' => now()
                ], 400);
            }

            // Check if bid amount is valid
            if ($request->bid_amount <= $auction->current_price) {
                return response()->json([
                    'message' => 'Bid amount must be higher than the current price.',
                    'current_price' => $auction->current_price,
                    'bid_amount' => $request->bid_amount
                ], 400);
            }

            // Place the bid
            $bid = Bid::placeBid($auction, $user, $request->bid_amount);

            if (!$bid) {
                // Check if there was an issue with the wallet
                $wallet = $user->getWallet();
                return response()->json([
                    'message' => 'Failed to place bid. There was an issue processing your bid.',
                    'wallet_balance' => [
                        'total' => $wallet->balance,
                        'available' => $wallet->available_balance,
                        'held' => $wallet->held_balance
                    ]
                ], 400);
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Exception in BidApiController::store: " . $e->getMessage());
            \Illuminate\Support\Facades\Log::error($e->getTraceAsString());

            return response()->json([
                'message' => 'An error occurred while placing your bid: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }

        // Load the wallet hold for the response
        $bid->load('walletHold');

        return (new BidResource($bid))
            ->additional([
                'message' => 'Bid placed successfully',
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Display a listing of the authenticated user's bids.
     *
     * @return \Illuminate\Http\Response
     */
    public function myBids(Request $request)
    {
        try {
            $user = auth()->user();

            // Validate filter parameters
            $request->validate([
                'status' => 'nullable|string|in:active,won,lost,cancelled',
                'auction_status' => 'nullable|string|in:active,ended,upcoming',
                'category_id' => 'nullable|integer|exists:categories,id',
                'search' => 'nullable|string|max:255',
                'min_amount' => 'nullable|numeric|min:0',
                'max_amount' => 'nullable|numeric|min:0',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $query = Bid::where('user_id', $user->id)
                ->with([
                    'auction.product.category:id,name',
                    'auction.product:id,name,description,image,category_id,vendor_id',
                    'auction.product.images',
                    'auction:id,product_id,vendor_id,start_time,end_time,starting_price,current_price,status',
                    'walletHold'
                ]);

            // Apply filters using when() for conditional filtering
            $query->when($request->filled('status'), function ($q) use ($request) {
                return $q->where('status', $request->status);
            });

            // Filter by auction status
            $query->when($request->filled('auction_status'), function ($q) use ($request) {
                return $q->whereHas('auction', function ($auctionQuery) use ($request) {
                    $auctionQuery->where('status', $request->auction_status);
                });
            });

            // Filter by product category
            $query->when($request->filled('category_id'), function ($q) use ($request) {
                return $q->whereHas('auction.product', function ($productQuery) use ($request) {
                    $productQuery->where('category_id', $request->category_id);
                });
            });

            // Search in product name and description
            $query->when($request->filled('search'), function ($q) use ($request) {
                $search = $request->search;
                return $q->whereHas('auction.product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', '%' . $search . '%')
                                 ->orWhere('description', 'like', '%' . $search . '%');
                });
            });

            // Filter by bid amount range
            $query->when($request->filled('min_amount'), function ($q) use ($request) {
                return $q->where('bid_amount', '>=', $request->min_amount);
            });

            $query->when($request->filled('max_amount'), function ($q) use ($request) {
                return $q->where('bid_amount', '<=', $request->max_amount);
            });

            // Filter by date range
            $query->when($request->filled('date_from'), function ($q) use ($request) {
                return $q->where('created_at', '>=', $request->date_from);
            });

            $query->when($request->filled('date_to'), function ($q) use ($request) {
                return $q->where('created_at', '<=', $request->date_to . ' 23:59:59');
            });

            // Default ordering - newest first
            $query->orderBy('created_at', 'desc');

            // Log complex queries for performance monitoring
            if ($request->filled(['search', 'min_amount', 'max_amount', 'date_from', 'date_to'])) {
                \Log::info('Complex bid filter query executed', [
                    'filters' => $request->only(['status', 'auction_status', 'category_id', 'search', 'min_amount', 'max_amount', 'date_from', 'date_to']),
                    'user_id' => $user->id
                ]);
            }

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $bids = $query->paginate($perPage);

            // Get wallet information
            $wallet = $user->getWallet();

            return BidResource::collection($bids)
                ->additional([
                    'wallet_balance' => [
                        'total' => $wallet->balance,
                        'available' => $wallet->available_balance,
                        'held' => $wallet->held_balance
                    ]
                ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving your bids',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a bid.
     *
     * @param  \App\Models\Bid  $bid
     * @return \Illuminate\Http\Response
     */
    public function cancel(Bid $bid)
    {
        $user = auth()->user();

        // Check if the bid belongs to the user
        if ($bid->user_id !== $user->id) {
            return response()->json([
                'message' => 'You are not authorized to cancel this bid.'
            ], 403);
        }

        // Check if the bid can be cancelled
        if ($bid->status !== 'active') {
            return response()->json([
                'message' => 'This bid cannot be cancelled.'
            ], 400);
        }

        // Cancel the bid (this will release the hold)
        $result = $bid->cancel();

        if (!$result) {
            return response()->json([
                'message' => 'Failed to cancel the bid. Please try again.'
            ], 400);
        }

        // Get updated wallet information
        $wallet = $user->getWallet();

        return (new BidResource($bid))
            ->additional([
                'message' => 'Bid cancelled successfully',
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ]);
    }
}
