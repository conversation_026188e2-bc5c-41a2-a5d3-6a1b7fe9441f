<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\BidResource;
use App\Models\Auction;
use App\Models\Bid;
use Illuminate\Http\Request;

class BidApiController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Auction $auction)
    {
        // Check if user is verified buyer
        $user = $request->user();
        if (!$user->canBuy()) {
            return response()->json([
                'message' => 'You must be a verified buyer to place bids.',
                'verification_required' => true,
                'required_role' => 'buyer',
                'verification_status' => $user->getVerificationStatus()
            ], 403);
        }

        // Validation rules depend on auction type
        $minBidAmount = $auction->auction_type === 'sealed'
            ? $auction->starting_price  // Sealed bids only need to be above starting price
            : max($auction->current_price, $auction->starting_price); // Online bids must be higher than current

        $request->validate([
            'bid_amount' => 'required|numeric|min:' . $minBidAmount,
        ]);

        $user = auth()->user();

        // Check if user has a wallet
        $wallet = $user->getWallet();

        // Check if user has enough available balance
        if ($wallet->available_balance < $request->bid_amount) {
            return response()->json([
                'message' => 'Insufficient funds in your wallet. Please add funds to place this bid.',
                'available_balance' => $wallet->available_balance,
                'required_amount' => $request->bid_amount
            ], 400);
        }

        // Use the placeBid method which handles all validation and wallet holds
        try {
            // Check auction status
            if ($auction->status !== 'active') {
                return response()->json([
                    'message' => 'This auction is not active.',
                    'status' => $auction->status
                ], 400);
            }

            // Check if auction has started
            if (now() < $auction->start_time) {
                return response()->json([
                    'message' => 'This auction has not started yet.',
                    'start_time' => $auction->start_time,
                    'current_time' => now()
                ], 400);
            }

            // Check if auction has ended
            if (now() > $auction->end_time) {
                return response()->json([
                    'message' => 'This auction has already ended.',
                    'end_time' => $auction->end_time,
                    'current_time' => now()
                ], 400);
            }

            // Check if bid amount is valid
            if ($request->bid_amount <= $auction->current_price) {
                return response()->json([
                    'message' => 'Bid amount must be higher than the current price.',
                    'current_price' => $auction->current_price,
                    'bid_amount' => $request->bid_amount
                ], 400);
            }

            // Place the bid
            $bid = Bid::placeBid($auction, $user, $request->bid_amount);

            if (!$bid) {
                // Check if there was an issue with the wallet
                $wallet = $user->getWallet();
                return response()->json([
                    'message' => 'Failed to place bid. There was an issue processing your bid.',
                    'wallet_balance' => [
                        'total' => $wallet->balance,
                        'available' => $wallet->available_balance,
                        'held' => $wallet->held_balance
                    ]
                ], 400);
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Exception in BidApiController::store: " . $e->getMessage());
            \Illuminate\Support\Facades\Log::error($e->getTraceAsString());

            return response()->json([
                'message' => 'An error occurred while placing your bid: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }

        // Load the wallet hold for the response
        $bid->load('walletHold');

        return (new BidResource($bid))
            ->additional([
                'message' => 'Bid placed successfully',
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Display a listing of the authenticated user's bids.
     *
     * @return \Illuminate\Http\Response
     */
    public function myBids(Request $request)
    {
        try {
            $user = auth()->user();

            // Validate filter parameters
            $request->validate([
                'status' => 'nullable|string|in:active,won,lost,cancelled',
                'auction_status' => 'nullable|string|in:active,ended,upcoming',
                'category_id' => 'nullable|integer|exists:categories,id',
                'search' => 'nullable|string|max:255',
                'min_amount' => 'nullable|numeric|min:0',
                'max_amount' => 'nullable|numeric|min:0',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $query = Bid::where('user_id', $user->id)
                ->with([
                    'auction.category:id,name',
                    'auction.auctionItems:id,auction_id,item_name,item_description,image',
                    'auction.auctionItems.images',
                    'auction:id,title,description,vendor_id,category_id,start_time,end_time,starting_price,current_price,status',
                    'walletHold'
                ]);

            // Apply filters using when() for conditional filtering
            $query->when($request->filled('status'), function ($q) use ($request) {
                return $q->where('status', $request->status);
            });

            // Filter by auction status
            $query->when($request->filled('auction_status'), function ($q) use ($request) {
                return $q->whereHas('auction', function ($auctionQuery) use ($request) {
                    $auctionQuery->where('status', $request->auction_status);
                });
            });

            // Filter by auction category
            $query->when($request->filled('category_id'), function ($q) use ($request) {
                return $q->whereHas('auction', function ($auctionQuery) use ($request) {
                    $auctionQuery->where('category_id', $request->category_id);
                });
            });

            // Search in auction title, description, and auction items
            $query->when($request->filled('search'), function ($q) use ($request) {
                $search = $request->search;
                return $q->whereHas('auction', function ($auctionQuery) use ($search) {
                    $auctionQuery->where('title', 'like', '%' . $search . '%')
                               ->orWhere('description', 'like', '%' . $search . '%')
                               ->orWhereHas('auctionItems', function ($itemQuery) use ($search) {
                                   $itemQuery->where('item_name', 'like', '%' . $search . '%')
                                            ->orWhere('item_description', 'like', '%' . $search . '%');
                               });
                });
            });

            // Filter by bid amount range
            $query->when($request->filled('min_amount'), function ($q) use ($request) {
                return $q->where('bid_amount', '>=', $request->min_amount);
            });

            $query->when($request->filled('max_amount'), function ($q) use ($request) {
                return $q->where('bid_amount', '<=', $request->max_amount);
            });

            // Filter by date range
            $query->when($request->filled('date_from'), function ($q) use ($request) {
                return $q->where('created_at', '>=', $request->date_from);
            });

            $query->when($request->filled('date_to'), function ($q) use ($request) {
                return $q->where('created_at', '<=', $request->date_to . ' 23:59:59');
            });

            // Default ordering - newest first
            $query->orderBy('created_at', 'desc');

            // Log complex queries for performance monitoring
            if ($request->filled(['search', 'min_amount', 'max_amount', 'date_from', 'date_to'])) {
                \Log::info('Complex bid filter query executed', [
                    'filters' => $request->only(['status', 'auction_status', 'category_id', 'search', 'min_amount', 'max_amount', 'date_from', 'date_to']),
                    'user_id' => $user->id
                ]);
            }

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $bids = $query->paginate($perPage);

            // Get wallet information
            $wallet = $user->getWallet();

            return BidResource::collection($bids)
                ->additional([
                    'wallet_balance' => [
                        'total' => $wallet->balance,
                        'available' => $wallet->available_balance,
                        'held' => $wallet->held_balance
                    ]
                ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving your bids',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a bid (only for sealed auctions).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Bid  $bid
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Bid $bid)
    {
        try {
            $user = auth()->user();

            // Check if the bid belongs to the authenticated user
            if ($bid->user_id !== $user->id) {
                return response()->json([
                    'message' => 'You can only update your own bids.'
                ], 403);
            }

            // Check if the bid can be updated
            if ($bid->status !== 'active') {
                return response()->json([
                    'message' => 'Only active bids can be updated.'
                ], 400);
            }

            $auction = $bid->auction;

            // Check if the auction is still active
            if (!$auction->isActive()) {
                return response()->json([
                    'message' => 'Cannot update bids on inactive auctions.'
                ], 400);
            }

            // Only allow bid updates for sealed auctions
            if ($auction->auction_type !== 'sealed') {
                return response()->json([
                    'message' => 'Bid updates are only allowed for sealed auctions.'
                ], 400);
            }

            // Validate the new bid amount
            $request->validate([
                'bid_amount' => 'required|numeric|min:' . $auction->starting_price,
            ]);

            $newBidAmount = $request->bid_amount;

            // Update the bid using the model method
            if ($bid->updateBidAmount($newBidAmount)) {
                // Get updated wallet information
                $wallet = $user->getWallet();

                return (new BidResource($bid->fresh(['auction', 'user', 'walletHold'])))
                    ->additional([
                        'message' => 'Bid updated successfully',
                        'wallet_balance' => [
                            'total' => $wallet->balance,
                            'available' => $wallet->available_balance,
                            'held' => $wallet->held_balance
                        ]
                    ]);
            } else {
                return response()->json([
                    'message' => 'Failed to update the bid. Please try again.'
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error updating bid',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a bid (only for sealed auctions).
     *
     * @param  \App\Models\Bid  $bid
     * @return \Illuminate\Http\Response
     */
    public function cancel(Bid $bid)
    {
        $user = auth()->user();

        // Check if the bid belongs to the user
        if ($bid->user_id !== $user->id) {
            return response()->json([
                'message' => 'You are not authorized to cancel this bid.'
            ], 403);
        }

        // Check if the bid can be cancelled
        if ($bid->status !== 'active') {
            return response()->json([
                'message' => 'This bid cannot be cancelled.'
            ], 400);
        }

        $auction = $bid->auction;

        // Check if the auction is still active
        if (!$auction->isActive()) {
            return response()->json([
                'message' => 'Cannot cancel bids on inactive auctions.'
            ], 400);
        }

        // Only allow bid cancellation for sealed auctions
        if ($auction->auction_type !== 'sealed') {
            return response()->json([
                'message' => 'Bid cancellation is only allowed for sealed auctions.'
            ], 400);
        }

        // Cancel the bid (this will release the hold)
        $result = $bid->cancel();

        if (!$result) {
            return response()->json([
                'message' => 'Failed to cancel the bid. Please try again.'
            ], 400);
        }

        // Get updated wallet information
        $wallet = $user->getWallet();

        return (new BidResource($bid))
            ->additional([
                'message' => 'Bid cancelled successfully',
                'wallet_balance' => [
                    'total' => $wallet->balance,
                    'available' => $wallet->available_balance,
                    'held' => $wallet->held_balance
                ]
            ]);
    }

    /**
     * Get bids for a specific auction (vendor only).
     * Respects sealed auction visibility rules.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Auction  $auction
     * @return \Illuminate\Http\Response
     */
    public function vendorBids(Request $request, Auction $auction)
    {
        try {
            $user = $request->user();

            // Check if user is the vendor of this auction
            if (!$user->vendor || $user->vendor->id !== $auction->vendor_id) {
                return response()->json([
                    'message' => 'You can only view bids on your own auctions.'
                ], 403);
            }

            // For sealed auctions, only show bids if auction has ended
            if ($auction->auction_type === 'sealed' && $auction->status === 'active') {
                return response()->json([
                    'message' => 'Bids are hidden until the sealed auction ends.',
                    'auction_type' => 'sealed',
                    'auction_status' => 'active',
                    'bids_hidden' => true,
                    'bid_count' => $auction->bids()->where('status', 'active')->count(),
                    'data' => []
                ]);
            }

            // Get bids with user information
            $bids = $auction->bids()
                ->with([
                    'user:id,name,email',
                    'walletHold'
                ])
                ->orderBy('bid_amount', 'desc')
                ->orderBy('created_at', 'asc') // Earlier bids win ties
                ->get();

            // Add additional information for vendor view
            $bidsWithDetails = $bids->map(function ($bid) use ($auction) {
                $bidData = [
                    'id' => $bid->id,
                    'user_id' => $bid->user_id,
                    'user_name' => $bid->user->name ?? 'Unknown',
                    'user_email' => $bid->user->email ?? 'Unknown',
                    'bid_amount' => $bid->bid_amount,
                    'status' => $bid->status,
                    'created_at' => $bid->created_at,
                    'updated_at' => $bid->updated_at,
                    'is_winning' => false,
                    'wallet_hold_status' => $bid->walletHold ? $bid->walletHold->status : null,
                ];

                // Mark winning bid if auction has ended
                if ($auction->status === 'ended' || $auction->status === 'completed') {
                    $winningBid = $auction->winningBid();
                    $bidData['is_winning'] = $winningBid && $winningBid->id === $bid->id;
                }

                return $bidData;
            });

            return response()->json([
                'message' => 'Bids retrieved successfully',
                'auction_type' => $auction->auction_type,
                'auction_status' => $auction->status,
                'bids_hidden' => false,
                'total_bids' => $bids->count(),
                'unique_bidders' => $bids->unique('user_id')->count(),
                'highest_bid' => $bids->max('bid_amount'),
                'data' => $bidsWithDetails
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving auction bids',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
