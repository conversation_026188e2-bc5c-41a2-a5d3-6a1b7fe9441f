<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Auction;
use App\Models\Bid;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VendorStatsApiController extends Controller
{
    /**
     * Get comprehensive vendor statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            
            // Check if user has a vendor profile
            if (!$user->vendor) {
                return response()->json([
                    'message' => 'You must be a vendor to access these statistics.'
                ], 403);
            }

            $vendor = $user->vendor;
            $vendorId = $vendor->id;

            // Basic auction statistics
            $totalAuctions = Auction::where('vendor_id', $vendorId)->count();
            $activeAuctions = Auction::where('vendor_id', $vendorId)
                ->where('status', 'active')
                ->where('end_time', '>', now())
                ->count();
            $completedAuctions = Auction::where('vendor_id', $vendorId)
                ->where('status', 'completed')
                ->count();
            $draftAuctions = Auction::where('vendor_id', $vendorId)
                ->where('status', 'draft')
                ->count();

            // Bid statistics
            $totalBidsReceived = Bid::whereHas('auction', function ($query) use ($vendorId) {
                $query->where('vendor_id', $vendorId);
            })->count();

            $uniqueBidders = Bid::whereHas('auction', function ($query) use ($vendorId) {
                $query->where('vendor_id', $vendorId);
            })->distinct('user_id')->count();

            // Revenue calculations (based on completed auctions)
            $revenueData = Auction::where('vendor_id', $vendorId)
                ->where('status', 'completed')
                ->selectRaw('
                    SUM(current_price) as total_revenue,
                    AVG(current_price) as average_sale_price,
                    COUNT(*) as successful_auctions
                ')
                ->first();

            $totalRevenue = $revenueData->total_revenue ?? 0;
            $averageSalePrice = $revenueData->average_sale_price ?? 0;
            $successfulAuctions = $revenueData->successful_auctions ?? 0;

            // Success rate calculation
            $successRate = $totalAuctions > 0 ? ($successfulAuctions / $totalAuctions) * 100 : 0;

            // Recent activity (last 30 days)
            $recentAuctions = Auction::where('vendor_id', $vendorId)
                ->where('created_at', '>=', now()->subDays(30))
                ->count();

            $recentBids = Bid::whereHas('auction', function ($query) use ($vendorId) {
                $query->where('vendor_id', $vendorId);
            })->where('created_at', '>=', now()->subDays(30))->count();

            // Top performing auctions (by bid count)
            $topAuctions = Auction::where('vendor_id', $vendorId)
                ->withCount('bids')
                ->orderBy('bids_count', 'desc')
                ->limit(5)
                ->get(['id', 'title', 'current_price', 'status', 'bids_count']);

            // Monthly performance (last 6 months)
            $monthlyStats = Auction::where('vendor_id', $vendorId)
                ->where('created_at', '>=', now()->subMonths(6))
                ->selectRaw('
                    DATE_FORMAT(created_at, "%Y-%m") as month,
                    COUNT(*) as auctions_created,
                    SUM(CASE WHEN status = "completed" THEN current_price ELSE 0 END) as revenue
                ')
                ->groupBy('month')
                ->orderBy('month', 'desc')
                ->get();

            // Category performance
            $categoryStats = Auction::where('vendor_id', $vendorId)
                ->join('categories', 'auctions.category_id', '=', 'categories.id')
                ->selectRaw('
                    categories.name as category_name,
                    COUNT(auctions.id) as auction_count,
                    AVG(auctions.current_price) as avg_price,
                    SUM(CASE WHEN auctions.status = "completed" THEN auctions.current_price ELSE 0 END) as total_revenue
                ')
                ->groupBy('categories.id', 'categories.name')
                ->orderBy('auction_count', 'desc')
                ->get();

            return response()->json([
                'vendor_info' => [
                    'id' => $vendor->id,
                    'name' => $vendor->name,
                    'email' => $vendor->email,
                    'is_active' => $vendor->is_active,
                ],
                'auction_stats' => [
                    'total_auctions' => $totalAuctions,
                    'active_auctions' => $activeAuctions,
                    'completed_auctions' => $completedAuctions,
                    'draft_auctions' => $draftAuctions,
                    'success_rate' => round($successRate, 2),
                ],
                'bid_stats' => [
                    'total_bids_received' => $totalBidsReceived,
                    'unique_bidders' => $uniqueBidders,
                    'average_bids_per_auction' => $totalAuctions > 0 ? round($totalBidsReceived / $totalAuctions, 2) : 0,
                ],
                'revenue_stats' => [
                    'total_revenue' => round($totalRevenue, 2),
                    'average_sale_price' => round($averageSalePrice, 2),
                    'successful_auctions' => $successfulAuctions,
                ],
                'recent_activity' => [
                    'auctions_last_30_days' => $recentAuctions,
                    'bids_last_30_days' => $recentBids,
                ],
                'top_auctions' => $topAuctions,
                'monthly_performance' => $monthlyStats,
                'category_performance' => $categoryStats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving vendor statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get quick vendor dashboard stats.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function dashboard(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user->vendor) {
                return response()->json([
                    'message' => 'You must be a vendor to access these statistics.'
                ], 403);
            }

            $vendorId = $user->vendor->id;

            // Quick stats for dashboard
            $activeAuctions = Auction::where('vendor_id', $vendorId)
                ->where('status', 'active')
                ->where('end_time', '>', now())
                ->count();

            $endingSoon = Auction::where('vendor_id', $vendorId)
                ->where('status', 'active')
                ->where('end_time', '>', now())
                ->where('end_time', '<=', now()->addHours(24))
                ->count();

            $pendingBids = Bid::whereHas('auction', function ($query) use ($vendorId) {
                $query->where('vendor_id', $vendorId)
                      ->where('status', 'active');
            })->where('created_at', '>=', now()->subDays(7))->count();

            $thisMonthRevenue = Auction::where('vendor_id', $vendorId)
                ->where('status', 'completed')
                ->whereMonth('updated_at', now()->month)
                ->whereYear('updated_at', now()->year)
                ->sum('current_price');

            return response()->json([
                'active_auctions' => $activeAuctions,
                'ending_soon' => $endingSoon,
                'recent_bids' => $pendingBids,
                'this_month_revenue' => round($thisMonthRevenue, 2),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving dashboard statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
