<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\CategoryCollection;

class CategoryApiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            // Validate filter parameters
            $request->validate([
                'search' => 'nullable|string|max:255',
                'has_auctions' => 'nullable|boolean',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $query = Category::query();

            // Add auction count for all categories
            $query->withCount('auctions');

            // Apply filters using when() for conditional filtering
            $query->when($request->filled('search'), function ($q) use ($request) {
                return $q->where('name', 'like', '%' . $request->search . '%');
            });

            // Filter categories that have auctions
            $query->when($request->filled('has_auctions'), function ($q) use ($request) {
                if ($request->boolean('has_auctions')) {
                    return $q->has('auctions');
                } else {
                    return $q->doesntHave('auctions');
                }
            });

            // Default ordering by name
            $query->orderBy('name', 'asc');

            // Add pagination with validation
            $perPage = min($request->input('per_page', 15), 100);
            $categories = $query->paginate($perPage);

            return CategoryResource::collection($categories);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $validated['created_by'] = $request->user()->id;
        $category = Category::create($validated);

        return (new CategoryResource($category))
            ->additional(['message' => 'Category created successfully'])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function show(Category $category)
    {
        try {
            $category->load(['auctions' => function($query) {
                $query->select('id', 'title', 'description', 'featured_image', 'category_id', 'vendor_id', 'start_time', 'end_time', 'status')
                      ->with(['vendor:id,name,slug', 'auctionItems:id,auction_id,item_name,image']);
            }]);
            return new CategoryResource($category);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving category details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $category->update($validated);

        return (new CategoryResource($category))
            ->additional(['message' => 'Category updated successfully']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function destroy(Category $category)
    {
        $category->delete();

        return response()->json([
            'message' => 'Category deleted successfully'
        ]);
    }

    /**
     * Display categories created by the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function myCategories(Request $request)
    {
        try {
            $user = $request->user();
            $perPage = $request->input('per_page', 15);

            // Get categories created by the authenticated user
            $categories = Category::where('created_by', $user->id)
                ->withCount('auctions')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return CategoryResource::collection($categories);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving your categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
