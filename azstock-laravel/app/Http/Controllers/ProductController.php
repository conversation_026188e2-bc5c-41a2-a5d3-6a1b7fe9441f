<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Vendor;
use App\Models\ProductImage;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index()
    {
        $vendor = auth()->user()->vendor; // Get the authenticated vendor
        if (!$vendor) {
            return redirect()->route('dashboard')->withErrors(['error' => 'You do not have a vendor account.']);
        }

        $products = Product::where('vendor_id', $vendor->id)->with(['vendor', 'category'])->get(); // Fetch only belonging to the current user
        return view('dashboard.products.index', compact('products'));
    }
    public function myProducts()
    {
        $vendor = auth()->user()->vendor; // Get the authenticated vendor
        if (!$vendor) {
            return redirect()->route('dashboard')->withErrors(['error' => 'You do not have a vendor account.']);
        }

        $products = Product::where('vendor_id', $vendor->id)->with(['vendor', 'category'])->get(); // Fetch only belonging to the current user
        return view('dashboard.products.index', compact('products'));
    }

    public function create()
    {
        $vendors = Vendor::all();
        $categories = Category::all();
        return view('dashboard.products.create', compact('vendors', 'categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpg,jpeg,png|max:2048', // Max size of 2MB
        ]);
    
        $vendorId = auth()->user()->vendor->id;
    
        $product = Product::create([
            'vendor_id' => $vendorId,
            'name' => $request->name,
            'description' => $request->description,
            'category_id' => $request->category_id,
        ]);
        // Handle featured image (can be file upload or Vercel Blob URL)
        if ($request->hasFile('image')) {
            // Traditional file upload
            $vendorId = $product->vendor_id;
            $path = $request->file('image')->store("uploads/vendors/{$vendorId}/products", 'public');
            $product->image = $path;
        } elseif ($request->has('image_url')) {
            // Vercel Blob URL (frontend sends as 'image_url')
            $imageUrl = $request->input('image_url');
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                $product->image = $imageUrl;
            }
        }

        // Handle gallery images (can be file uploads or Vercel Blob URLs)
        if ($request->hasFile('images')) {
            // Traditional file uploads
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store("uploads/vendors/{$vendorId}/products", 'public');
                $product->images()->create(['image_path' => $imagePath]);
            }
        } elseif ($request->has('image_urls')) {
            // Vercel Blob URLs
            $imageUrls = $request->input('image_urls', []);
            foreach ($imageUrls as $imageUrl) {
                if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    $product->images()->create(['image_path' => $imageUrl]);
                }
            }
        }
        $product->save();
    
        return redirect()->route('products')->with('success', 'Product created successfully.');
    }
    

    public function edit(Product $product)
    {
        $categories = Category::all();
        return view('dashboard.products.edit', compact('product', 'categories'));
    }
    public function show(Product $product)
    {
        $product->load(['category', 'vendor']);
        return view('auctions.show', compact('product'));
    }

    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048', // Validate each image
        ]);
    
        $product->update([
            'name' => $request->name,
            'description' => $request->description,
            'category_id' => $request->category_id,
        ]);
        if ($request->hasFile('images')) {
            $vendorId = auth()->user()->vendor->id;
    
            foreach ($request->file('images') as $image) {
                // Save image to vendor-specific product folder
                $imagePath = $image->store("uploads/vendors/{$vendorId}/products", 'public');
                
                // Attach new image to product
                $product->images()->create(['image_path' => $imagePath]);
            }
        }

        return redirect()->route('products')->with('success', 'Product updated successfully.');
    }

    public function destroy(Product $product)
    {
        // $image = ProductImage::findOrFail($imageId);
    
        // // Delete the image file from storage
        // if (Storage::disk('public')->exists($image->image_path)) {
        //     Storage::disk('public')->delete($image->image_path);
        // }
    
        // // Delete the image record from the database
        // $image->delete();
        $product->delete();
        return redirect()->route('products')->with('success', 'Product deleted successfully.');
    }
}
