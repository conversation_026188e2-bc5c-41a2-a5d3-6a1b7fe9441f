<?php

namespace App\Services;

use App\Models\ActivityLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class ActivityLogger
{
    /**
     * Log an activity.
     *
     * @param string $action
     * @param string $actionType
     * @param string|null $entityType
     * @param int|null $entityId
     * @param string $description
     * @param array|null $oldValues
     * @param array|null $newValues
     * @return ActivityLog
     */
    public static function log($action, $actionType, $entityType = null, $entityId = null, $description = '', $oldValues = null, $newValues = null)
    {
        $userId = Auth::id();
        
        return ActivityLog::create([
            'user_id' => $userId,
            'action' => $action,
            'action_type' => $actionType,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'description' => $description,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }
    
    /**
     * Log a login activity.
     *
     * @param int $userId
     * @param string $description
     * @return ActivityLog
     */
    public static function logLogin($userId, $description = 'User logged in')
    {
        return ActivityLog::create([
            'user_id' => $userId,
            'action' => 'login',
            'action_type' => 'auth',
            'entity_type' => 'user',
            'entity_id' => $userId,
            'description' => $description,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }
    
    /**
     * Log a logout activity.
     *
     * @param int $userId
     * @param string $description
     * @return ActivityLog
     */
    public static function logLogout($userId, $description = 'User logged out')
    {
        return ActivityLog::create([
            'user_id' => $userId,
            'action' => 'logout',
            'action_type' => 'auth',
            'entity_type' => 'user',
            'entity_id' => $userId,
            'description' => $description,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }
    
    /**
     * Log a create activity.
     *
     * @param string $entityType
     * @param int $entityId
     * @param string $description
     * @param array|null $values
     * @return ActivityLog
     */
    public static function logCreate($entityType, $entityId, $description, $values = null)
    {
        return self::log(
            'create',
            'crud',
            $entityType,
            $entityId,
            $description,
            null,
            $values
        );
    }
    
    /**
     * Log an update activity.
     *
     * @param string $entityType
     * @param int $entityId
     * @param string $description
     * @param array|null $oldValues
     * @param array|null $newValues
     * @return ActivityLog
     */
    public static function logUpdate($entityType, $entityId, $description, $oldValues = null, $newValues = null)
    {
        return self::log(
            'update',
            'crud',
            $entityType,
            $entityId,
            $description,
            $oldValues,
            $newValues
        );
    }
    
    /**
     * Log a delete activity.
     *
     * @param string $entityType
     * @param int $entityId
     * @param string $description
     * @param array|null $values
     * @return ActivityLog
     */
    public static function logDelete($entityType, $entityId, $description, $values = null)
    {
        return self::log(
            'delete',
            'crud',
            $entityType,
            $entityId,
            $description,
            $values,
            null
        );
    }
}
