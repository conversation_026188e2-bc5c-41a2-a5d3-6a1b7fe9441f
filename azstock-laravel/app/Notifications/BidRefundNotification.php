<?php

namespace App\Notifications;

use App\Models\Bid;
use App\Models\WalletTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BidRefundNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $bid;
    protected $transaction;

    /**
     * Create a new notification instance.
     *
     * @param Bid $bid
     * @param WalletTransaction $transaction
     */
    public function __construct(Bid $bid, WalletTransaction $transaction)
    {
        $this->bid = $bid;
        $this->transaction = $transaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $auction = $this->bid->auction;
        $product = $auction->product;
        
        return (new MailMessage)
            ->subject('Bid Refund Processed')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your bid on "' . $product->name . '" has been refunded.')
            ->line('Bid Amount: $' . number_format($this->bid->bid_amount, 2))
            ->line('Refund Reason: ' . $this->transaction->description)
            ->line('Transaction ID: ' . $this->transaction->id)
            ->line('The funds have been returned to your wallet and are now available for other bids.')
            ->action('View Your Wallet', url('/wallet'))
            ->line('Thank you for using our platform!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'bid_id' => $this->bid->id,
            'auction_id' => $this->bid->auction_id,
            'product_name' => $this->bid->auction->product->name,
            'amount' => $this->bid->bid_amount,
            'transaction_id' => $this->transaction->id,
            'description' => $this->transaction->description,
        ];
    }
}
