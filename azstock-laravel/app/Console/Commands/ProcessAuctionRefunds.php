<?php

namespace App\Console\Commands;

use App\Models\Auction;
use App\Models\Bid;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessAuctionRefunds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auction:process-refunds {--days=7 : Process auctions ended within the specified number of days}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process refunds for ended auctions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $this->info("Processing refunds for auctions ended within the last {$days} days...");
        
        // Find ended auctions that ended within the specified time period
        $endedAuctions = Auction::where('status', 'ended')
            ->where('end_time', '>=', now()->subDays($days))
            ->where('end_time', '<=', now())
            ->get();
        
        $count = $endedAuctions->count();
        $this->info("Found {$count} ended auctions to process.");
        
        if ($count === 0) {
            return 0;
        }
        
        $processedAuctions = 0;
        $processedBids = 0;
        $failedBids = 0;
        
        foreach ($endedAuctions as $auction) {
            $this->info("Processing auction #{$auction->id}: {$auction->product->name}");
            
            // Get the winning bid if there is one
            $winningBid = $auction->winningBid();
            
            // Get all active bids that need to be refunded
            $activeBids = $auction->bids()
                ->where('status', 'active')
                ->get();
            
            $bidCount = $activeBids->count();
            $this->info("Found {$bidCount} active bids to process for auction #{$auction->id}");
            
            if ($bidCount > 0) {
                foreach ($activeBids as $bid) {
                    // Skip the winning bid if there is one
                    if ($winningBid && $bid->id === $winningBid->id) {
                        $this->info("Skipping winning bid #{$bid->id}");
                        continue;
                    }
                    
                    // Determine the reason for the refund
                    $reason = $this->getRefundReason($auction, $bid, $winningBid);
                    
                    // Process the refund
                    $this->info("Processing refund for bid #{$bid->id} by user #{$bid->user_id} for amount {$bid->bid_amount}");
                    
                    try {
                        $result = $bid->markAsLost($reason);
                        
                        if ($result) {
                            $this->info("Successfully refunded bid #{$bid->id}");
                            $processedBids++;
                        } else {
                            $this->error("Failed to refund bid #{$bid->id}");
                            $failedBids++;
                        }
                    } catch (\Exception $e) {
                        $this->error("Exception while refunding bid #{$bid->id}: " . $e->getMessage());
                        Log::error("Exception while refunding bid #{$bid->id}: " . $e->getMessage());
                        Log::error($e->getTraceAsString());
                        $failedBids++;
                    }
                }
            }
            
            $processedAuctions++;
        }
        
        $this->info("Processed {$processedAuctions} auctions.");
        $this->info("Successfully refunded {$processedBids} bids.");
        
        if ($failedBids > 0) {
            $this->error("Failed to refund {$failedBids} bids.");
        }
        
        return 0;
    }
    
    /**
     * Get the reason for the refund.
     *
     * @param Auction $auction
     * @param Bid $bid
     * @param Bid|null $winningBid
     * @return string
     */
    protected function getRefundReason(Auction $auction, Bid $bid, $winningBid)
    {
        if ($winningBid) {
            return "Outbid by winning bid #{$winningBid->id} with amount {$winningBid->bid_amount}";
        } elseif ($auction->reserve_price && $auction->reserve_price > $bid->bid_amount) {
            return "Auction ended with no winner: reserve price of {$auction->reserve_price} not met";
        } else {
            return "Auction ended with no winner";
        }
    }
}
