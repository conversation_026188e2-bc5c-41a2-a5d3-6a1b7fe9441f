<?php

namespace App\Console\Commands;

use App\Models\Auction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class EndExpiredAuctions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auction:end-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'End auctions that have passed their end time';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Looking for expired auctions...');

        // Find active auctions that have passed their end time
        $expiredAuctions = Auction::where('status', 'active')
            ->where('end_time', '<', now())
            ->get();

        $count = $expiredAuctions->count();
        $this->info("Found {$count} expired auctions.");

        if ($count === 0) {
            return 0;
        }

        $ended = 0;
        $failed = 0;

        foreach ($expiredAuctions as $auction) {
            try {
                $result = $auction->end();

                if ($result) {
                    $ended++;
                    $this->info("Ended auction #{$auction->id}");

                    // Get the winning bid if there is one
                    $winningBid = $auction->winningBid();
                    if ($winningBid) {
                        $this->info("Auction #{$auction->id} won by user #{$winningBid->user_id} with bid amount {$winningBid->bid_amount}");
                    } else {
                        $this->info("Auction #{$auction->id} ended with no winner");
                    }
                } else {
                    $failed++;
                    $this->error("Failed to end auction #{$auction->id}");
                }
            } catch (\Exception $e) {
                $failed++;
                $this->error("Error ending auction #{$auction->id}: " . $e->getMessage());
                Log::error("Error ending auction #{$auction->id}: " . $e->getMessage());
            }
        }

        $this->info("Ended {$ended} auctions, failed to end {$failed} auctions.");

        return 0;
    }
}
