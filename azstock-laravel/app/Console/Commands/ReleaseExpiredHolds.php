<?php

namespace App\Console\Commands;

use App\Models\WalletHold;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ReleaseExpiredHolds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wallet:release-expired-holds';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Release funds from expired wallet holds';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Looking for expired holds...');

        // Find all expired holds that are still active
        $expiredHolds = WalletHold::expired()->get();

        $count = $expiredHolds->count();
        $this->info("Found {$count} expired holds.");

        if ($count === 0) {
            return 0;
        }

        $released = 0;
        $failed = 0;

        foreach ($expiredHolds as $hold) {
            try {
                $wallet = $hold->wallet;
                $result = $wallet->releaseHold($hold, 'Hold expired');

                if ($result) {
                    $released++;
                    $this->info("Released hold #{$hold->id} for {$hold->amount} from wallet #{$wallet->id}");
                } else {
                    $failed++;
                    $this->error("Failed to release hold #{$hold->id}");
                }
            } catch (\Exception $e) {
                $failed++;
                $this->error("Error releasing hold #{$hold->id}: " . $e->getMessage());
                Log::error("Error releasing hold #{$hold->id}: " . $e->getMessage());
            }
        }

        $this->info("Released {$released} holds, failed to release {$failed} holds.");

        return 0;
    }
}
