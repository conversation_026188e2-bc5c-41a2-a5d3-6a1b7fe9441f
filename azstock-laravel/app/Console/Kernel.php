<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Release expired holds every hour
        $schedule->command('wallet:release-expired-holds')
                 ->hourly()
                 ->appendOutputTo(storage_path('logs/wallet-holds.log'));

        // End expired auctions every 5 minutes
        $schedule->command('auction:end-expired')
                 ->everyFiveMinutes()
                 ->appendOutputTo(storage_path('logs/auctions.log'));

        // Process refunds for ended auctions every 15 minutes
        $schedule->command('auction:process-refunds')
                 ->everyFifteenMinutes()
                 ->appendOutputTo(storage_path('logs/refunds.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
