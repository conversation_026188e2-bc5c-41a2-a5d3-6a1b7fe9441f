<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Wallet extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'balance',
        'held_balance',
        'available_balance',
        'currency',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'balance' => 'decimal:2',
        'held_balance' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the wallet.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transactions for the wallet.
     */
    public function transactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get the holds for the wallet.
     */
    public function holds()
    {
        return $this->hasMany(WalletHold::class);
    }

    /**
     * Get active holds for the wallet.
     */
    public function activeHolds()
    {
        return $this->holds()->where('status', 'active');
    }

    /**
     * Deposit funds into the wallet.
     *
     * @param float $amount
     * @param string $description
     * @param array $metadata
     * @return WalletTransaction
     */
    public function deposit($amount, $description = 'Deposit', $metadata = [])
    {
        $this->balance += $amount;
        $this->available_balance += $amount;
        $this->save();

        return $this->recordTransaction('deposit', $amount, $description, null, null, $metadata);
    }

    /**
     * Withdraw funds from the wallet.
     *
     * @param float $amount
     * @param string $description
     * @param array $metadata
     * @return WalletTransaction|false
     */
    public function withdraw($amount, $description = 'Withdrawal', $metadata = [])
    {
        if ($this->available_balance < $amount) {
            return false;
        }

        $this->balance -= $amount;
        $this->available_balance -= $amount;
        $this->save();

        return $this->recordTransaction('withdrawal', -$amount, $description, null, null, $metadata);
    }

    /**
     * Place a hold on funds in the wallet.
     *
     * @param float $amount
     * @param string $reason
     * @param string $referenceType
     * @param int $referenceId
     * @param \DateTime|null $expiresAt
     * @return WalletHold|false
     */
    public function placeHold($amount, $reason, $referenceType, $referenceId, $expiresAt = null)
    {
        Log::info("Attempting to place hold: Wallet #{$this->id}, Amount: {$amount}, Reference: {$referenceType}#{$referenceId}");

        if ($this->available_balance < $amount) {
            Log::warning("Hold failed: Insufficient available balance. Required: {$amount}, Available: {$this->available_balance}");
            return false;
        }

        try {
            $balanceBefore = $this->available_balance;
            Log::info("Balance before hold: Total: {$this->balance}, Available: {$this->available_balance}, Held: {$this->held_balance}");

            $this->held_balance += $amount;
            $this->available_balance -= $amount;
            $this->save();

            Log::info("Balance after hold: Total: {$this->balance}, Available: {$this->available_balance}, Held: {$this->held_balance}");

            $transaction = $this->recordTransaction(
                'hold',
                -$amount,
                "Hold: {$reason}",
                $referenceType,
                $referenceId
            );

            Log::info("Transaction recorded: #{$transaction->id}");

            try {
                $hold = WalletHold::create([
                    'wallet_id' => $this->id,
                    'user_id' => $this->user_id,
                    'amount' => $amount,
                    'reason' => $reason,
                    'status' => 'active',
                    'reference_type' => $referenceType,
                    'reference_id' => $referenceId,
                    'transaction_id' => $transaction->id,
                    'expires_at' => $expiresAt,
                ]);

                Log::info("Hold created: #{$hold->id}");
                return $hold;
            } catch (\Exception $e) {
                Log::error("Exception while creating hold record: " . $e->getMessage());
                Log::error($e->getTraceAsString());

                // Rollback the balance changes
                $this->held_balance -= $amount;
                $this->available_balance += $amount;
                $this->save();

                Log::info("Balance rolled back: Total: {$this->balance}, Available: {$this->available_balance}, Held: {$this->held_balance}");
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Exception while placing hold: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * Release a hold on funds in the wallet.
     *
     * @param WalletHold $hold
     * @param string $description
     * @return WalletTransaction|false
     */
    public function releaseHold(WalletHold $hold, $description = 'Hold released')
    {
        if ($hold->status !== 'active') {
            return false;
        }

        $this->held_balance -= $hold->amount;
        $this->available_balance += $hold->amount;
        $this->save();

        $hold->status = 'released';
        $hold->released_at = now();
        $hold->save();

        return $this->recordTransaction(
            'release',
            $hold->amount,
            $description,
            $hold->reference_type,
            $hold->reference_id
        );
    }

    /**
     * Apply a hold (convert it to a payment).
     *
     * @param WalletHold $hold
     * @param string $description
     * @return WalletTransaction|false
     */
    public function applyHold(WalletHold $hold, $description = 'Hold applied')
    {
        if ($hold->status !== 'active') {
            return false;
        }

        $this->held_balance -= $hold->amount;
        // Note: balance already reduced when hold was placed
        $this->save();

        $hold->status = 'applied';
        $hold->released_at = now();
        $hold->save();

        return $this->recordTransaction(
            'payment',
            -$hold->amount,
            $description,
            $hold->reference_type,
            $hold->reference_id
        );
    }

    /**
     * Record a transaction.
     *
     * @param string $type
     * @param float $amount
     * @param string $description
     * @param string|null $referenceType
     * @param int|null $referenceId
     * @param array $metadata
     * @return WalletTransaction
     */
    protected function recordTransaction($type, $amount, $description, $referenceType = null, $referenceId = null, $metadata = [])
    {
        try {
            Log::info("Recording transaction: Type: {$type}, Amount: {$amount}, Description: {$description}");

            $balanceBefore = $this->getOriginal('balance') ?? $this->balance - $amount;

            $transaction = WalletTransaction::create([
                'wallet_id' => $this->id,
                'user_id' => $this->user_id,
                'type' => $type,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $this->balance,
                'description' => $description,
                'reference_type' => $referenceType,
                'reference_id' => $referenceId,
                'metadata' => !empty($metadata) ? json_encode($metadata) : null,
                'status' => 'completed',
            ]);

            Log::info("Transaction recorded: #{$transaction->id}");
            return $transaction;
        } catch (\Exception $e) {
            Log::error("Exception while recording transaction: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            throw $e; // Re-throw to handle at a higher level
        }
    }
}
