<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletHold extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'wallet_id',
        'user_id',
        'amount',
        'reason',
        'status',
        'reference_type',
        'reference_id',
        'transaction_id',
        'expires_at',
        'released_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'expires_at' => 'datetime',
        'released_at' => 'datetime',
    ];

    /**
     * Get the wallet that owns the hold.
     */
    public function wallet()
    {
        return $this->belongsTo(Wallet::class);
    }

    /**
     * Get the user that owns the hold.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transaction that created this hold.
     */
    public function transaction()
    {
        return $this->belongsTo(WalletTransaction::class);
    }

    /**
     * Get the reference model (polymorphic).
     */
    public function reference()
    {
        if ($this->reference_type === 'bid') {
            return $this->belongsTo(Bid::class, 'reference_id');
        } elseif ($this->reference_type === 'auction') {
            return $this->belongsTo(Auction::class, 'reference_id');
        }

        return null;
    }

    /**
     * Scope a query to only include active holds.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include released holds.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeReleased($query)
    {
        return $query->where('status', 'released');
    }

    /**
     * Scope a query to only include applied holds.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApplied($query)
    {
        return $query->where('status', 'applied');
    }

    /**
     * Scope a query to only include expired holds.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'active')
            ->whereNotNull('expires_at')
            ->where('expires_at', '<', now());
    }

    /**
     * Check if the hold is active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if the hold is expired.
     *
     * @return bool
     */
    public function isExpired()
    {
        return $this->expires_at !== null && $this->expires_at < now();
    }

    /**
     * Release the hold.
     *
     * @param string $description
     * @return bool
     */
    public function release($description = 'Hold released')
    {
        if (!$this->isActive()) {
            return false;
        }

        return (bool) $this->wallet->releaseHold($this, $description);
    }

    /**
     * Apply the hold.
     *
     * @param string $description
     * @return bool
     */
    public function apply($description = 'Hold applied')
    {
        if (!$this->isActive()) {
            return false;
        }

        return (bool) $this->wallet->applyHold($this, $description);
    }
}
