<?php

namespace App\Models;

use App\Notifications\BidRefundNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Bid extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'user_id',
        'bid_amount',
        'status', // 'active', 'won', 'lost', 'cancelled'
    ];

    protected $casts = [
        'bid_amount' => 'decimal:2',
    ];

    // Add relationships
    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the wallet hold associated with this bid.
     */
    public function walletHold()
    {
        return $this->hasOne(WalletHold::class, 'reference_id')
            ->where('reference_type', 'bid');
    }

    /**
     * Place a bid with a wallet hold.
     *
     * @param \App\Models\Auction $auction
     * @param \App\Models\User $user
     * @param float $bidAmount
     * @return \App\Models\Bid|false
     */
    public static function placeBid(Auction $auction, User $user, $bidAmount)
    {
        Log::info("Attempting to place bid: Auction #{$auction->id}, User #{$user->id}, Amount: {$bidAmount}");

        // Check if auction is active
        if ($auction->status !== 'active') {
            Log::warning("Bid failed: Auction #{$auction->id} is not active. Status: {$auction->status}");
            return false;
        }

        // Check if auction has started
        if (now() < $auction->start_time) {
            Log::warning("Bid failed: Auction #{$auction->id} has not started yet. Start time: {$auction->start_time}");
            return false;
        }

        // Check if auction has ended
        if (now() > $auction->end_time) {
            Log::warning("Bid failed: Auction #{$auction->id} has already ended. End time: {$auction->end_time}");
            return false;
        }

        // Check if bid amount is valid based on auction type
        if ($auction->auction_type === 'sealed') {
            // For sealed auctions, bids only need to be above starting price
            if ($bidAmount < $auction->starting_price) {
                Log::warning("Bid failed: Sealed bid amount {$bidAmount} is below starting price {$auction->starting_price}");
                return false;
            }
        } else {
            // For online auctions, bids must be higher than current price
            if ($bidAmount <= $auction->current_price) {
                Log::warning("Bid failed: Online bid amount {$bidAmount} is not higher than current price {$auction->current_price}");
                return false;
            }
        }

        // Get user's wallet
        $wallet = $user->getWallet();
        Log::info("User wallet: Total: {$wallet->balance}, Available: {$wallet->available_balance}, Held: {$wallet->held_balance}");

        // Check if user has enough available balance
        if ($wallet->available_balance < $bidAmount) {
            Log::warning("Bid failed: Insufficient available balance. Required: {$bidAmount}, Available: {$wallet->available_balance}");
            return false;
        }

        try {
            // Create the bid
            $bid = self::create([
                'auction_id' => $auction->id,
                'user_id' => $user->id,
                'bid_amount' => $bidAmount,
                'status' => 'active',
            ]);

            Log::info("Bid created: #{$bid->id}");

            try {
                // Place a hold on the funds
                $hold = $wallet->placeHold(
                    $bidAmount,
                    "Bid on auction #{$auction->id}",
                    'bid',
                    $bid->id,
                    $auction->end_time
                );

                if (!$hold) {
                    // If hold fails, delete the bid and return false
                    Log::error("Failed to place hold on funds for bid #{$bid->id}");
                    $bid->delete();
                    return false;
                }
            } catch (\Exception $e) {
                Log::error("Exception while placing hold: " . $e->getMessage());
                Log::error($e->getTraceAsString());

                // If there's an exception, delete the bid and return false
                $bid->delete();
                return false;
            }

            Log::info("Hold placed: #{$hold->id} for amount {$hold->amount}");

            // Update the auction's current price only for online auctions
            if ($auction->auction_type === 'online') {
                $auction->update(['current_price' => $bidAmount]);
                Log::info("Online auction #{$auction->id} current price updated to {$bidAmount}");
            } else {
                Log::info("Sealed auction #{$auction->id} current price not updated (sealed bid)");
            }

            return $bid;
        } catch (\Exception $e) {
            Log::error("Exception while placing bid: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * Mark this bid as won and apply the hold.
     *
     * @return bool
     */
    public function markAsWon()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $hold = $this->walletHold;

        if (!$hold || !$hold->isActive()) {
            return false;
        }

        // Apply the hold (convert to payment)
        $result = $hold->apply("Payment for winning bid on auction #{$this->auction_id}");

        if ($result) {
            $this->status = 'won';
            $this->save();
            return true;
        }

        return false;
    }

    /**
     * Mark this bid as lost and release the hold.
     *
     * @param string $reason Optional reason for marking as lost
     * @return bool
     */
    public function markAsLost($reason = null)
    {
        if ($this->status !== 'active') {
            Log::info("Cannot mark bid #{$this->id} as lost: status is {$this->status}, not active");
            return false;
        }

        $hold = $this->walletHold;

        if (!$hold || !$hold->isActive()) {
            Log::info("Cannot mark bid #{$this->id} as lost: no active hold found");
            return false;
        }

        // Create a description with the reason if provided
        $description = $reason
            ? "Funds refunded for lost bid on auction #{$this->auction_id}: {$reason}"
            : "Funds refunded for lost bid on auction #{$this->auction_id}";

        // Release the hold (this is effectively a refund)
        $transaction = $hold->release($description);

        if ($transaction) {
            $this->status = 'lost';
            $this->save();

            // Log the refund
            Log::info("Refund processed for bid #{$this->id} on auction #{$this->auction_id}: {$hold->amount} refunded to user #{$this->user_id}");

            // Notify the user about the refund
            try {
                $user = $this->user;
                if ($user && method_exists($user, 'notify')) {
                    $user->notify(new BidRefundNotification($this, $transaction));
                    Log::info("Refund notification sent to user #{$this->user_id}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to send refund notification: " . $e->getMessage());
            }

            return true;
        }

        Log::error("Failed to release hold for bid #{$this->id}");
        return false;
    }

    /**
     * Update the bid amount (only for sealed auctions).
     *
     * @param float $newBidAmount
     * @return bool
     */
    public function updateBidAmount($newBidAmount)
    {
        if ($this->status !== 'active') {
            Log::warning("Cannot update bid #{$this->id}: status is {$this->status}, not active");
            return false;
        }

        $auction = $this->auction;

        // Only allow updates for sealed auctions
        if ($auction->auction_type !== 'sealed') {
            Log::warning("Cannot update bid #{$this->id}: auction type is {$auction->auction_type}, not sealed");
            return false;
        }

        // Check if auction is still active
        if (!$auction->isActive()) {
            Log::warning("Cannot update bid #{$this->id}: auction #{$auction->id} is not active");
            return false;
        }

        // Validate new bid amount
        if ($newBidAmount < $auction->starting_price) {
            Log::warning("Cannot update bid #{$this->id}: new amount {$newBidAmount} is below starting price {$auction->starting_price}");
            return false;
        }

        $oldBidAmount = $this->bid_amount;
        $user = $this->user;
        $wallet = $user->getWallet();

        try {
            // Get the current hold
            $currentHold = $this->walletHold;

            if (!$currentHold || !$currentHold->isActive()) {
                Log::error("Cannot update bid #{$this->id}: no active hold found");
                return false;
            }

            // Calculate the difference
            $difference = $newBidAmount - $oldBidAmount;

            if ($difference > 0) {
                // Need to increase the hold
                if ($wallet->available_balance < $difference) {
                    Log::warning("Cannot update bid #{$this->id}: insufficient balance for increase. Required: {$difference}, Available: {$wallet->available_balance}");
                    return false;
                }

                // Increase the hold amount
                $result = $currentHold->increaseAmount($difference, "Bid amount increased from {$oldBidAmount} to {$newBidAmount}");
            } elseif ($difference < 0) {
                // Need to decrease the hold
                $decreaseAmount = abs($difference);
                $result = $currentHold->decreaseAmount($decreaseAmount, "Bid amount decreased from {$oldBidAmount} to {$newBidAmount}");
            } else {
                // No change in amount
                $result = true;
            }

            if ($result) {
                // Update the bid amount
                $this->bid_amount = $newBidAmount;
                $this->save();

                Log::info("Bid #{$this->id} amount updated from {$oldBidAmount} to {$newBidAmount}");
                return true;
            } else {
                Log::error("Failed to adjust hold for bid #{$this->id}");
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Exception while updating bid #{$this->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Cancel this bid and release the hold.
     *
     * @return bool
     */
    public function cancel()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $hold = $this->walletHold;

        if (!$hold || !$hold->isActive()) {
            return false;
        }

        // Release the hold
        $result = $hold->release("Funds released for cancelled bid on auction #{$this->auction_id}");

        if ($result) {
            $this->status = 'cancelled';
            $this->save();
            return true;
        }

        return false;
    }
}