<?php

namespace App\Models;

use App\Notifications\BidRefundNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Bid extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'user_id',
        'bid_amount',
        'status', // 'active', 'won', 'lost', 'cancelled'
    ];

    protected $casts = [
        'bid_amount' => 'decimal:2',
    ];

    // Add relationships
    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the wallet hold associated with this bid.
     */
    public function walletHold()
    {
        return $this->hasOne(WalletHold::class, 'reference_id')
            ->where('reference_type', 'bid');
    }

    /**
     * Place a bid with a wallet hold.
     *
     * @param \App\Models\Auction $auction
     * @param \App\Models\User $user
     * @param float $bidAmount
     * @return \App\Models\Bid|false
     */
    public static function placeBid(Auction $auction, User $user, $bidAmount)
    {
        Log::info("Attempting to place bid: Auction #{$auction->id}, User #{$user->id}, Amount: {$bidAmount}");

        // Check if auction is active
        if ($auction->status !== 'active') {
            Log::warning("Bid failed: Auction #{$auction->id} is not active. Status: {$auction->status}");
            return false;
        }

        // Check if auction has started
        if (now() < $auction->start_time) {
            Log::warning("Bid failed: Auction #{$auction->id} has not started yet. Start time: {$auction->start_time}");
            return false;
        }

        // Check if auction has ended
        if (now() > $auction->end_time) {
            Log::warning("Bid failed: Auction #{$auction->id} has already ended. End time: {$auction->end_time}");
            return false;
        }

        // Check if bid amount is valid
        if ($bidAmount <= $auction->current_price) {
            Log::warning("Bid failed: Bid amount {$bidAmount} is not higher than current price {$auction->current_price}");
            return false;
        }

        // Get user's wallet
        $wallet = $user->getWallet();
        Log::info("User wallet: Total: {$wallet->balance}, Available: {$wallet->available_balance}, Held: {$wallet->held_balance}");

        // Check if user has enough available balance
        if ($wallet->available_balance < $bidAmount) {
            Log::warning("Bid failed: Insufficient available balance. Required: {$bidAmount}, Available: {$wallet->available_balance}");
            return false;
        }

        try {
            // Create the bid
            $bid = self::create([
                'auction_id' => $auction->id,
                'user_id' => $user->id,
                'bid_amount' => $bidAmount,
                'status' => 'active',
            ]);

            Log::info("Bid created: #{$bid->id}");

            try {
                // Place a hold on the funds
                $hold = $wallet->placeHold(
                    $bidAmount,
                    "Bid on auction #{$auction->id}",
                    'bid',
                    $bid->id,
                    $auction->end_time
                );

                if (!$hold) {
                    // If hold fails, delete the bid and return false
                    Log::error("Failed to place hold on funds for bid #{$bid->id}");
                    $bid->delete();
                    return false;
                }
            } catch (\Exception $e) {
                Log::error("Exception while placing hold: " . $e->getMessage());
                Log::error($e->getTraceAsString());

                // If there's an exception, delete the bid and return false
                $bid->delete();
                return false;
            }

            Log::info("Hold placed: #{$hold->id} for amount {$hold->amount}");

            // Update the auction's current price
            $auction->update(['current_price' => $bidAmount]);
            Log::info("Auction #{$auction->id} current price updated to {$bidAmount}");

            return $bid;
        } catch (\Exception $e) {
            Log::error("Exception while placing bid: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * Mark this bid as won and apply the hold.
     *
     * @return bool
     */
    public function markAsWon()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $hold = $this->walletHold;

        if (!$hold || !$hold->isActive()) {
            return false;
        }

        // Apply the hold (convert to payment)
        $result = $hold->apply("Payment for winning bid on auction #{$this->auction_id}");

        if ($result) {
            $this->status = 'won';
            $this->save();
            return true;
        }

        return false;
    }

    /**
     * Mark this bid as lost and release the hold.
     *
     * @param string $reason Optional reason for marking as lost
     * @return bool
     */
    public function markAsLost($reason = null)
    {
        if ($this->status !== 'active') {
            Log::info("Cannot mark bid #{$this->id} as lost: status is {$this->status}, not active");
            return false;
        }

        $hold = $this->walletHold;

        if (!$hold || !$hold->isActive()) {
            Log::info("Cannot mark bid #{$this->id} as lost: no active hold found");
            return false;
        }

        // Create a description with the reason if provided
        $description = $reason
            ? "Funds refunded for lost bid on auction #{$this->auction_id}: {$reason}"
            : "Funds refunded for lost bid on auction #{$this->auction_id}";

        // Release the hold (this is effectively a refund)
        $transaction = $hold->release($description);

        if ($transaction) {
            $this->status = 'lost';
            $this->save();

            // Log the refund
            Log::info("Refund processed for bid #{$this->id} on auction #{$this->auction_id}: {$hold->amount} refunded to user #{$this->user_id}");

            // Notify the user about the refund
            try {
                $user = $this->user;
                if ($user && method_exists($user, 'notify')) {
                    $user->notify(new BidRefundNotification($this, $transaction));
                    Log::info("Refund notification sent to user #{$this->user_id}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to send refund notification: " . $e->getMessage());
            }

            return true;
        }

        Log::error("Failed to release hold for bid #{$this->id}");
        return false;
    }

    /**
     * Cancel this bid and release the hold.
     *
     * @return bool
     */
    public function cancel()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $hold = $this->walletHold;

        if (!$hold || !$hold->isActive()) {
            return false;
        }

        // Release the hold
        $result = $hold->release("Funds released for cancelled bid on auction #{$this->auction_id}");

        if ($result) {
            $this->status = 'cancelled';
            $this->save();
            return true;
        }

        return false;
    }
}