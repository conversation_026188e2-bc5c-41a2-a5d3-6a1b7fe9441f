<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AuctionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'item_name',
        'item_description',
        'image',
    ];

    /**
     * Get the auction that owns this item.
     */
    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Get all images for this auction item.
     */
    public function images()
    {
        return $this->hasMany(AuctionItemImage::class);
    }

    /**
     * Get the image URL (for API responses)
     *
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    /**
     * Get the image path attribute (for backward compatibility)
     */
    public function getImagePathAttribute()
    {
        if (!$this->image) {
            return asset('images/default-product.jpg');
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }
}
