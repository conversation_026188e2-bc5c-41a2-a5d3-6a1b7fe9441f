<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AuctionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'product_id',
        'quantity',
        'starting_price',
        'current_price',
        'reserve_price',
    ];

    protected $casts = [
        'starting_price' => 'decimal:2',
        'current_price' => 'decimal:2',
        'reserve_price' => 'decimal:2',
        'quantity' => 'integer',
    ];

    /**
     * Get the auction that owns this item.
     */
    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Get the product for this auction item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the bids for this auction item.
     */
    public function bids()
    {
        return $this->hasMany(Bid::class, 'auction_item_id');
    }

    /**
     * Get the highest bid for this auction item.
     */
    public function highestBid()
    {
        return $this->bids()->orderByDesc('bid_amount')->first();
    }

    /**
     * Get the winning bid for this auction item.
     */
    public function winningBid()
    {
        return $this->bids()->where('status', 'won')->first();
    }
}
