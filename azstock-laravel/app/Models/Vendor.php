<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'slug', 'email', 'logo', 'website', 'phone', 'address', 'description', 'is_active', 'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all auctions created by this vendor.
     */
    public function auctions()
    {
        return $this->hasMany(Auction::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the logo URL (for API responses)
     *
     * @return string|null
     */
    public function getLogoUrl(): ?string
    {
        if (!$this->logo) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->logo, FILTER_VALIDATE_URL)) {
            return $this->logo;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->logo);
    }
}
