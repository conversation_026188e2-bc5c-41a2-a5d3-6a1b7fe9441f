<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Watchlist extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'auction_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns this watchlist item.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the auction that this watchlist item refers to.
     */
    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Scope to get watchlist items for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get watchlist items for active auctions.
     */
    public function scopeActiveAuctions($query)
    {
        return $query->whereHas('auction', function ($q) {
            $q->where('status', 'active')
              ->where('end_time', '>', now());
        });
    }

    /**
     * Scope to get watchlist items for auctions ending soon.
     */
    public function scopeEndingSoon($query, $hours = 24)
    {
        return $query->whereHas('auction', function ($q) use ($hours) {
            $q->where('status', 'active')
              ->where('end_time', '>', now())
              ->where('end_time', '<=', now()->addHours($hours));
        });
    }
}
