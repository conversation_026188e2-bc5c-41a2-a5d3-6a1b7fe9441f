<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AuctionItemImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_item_id',
        'image_path',
        'thumbnail_url',
        'original_name',
        'file_size',
    ];

    protected $casts = [
        'file_size' => 'integer',
    ];

    /**
     * Get the auction item that owns this image.
     */
    public function auctionItem()
    {
        return $this->belongsTo(AuctionItem::class);
    }

    /**
     * Get the full URL for the image.
     *
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image_path) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            return $this->image_path;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image_path);
    }

    /**
     * Get the full URL for the thumbnail.
     *
     * @return string|null
     */
    public function getThumbnailUrl(): ?string
    {
        if (!$this->thumbnail_url) {
            return $this->getImageUrl(); // Fallback to main image
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->thumbnail_url, FILTER_VALIDATE_URL)) {
            return $this->thumbnail_url;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->thumbnail_url);
    }

    /**
     * Get the image path attribute (for backward compatibility)
     */
    public function getImagePathAttribute($value)
    {
        if (!$value) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return $value;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $value);
    }
}
