<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'description', 'price','vendor_id', 'category_id', 'image',
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function auction()
    {
        return $this->hasOne(Auction::class);
    }

    /**
     * Get all auctions this product is part of.
     */
    public function auctions()
    {
        return $this->belongsToMany(Auction::class, 'auction_product')
                    ->withPivot(['quantity', 'starting_price', 'current_price', 'reserve_price'])
                    ->withTimestamps();
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    public function getImagePathAttribute()
    {
        if (!$this->image) {
            return asset('images/default-product.jpg');
        }



        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    /**
     * Get the image URL (for API responses)
     *
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    public function images()
    {
        return $this->hasMany(ProductImage::class);
    }

}
