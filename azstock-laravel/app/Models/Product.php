<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'vendor_id',
        'category_id',
        'image',
        'auction_id',
        'auction_quantity',
        'auction_starting_price',
        'auction_current_price',
        'auction_reserve_price',
    ];

    protected $casts = [
        'auction_quantity' => 'integer',
        'auction_starting_price' => 'decimal:2',
        'auction_current_price' => 'decimal:2',
        'auction_reserve_price' => 'decimal:2',
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the auction this product belongs to (new relationship).
     */
    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    /**
     * Legacy relationship - get auction where this product is the primary product.
     * This maintains backward compatibility.
     */
    public function primaryAuction()
    {
        return $this->hasOne(Auction::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    public function getImagePathAttribute()
    {
        if (!$this->image) {
            return asset('images/default-product.jpg');
        }



        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    /**
     * Get the image URL (for API responses)
     *
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    public function images()
    {
        return $this->hasMany(ProductImage::class);
    }

}
