<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Auction;
use Illuminate\Support\Facades\DB;

// Get or create a product
$product = Product::first();
if (!$product) {
    echo "No products found. Creating a test product...\n";
    $category = Category::first() ?? Category::create(['name' => 'Test Category']);
    $user = User::first();
    if (!$user) {
        echo "No users found. Please create a user first.\n";
        exit;
    }
    $vendor = $user->vendor ?? Vendor::create([
        'user_id' => $user->id,
        'name' => 'Test Vendor',
        'slug' => 'test-vendor',
        'email' => $user->email,
        'is_active' => true
    ]);
    $product = Product::create([
        'name' => 'Test Product',
        'description' => 'This is a test product',
        'image' => 'test.jpg',
        'category_id' => $category->id,
        'vendor_id' => $vendor->id
    ]);
    echo "Test product created with ID: {$product->id}\n";
}

// Create a test auction
$auction = Auction::create([
    'vendor_id' => $product->vendor_id,
    'product_id' => $product->id,
    'start_time' => now()->subHour(),
    'end_time' => now()->addDays(7),
    'starting_price' => 10.00,
    'current_price' => 10.00,
    'reserve_price' => null,
    'status' => 'active'
]);

echo "Test auction created with ID: {$auction->id}\n";
echo "Vendor ID: {$auction->vendor_id}\n";
echo "Product ID: {$auction->product_id}\n";
echo "Starting Price: {$auction->starting_price}\n";
echo "Current Price: {$auction->current_price}\n";
echo "Status: {$auction->status}\n";
echo "Start Time: {$auction->start_time}\n";
echo "End Time: {$auction->end_time}\n";
