<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use Spatie\Permission\Models\Role;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Role Update Test ===" . PHP_EOL;

try {
    // Create an admin user for testing
    $admin = User::where('email', '<EMAIL>')->first();
    if (!$admin) {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'admin',
            'is_verified_buyer' => true,
            'is_verified_vendor' => true,
            'is_verified_admin' => true,
        ]);
        $admin->assignRole('admin');
        echo "Created admin user: " . $admin->email . PHP_EOL;
    }

    // Create a test user
    $testUser = User::where('email', '<EMAIL>')->first();
    if (!$testUser) {
        $testUser = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'buyer',
            'is_verified_buyer' => false,
            'is_verified_vendor' => false,
            'is_verified_admin' => false,
        ]);
        $testUser->assignRole('buyer');
        echo "Created test user: " . $testUser->email . PHP_EOL;
    }

    echo "Initial state:" . PHP_EOL;
    echo "- Primary role: " . $testUser->role . PHP_EOL;
    echo "- Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . PHP_EOL;
    echo "- User roles: " . implode(', ', $testUser->getUserRoles()) . PHP_EOL;

    // Test 1: Update to single role (vendor)
    echo PHP_EOL . "Test 1: Updating to vendor role..." . PHP_EOL;
    $testUser->syncRoles(['vendor']);
    $testUser->update(['role' => 'vendor']);
    $testUser->refresh();
    
    echo "After single role update:" . PHP_EOL;
    echo "- Primary role: " . $testUser->role . PHP_EOL;
    echo "- Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . PHP_EOL;
    echo "- User roles: " . implode(', ', $testUser->getUserRoles()) . PHP_EOL;

    // Test 2: Update to multiple roles
    echo PHP_EOL . "Test 2: Updating to multiple roles (buyer + vendor)..." . PHP_EOL;
    $testUser->syncRoles(['buyer', 'vendor']);
    $testUser->update(['role' => 'buyer']); // Primary role
    $testUser->refresh();
    
    echo "After multi-role update:" . PHP_EOL;
    echo "- Primary role: " . $testUser->role . PHP_EOL;
    echo "- Spatie roles: " . $testUser->roles->pluck('name')->join(', ') . PHP_EOL;
    echo "- User roles: " . implode(', ', $testUser->getUserRoles()) . PHP_EOL;

    // Test 3: Check vendor profile creation
    if ($testUser->isVendor() && !$testUser->vendor) {
        $testUser->vendor()->create([
            'name' => $testUser->name,
            'slug' => \Illuminate\Support\Str::slug($testUser->name . '-' . $testUser->id),
            'email' => $testUser->email,
            'is_active' => true,
        ]);
        echo "- Vendor profile created" . PHP_EOL;
    } else {
        echo "- Vendor profile: " . ($testUser->vendor ? 'EXISTS' : 'NOT NEEDED') . PHP_EOL;
    }

    echo PHP_EOL . "🎉 Role update tests completed successfully!" . PHP_EOL;

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    echo "File: " . $e->getFile() . ":" . $e->getLine() . PHP_EOL;
}
