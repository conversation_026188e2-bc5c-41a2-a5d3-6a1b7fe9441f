<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\WalletHold;
use Illuminate\Support\Facades\Log;

// Get a user
$user = User::first();
if (!$user) {
    echo "No users found. Please create a user first.\n";
    exit;
}

echo "Using user: {$user->name} (ID: {$user->id})\n";

// Get or create wallet
$wallet = $user->getWallet();
echo "User wallet: Total: {$wallet->balance}, Available: {$wallet->available_balance}, Held: {$wallet->held_balance}\n";

// Add funds if needed
if ($wallet->available_balance < 50) {
    echo "Adding funds to wallet...\n";
    try {
        $transaction = $wallet->deposit(100, 'Test deposit');
        echo "Funds added. Transaction ID: {$transaction->id}\n";
    } catch (\Exception $e) {
        echo "Exception while adding funds: " . $e->getMessage() . "\n";
        echo $e->getTraceAsString() . "\n";
        exit;
    }
}

echo "Updated wallet balance: Total: {$wallet->balance}, Available: {$wallet->available_balance}, Held: {$wallet->held_balance}\n";

// Test placing a hold
echo "Testing place hold...\n";
try {
    $hold = $wallet->placeHold(
        25.00,
        "Test hold",
        'test',
        1,
        now()->addHour()
    );
    
    if ($hold) {
        echo "Hold placed successfully! Hold ID: {$hold->id}\n";
        echo "New wallet balance: Total: {$wallet->balance}, Available: {$wallet->available_balance}, Held: {$wallet->held_balance}\n";
        
        // Test releasing the hold
        echo "Testing release hold...\n";
        $transaction = $wallet->releaseHold($hold, 'Test release');
        
        if ($transaction) {
            echo "Hold released successfully! Transaction ID: {$transaction->id}\n";
            echo "New wallet balance: Total: {$wallet->balance}, Available: {$wallet->available_balance}, Held: {$wallet->held_balance}\n";
        } else {
            echo "Failed to release hold.\n";
        }
    } else {
        echo "Failed to place hold.\n";
    }
} catch (\Exception $e) {
    echo "Exception while testing hold: " . $e->getMessage() . "\n";
    echo $e->getTraceAsString() . "\n";
}

// Check the logs
echo "\nCheck the logs at storage/logs/laravel.log for more details.\n";
