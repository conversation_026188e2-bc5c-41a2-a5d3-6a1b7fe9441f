import daisyui from "daisyui";
import plugin from "tailwindcss/plugin";

function withOpacityValue(variable) {
  return ({ opacityValue }) => {
    if (opacityValue === undefined) {
      return `hsl(var(${variable}))`;
    }
    return `hsl(var(${variable}) / ${opacityValue})`;
  };
}

const backfaceVisibility = plugin(function ({ addUtilities }) {
  addUtilities({
    ".backface-visible": {
      "backface-visibility": "visible",
      "-moz-backface-visibility": "visible",
      "-webkit-backface-visibility": "visible",
      "-ms-backface-visibility": "visible",
    },
    ".backface-hidden": {
      "backface-visibility": "hidden",
      "-moz-backface-visibility": "hidden",
      "-webkit-backface-visibility": "hidden",
      "-ms-backface-visibility": "hidden",
    },
  });
});

const config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      screens: {
        xs: { raw: "(max-width: 768px)" }, // max-widht:768px
      },
      colors: {
        text: {
          primary: withOpacityValue("--text-primary"),
          secondary: withOpacityValue("--text-secondary"),},
          background: {
            primary: withOpacityValue("--background"),
            secondary:withOpacityValue("--background-secondary"),
 
          },
      },
     
    },
  },
  plugins: [backfaceVisibility, daisyui],
  daisyui: {
    themes: [
      {
        glamlight: {
          ...require("daisyui/src/theming/themes")["[data-theme=light]"],
       //text colors
       "--text-primary": "189 100% 17%",
       "--text-secondary": "221 100% 88%",
       // background colors
       "--background": "189 100% 17%",
       "--background-secondary":"221 100% 88%",

        },
      },
      {
        glamdark: {
          ...require("daisyui/src/theming/themes")["[data-theme=dark]"],
    //text colors
    "--text-primary": "189 100% 17%",
    "--text-secondary": "221 100% 88%",
    // background colors
    "--background": "189 100% 17%",
    "--background-secondary":"221 100% 88%",

        },
      },
    ], 

  },
};
export default config;
