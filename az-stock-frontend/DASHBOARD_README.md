# Dashboard System Documentation

## Overview

This comprehensive dashboard system provides role-based interfaces for managing the auction platform. The system supports three user roles: Ad<PERSON>, Vendor, and Buyer, each with tailored functionality and access controls.

## Architecture

### File Structure

```
src/
├── app/dashboard/
│   ├── page.tsx                    # Role-based redirect
│   ├── admin/
│   │   ├── page.tsx               # Admin dashboard
│   │   ├── users/page.tsx         # User management
│   │   ├── categories/page.tsx    # Category management
│   │   ├── auctions/page.tsx      # Auction oversight
│   │   └── activity-logs/page.tsx # System activity logs
│   ├── vendor/
│   │   ├── page.tsx               # Vendor dashboard
│   │   ├── products/page.tsx      # Product management
│   │   ├── auctions/page.tsx      # Auction management
│   │   └── analytics/page.tsx     # Vendor analytics
│   └── buyer/
│       ├── page.tsx               # Buyer dashboard
│       ├── bids/page.tsx          # Bid management
│       ├── auctions/page.tsx      # Auction browsing
│       ├── wallet/page.tsx        # Wallet management
│       └── watchlist/page.tsx     # Favorites
├── modules/dashboard/
│   ├── components/
│   │   ├── DashboardLayout.tsx    # Main layout component
│   │   └── shared/
│   │       ├── DataTable.tsx      # Reusable table component
│   │       ├── StatsCard.tsx      # Statistics display
│   │       └── Modal.tsx          # Modal dialogs
│   ├── services/
│   │   ├── AdminService.ts        # Admin API calls
│   │   ├── VendorService.ts       # Vendor API calls
│   │   └── BuyerService.ts        # Buyer API calls
│   └── models/
│       └── DashboardModels.ts     # TypeScript interfaces
```

### API Integration

All dashboard services use the proxy API structure (`/api/proxy`) for secure, authenticated requests to the Laravel backend. The proxy automatically handles:

- Authorization token injection
- CSRF protection
- Error handling
- Request/response formatting

## User Roles & Features

### Admin Dashboard (`/dashboard/admin`)

**Overview**: Complete system administration and oversight

**Features**:
- **User Management**: Create, edit, delete users; manage roles
- **Auction Oversight**: View all auctions, end auctions, process refunds
- **Category Management**: CRUD operations for product categories
- **Activity Monitoring**: System-wide activity logs and analytics
- **System Statistics**: User counts, auction metrics, revenue tracking

**Key Pages**:
- `/dashboard/admin` - Main dashboard with statistics
- `/dashboard/admin/users` - User management interface
- `/dashboard/admin/categories` - Category management
- `/dashboard/admin/auctions` - All auctions overview
- `/dashboard/admin/activity-logs` - System activity monitoring

### Vendor Dashboard (`/dashboard/vendor`)

**Overview**: Product and auction management for vendors

**Features**:
- **Product Management**: Create, edit, delete products with image uploads
- **Auction Management**: Create auctions, monitor bids, end auctions
- **Analytics**: Sales performance, bid statistics, revenue tracking
- **Inventory Tracking**: Product status and availability
- **Bid Monitoring**: Real-time bid tracking for vendor auctions

**Key Pages**:
- `/dashboard/vendor` - Main dashboard with vendor statistics
- `/dashboard/vendor/products` - Product inventory management
- `/dashboard/vendor/auctions` - Auction creation and management
- `/dashboard/vendor/analytics` - Performance metrics

### Buyer Dashboard (`/dashboard/buyer`)

**Overview**: Bidding activity and wallet management for buyers

**Features**:
- **Bid Management**: View active bids, bid history, cancel bids
- **Auction Browsing**: Search and filter available auctions
- **Wallet Management**: Deposit funds, withdraw money, transaction history
- **Watchlist**: Save favorite auctions for later
- **Purchase History**: Track won auctions and payments

**Key Pages**:
- `/dashboard/buyer` - Main dashboard with buyer statistics
- `/dashboard/buyer/bids` - Bid tracking and management
- `/dashboard/buyer/auctions` - Auction browsing interface
- `/dashboard/buyer/wallet` - Financial management
- `/dashboard/buyer/watchlist` - Saved auctions

## API Endpoints Mapping

### Admin Endpoints
- `GET /admin/users` - User listing with filters
- `POST /admin/users` - Create new user
- `PUT /admin/users/{id}` - Update user
- `DELETE /admin/users/{id}` - Delete user
- `GET /admin/activity-logs` - System activity logs
- `GET /admin/activity-logs/statistics` - Activity analytics

### Vendor Endpoints
- `GET /my-products` - Vendor's products
- `POST /products` - Create product
- `PUT /products/{id}` - Update product
- `DELETE /products/{id}` - Delete product
- `GET /my-auctions` - Vendor's auctions
- `POST /auctions` - Create auction
- `PUT /auctions/{id}` - Update auction
- `POST /auctions/{id}/end` - End auction
- `POST /auctions/{id}/refunds` - Process refunds

### Buyer Endpoints
- `GET /my-bids` - User's bids
- `POST /auctions/{id}/bids` - Place bid
- `POST /bids/{id}/cancel` - Cancel bid
- `GET /wallet` - Wallet information
- `POST /wallet/deposit` - Deposit funds
- `POST /wallet/withdraw` - Withdraw funds
- `GET /wallet/transactions` - Transaction history
- `GET /wallet/holds` - Held funds

### Shared Endpoints
- `GET /auctions` - List auctions (with filters)
- `GET /auctions/{id}` - Single auction details
- `GET /categories` - List categories
- `POST /categories` - Create category (admin only)
- `PUT /categories/{id}` - Update category (admin only)
- `DELETE /categories/{id}` - Delete category (admin only)

## Components

### DashboardLayout
Main layout component providing:
- Role-based navigation sidebar
- Responsive design
- User session management
- Sign-out functionality

### DataTable
Reusable table component featuring:
- Sorting capabilities
- Pagination
- Custom column rendering
- Action buttons
- Loading states
- Empty state handling

### StatsCard
Statistics display component with:
- Icon support
- Change indicators (increase/decrease)
- Loading states
- Customizable styling

### Modal
Dialog component providing:
- Overlay backdrop
- Size variants (sm, md, lg, xl)
- Close button
- Transition animations

## Authentication & Authorization

### Role-Based Access Control
- Dashboard routes are protected by role-based middleware
- Automatic redirection based on user role
- Session validation on each page load
- Unauthorized access prevention

### Session Management
- NextAuth.js integration
- JWT token handling
- Automatic token refresh
- Secure logout functionality

## Development Guidelines

### Adding New Features
1. Create TypeScript interfaces in `DashboardModels.ts`
2. Add API methods to appropriate service class
3. Create page components in role-specific directories
4. Update navigation in `DashboardLayout.tsx`
5. Add proper error handling and loading states

### Best Practices
- Use TypeScript for all components and services
- Implement proper error boundaries
- Add loading states for all async operations
- Follow consistent naming conventions
- Use shared components for common functionality
- Implement proper form validation
- Add confirmation dialogs for destructive actions

### Testing Considerations
- Test role-based access controls
- Verify API integration with proxy
- Test responsive design across devices
- Validate form submissions and error handling
- Test pagination and filtering functionality

## Security Features

### API Security
- All requests go through authenticated proxy
- Automatic token injection
- CSRF protection
- Input validation and sanitization

### Access Control
- Role-based route protection
- Server-side session validation
- Automatic logout on token expiration
- Secure credential handling

## Performance Optimizations

### Client-Side
- Component lazy loading
- Efficient state management
- Optimized re-renders
- Image optimization

### API Integration
- Request caching where appropriate
- Pagination for large datasets
- Efficient filtering and sorting
- Minimal data transfer

## Future Enhancements

### Planned Features
- Real-time notifications
- Advanced analytics dashboards
- Bulk operations
- Export functionality
- Advanced search and filtering
- Mobile app support

### Technical Improvements
- WebSocket integration for real-time updates
- Advanced caching strategies
- Performance monitoring
- Automated testing suite
- CI/CD pipeline integration

## Troubleshooting

### Common Issues
1. **Authentication Errors**: Check session configuration and token handling
2. **API Failures**: Verify proxy configuration and backend connectivity
3. **Permission Denied**: Ensure proper role assignment and route protection
4. **Loading Issues**: Check error boundaries and loading state management

### Debug Tools
- Browser developer tools for client-side debugging
- Network tab for API request monitoring
- Console logs for error tracking
- React Developer Tools for component inspection

## Support

For technical support or feature requests, please refer to the main project documentation or contact the development team.
