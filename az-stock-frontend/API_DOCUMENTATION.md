# AZStock API Documentation

This document provides information about the AZStock API endpoints and how to use them.

## Base URL

All API endpoints are prefixed with `/api`.

## Authentication

The API uses token-based authentication. To access protected endpoints, you need to include the token in the Authorization header:

```
Authorization: Bearer {your_token}
```

### User Roles and Permissions

The system supports a **multi-role system** where users can have multiple roles simultaneously:

- **Buyer**: Can place bids, view auctions and products, manage wallet for purchases
- **Vendor**: Can create and manage products and auctions, receive payments, plus all buyer permissions
- **Admin**: Can manage all resources (products, auctions, users, vendors) regardless of ownership

#### Multi-Role Benefits:

- **Seamless Experience**: Users can buy and sell without separate accounts
- **Flexible Permissions**: Vendors can also participate as buyers in other auctions
- **Unified Wallet**: Single wallet for both buying and selling activities
- **Role Combinations**: Any combination of buyer, vendor, and admin roles

### User Verification System

The platform implements **role-specific verification** to ensure security and trust:

#### Verification Requirements:

- **New Users**: Register as unverified by default
- **Verified Actions**: Only verified users can perform key actions:
  - **Buyers**: Place bids, perform wallet operations
  - **Vendors**: Create products/auctions, receive payments
  - **Admins**: Access admin panel and management features

#### Verification Status:

- **Role-Specific**: Users can be verified for specific roles (buyer, vendor, admin)
- **Independent**: Verification for one role doesn't affect others
- **Admin Controlled**: Only admins can verify/unverify users

#### Admin Registration Restrictions:

- **Public Registration**: Admin role blocked from `/api/register`
- **Bootstrap Exception**: First admin can register if no admins exist
- **Admin Creation**: Only existing admins can create new admin users

### Admin Privileges

Admin users have special privileges and can:

- Update or delete any product (regardless of vendor ownership)
- Update, delete, or end any auction (regardless of vendor ownership)
- Update or delete any vendor profile
- Access all admin-only endpoints for user management
- Process refunds for any auction

### Authentication Endpoints

#### Register

- **URL**: `/api/register`
- **Method**: `POST`
- **Description**: Register a new user with single or multiple roles
- **Request Body (Single Role - Backward Compatible)**:
  ```json
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "role": "buyer"
  }
  ```
- **Request Body (Multi-Role - New Feature)**:
  ```json
  {
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "roles": ["buyer", "vendor"]
  }
  ```
  The `role` field must be one of: `buyer`, `vendor` (admin role restricted). The `roles` field accepts an array of these values. When registering with vendor role, a vendor profile will be automatically created. **New users are unverified by default.**
- **Response**: Returns the user data with all assigned roles, verification status, and a token

#### Login

- **URL**: `/api/login`
- **Method**: `POST`
- **Description**: Login with existing credentials
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**: Returns the user data and a token

#### Logout

- **URL**: `/api/logout`
- **Method**: `POST`
- **Description**: Invalidate the current token
- **Authentication**: Required
- **Response**: Confirmation message

## Categories

### Get All Categories

- **URL**: `/api/categories`
- **Method**: `GET`
- **Description**: Get a list of all categories
- **Authentication**: Not required

### Get a Specific Category

- **URL**: `/api/categories/{category_id}`
- **Method**: `GET`
- **Description**: Get details of a specific category
- **Authentication**: Not required

### Create a Category

- **URL**: `/api/categories`
- **Method**: `POST`
- **Description**: Create a new category
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "name": "Electronics"
  }
  ```

### Update a Category

- **URL**: `/api/categories/{category_id}`
- **Method**: `PUT`
- **Description**: Update an existing category
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "name": "Updated Electronics"
  }
  ```

### Delete a Category

- **URL**: `/api/categories/{category_id}`
- **Method**: `DELETE`
- **Description**: Delete a category
- **Authentication**: Required

## Products

### Get All Products

- **URL**: `/api/products`
- **Method**: `GET`
- **Description**: Get a list of all products with comprehensive filtering
- **Authentication**: Not required
- **Query Parameters**:
  - `category_id` (integer): Filter by category ID
  - `vendor_id` (integer): Filter by vendor ID
  - `search` (string): Search in product names and descriptions
  - `min_price` (numeric): Minimum price filter (based on auction prices)
  - `max_price` (numeric): Maximum price filter (based on auction prices)
  - `has_auction` (boolean): Filter products with/without active auctions
  - `per_page` (integer): Number of products per page (default: 15, max: 100)
- **Response**: Paginated list of products with category, vendor, and auction information

### Get a Specific Product

- **URL**: `/api/products/{product_id}`
- **Method**: `GET`
- **Description**: Get details of a specific product
- **Authentication**: Not required

### Create a Product

- **URL**: `/api/products`
- **Method**: `POST`
- **Description**: Create a new product
- **Authentication**: Required
- **Request Body**: JSON with fields:
  ```json
  {
    "name": "Product Name",
    "description": "Product description",
    "category_id": 1,
    "image_url": "http://localhost:8000/storage/temp/123/featured.jpg",
    "image_urls": [
      "http://localhost:8000/storage/temp/123/image1.jpg",
      "http://localhost:8000/storage/temp/123/image2.jpg"
    ]
  }
  ```
- **Image Fields**:
  - `image_url` (optional): Dedicated featured image URL
  - `image_urls` (optional): Array of additional product image URLs
- **Featured Image Logic**:
  - If `image_url` is provided → Use as featured image
  - If `image_url` is NOT provided but `image_urls` has images → Use first image from `image_urls` as featured
  - If neither provided → No featured image
- **Backward Compatibility**: Still supports the old `image_urls` only approach
- **Note**: Upload images first using `/api/images/upload` endpoint to get URLs

### Update a Product

- **URL**: `/api/products/{product_id}`
- **Method**: `PUT`
- **Description**: Update an existing product
- **Authentication**: Required
- **Authorization**: Product owner (vendor) or admin
- **Request Body**: JSON with fields:
  ```json
  {
    "name": "Updated Product Name",
    "description": "Updated description",
    "category_id": 1,
    "image_url": "http://localhost:8000/storage/temp/123/featured.jpg",
    "image_urls": [
      "http://localhost:8000/storage/temp/123/image1.jpg",
      "http://localhost:8000/storage/temp/123/image2.jpg"
    ],
    "replace_images": true
  }
  ```
- **Image Fields**:
  - `image_url` (optional): Dedicated featured image URL
  - `image_urls` (optional): Array of additional product image URLs
  - `replace_images` (boolean, optional): If `true`, replaces all existing images. If `false` or omitted, adds to existing images.
- **Featured Image Logic**:
  - If `image_url` is provided → Use as featured image
  - If `image_url` is NOT provided but `image_urls` has images → Use first image from `image_urls` as featured
  - If `replace_images` is `true` → Always update featured image with new logic above
  - If `replace_images` is `false` → Only set featured image if product doesn't already have one
- **Note**: Upload images first using `/api/images/upload` endpoint to get URLs

### Delete a Product

- **URL**: `/api/products/{product_id}`
- **Method**: `DELETE`
- **Description**: Delete a product
- **Authentication**: Required
- **Authorization**: Product owner (vendor) or admin

### Get My Products

- **URL**: `/api/my-products`
- **Method**: `GET`
- **Description**: Get products created by the authenticated vendor with filtering
- **Authentication**: Required
- **Authorization**: Vendor role required
- **Query Parameters**:
  - `category_id` (integer): Filter by category ID
  - `search` (string): Search in product names and descriptions
  - `has_auction` (boolean): Filter products with/without auctions
  - `status` (string): Filter by product status (for future use: active, inactive)
  - `per_page` (integer): Number of products per page (default: 15, max: 100)
- **Response**: Returns a paginated list of products created by the authenticated vendor

## Images

### Upload Images (General)

- **URL**: `/api/images/upload`
- **Method**: `POST`
- **Description**: Upload images to temporary storage (use this before creating/updating products)
- **Authentication**: Required
- **Authorization**: Vendor role required
- **Request Body**: Form data with:
  - `images[]`: Array of image files (max 10 files, 5MB each)
- **Supported formats**: JPEG, PNG, GIF, WebP
- **Response**: Returns uploaded image URLs that can be used in product creation/update
  ```json
  {
    "message": "Images uploaded successfully",
    "images": [
      {
        "url": "http://localhost:8000/storage/temp/123/2024-01-15/image1.jpg",
        "thumbnail_url": "http://localhost:8000/storage/temp/123/2024-01-15/image1_thumb.jpg",
        "original_name": "my-image.jpg",
        "path": "temp/123/2024-01-15/image1.jpg"
      }
    ],
    "upload_errors": []
  }
  ```

### Upload Product Images (Legacy)

- **URL**: `/api/images/products`
- **Method**: `POST`
- **Description**: Upload images directly for a specific product (legacy endpoint)
- **Authentication**: Required
- **Authorization**: Must be the vendor who owns the product
- **Request Body**: Form data with:
  - `product_id`: ID of the product
  - `images[]`: Array of image files (max 10 files, 5MB each)
- **Supported formats**: JPEG, PNG, GIF, WebP
- **Response**: Returns uploaded image details and any upload errors

### Delete Product Image

- **URL**: `/api/images/{image_id}`
- **Method**: `DELETE`
- **Description**: Delete a product image
- **Authentication**: Required
- **Authorization**: Must be the vendor who owns the product
- **Response**: Confirmation message

### Get Product Images

- **URL**: `/api/products/{product_id}/images`
- **Method**: `GET`
- **Description**: Get all images for a product
- **Authentication**: Not required
- **Response**: Returns array of image details including URLs and thumbnails

## Categories

### Get All Categories

- **URL**: `/api/categories`
- **Method**: `GET`
- **Description**: Get a list of all categories with filtering
- **Authentication**: Not required
- **Query Parameters**:
  - `search` (string): Search in category names
  - `has_products` (boolean): Filter categories with/without products
  - `per_page` (integer): Number of categories per page (default: 15, max: 100)
- **Response**: Paginated list of categories with product counts

### Get a Specific Category

- **URL**: `/api/categories/{category_id}`
- **Method**: `GET`
- **Description**: Get details of a specific category
- **Authentication**: Not required

### Create a Category

- **URL**: `/api/categories`
- **Method**: `POST`
- **Description**: Create a new category
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "name": "Electronics"
  }
  ```

### Update a Category

- **URL**: `/api/categories/{category_id}`
- **Method**: `PUT`
- **Description**: Update an existing category
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "name": "Updated Electronics"
  }
  ```

### Delete a Category

- **URL**: `/api/categories/{category_id}`
- **Method**: `DELETE`
- **Description**: Delete a category
- **Authentication**: Required

### Get My Categories

- **URL**: `/api/my-categories`
- **Method**: `GET`
- **Description**: Get categories created by the authenticated user
- **Authentication**: Required
- **Query Parameters**:
  - `per_page`: Number of categories per page (default: 15)
- **Response**: Returns a paginated list of categories created by the authenticated user

## Auctions

### Get All Auctions

- **URL**: `/api/auctions`
- **Method**: `GET`
- **Description**: Get a list of all auctions with comprehensive filtering
- **Authentication**: Not required
- **Query Parameters**:
  - `status` (string): Filter by status (active, ended, upcoming)
  - `category_id` (integer): Filter by product category ID
  - `vendor_id` (integer): Filter by vendor ID
  - `search` (string): Search in product names and descriptions
  - `min_price` (numeric): Minimum current/starting price
  - `max_price` (numeric): Maximum current/starting price
  - `ending_soon` (boolean): Filter auctions ending within 24 hours
  - `per_page` (integer): Number of auctions per page (default: 15, max: 100)
- **Response**: Paginated list of auctions with product and vendor information

### Get a Specific Auction

- **URL**: `/api/auctions/{auction_id}`
- **Method**: `GET`
- **Description**: Get details of a specific auction
- **Authentication**: Not required

### Get My Auctions

- **URL**: `/api/my-auctions`
- **Method**: `GET`
- **Description**: Get auctions created by the authenticated vendor with filtering
- **Authentication**: Required
- **Authorization**: Vendor role required
- **Query Parameters**:
  - `status` (string): Filter by status (active, ended, upcoming)
  - `category_id` (integer): Filter by product category ID
  - `search` (string): Search in product names and descriptions
  - `ending_soon` (boolean): Filter auctions ending within 24 hours
  - `per_page` (integer): Number of auctions per page (default: 15, max: 100)
- **Response**: Paginated list of auctions created by the authenticated vendor

### Create an Auction

- **URL**: `/api/auctions`
- **Method**: `POST`
- **Description**: Create a new auction
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "product_id": 1,
    "start_time": "2023-01-01T12:00:00",
    "end_time": "2023-01-10T12:00:00",
    "starting_price": 100.0,
    "reserve_price": 150.0
  }
  ```

### Update an Auction

- **URL**: `/api/auctions/{auction_id}`
- **Method**: `PUT`
- **Description**: Update an existing auction
- **Authentication**: Required
- **Authorization**: Auction owner (vendor) or admin
- **Request Body**:
  ```json
  {
    "end_time": "2023-01-15T12:00:00",
    "reserve_price": 200.0,
    "status": "active"
  }
  ```

### Delete an Auction

- **URL**: `/api/auctions/{auction_id}`
- **Method**: `DELETE`
- **Description**: Delete an auction (only if no bids have been placed)
- **Authentication**: Required
- **Authorization**: Auction owner (vendor) or admin

### End an Auction

- **URL**: `/api/auctions/{auction_id}/end`
- **Method**: `POST`
- **Description**: Manually end an active auction
- **Authentication**: Required
- **Authorization**: Auction owner (vendor) or admin
- **Authorization**: Only the vendor who created the auction or an admin can end it
- **Response**: Returns the auction details with the winning bid information

### Process Refunds for an Auction

- **URL**: `/api/auctions/{auction_id}/refunds`
- **Method**: `POST`
- **Description**: Manually process refunds for unsuccessful bids on an ended auction
- **Authentication**: Required
- **Authorization**: Only the vendor who created the auction or an admin can process refunds
- **Response**: Returns details about the processed refunds
  ```json
  {
    "message": "Refunds processed successfully for auction #1",
    "processed_bids": 3,
    "failed_bids": 0,
    "refunded_amount": 250.0
  }
  ```

## Bids

### Place a Bid

- **URL**: `/api/auctions/{auction_id}/bids`
- **Method**: `POST`
- **Description**: Place a bid on an auction
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "bid_amount": 120.0
  }
  ```

### Get My Bids

- **URL**: `/api/my-bids`
- **Method**: `GET`
- **Description**: Get bids placed by the authenticated user with comprehensive filtering
- **Authentication**: Required
- **Query Parameters**:
  - `status` (string): Filter by bid status (active, won, lost, cancelled)
  - `auction_status` (string): Filter by auction status (active, ended, upcoming)
  - `category_id` (integer): Filter by product category ID
  - `search` (string): Search in product names and descriptions
  - `min_amount` (numeric): Minimum bid amount filter
  - `max_amount` (numeric): Maximum bid amount filter
  - `date_from` (date): Filter bids from this date
  - `date_to` (date): Filter bids to this date
  - `per_page` (integer): Number of bids per page (default: 15, max: 100)
- **Response**: Paginated list of bids with auction, product, and wallet information

### Cancel a Bid

- **URL**: `/api/bids/{bid_id}/cancel`
- **Method**: `POST`
- **Description**: Cancel an active bid and release the hold on funds
- **Authentication**: Required
- **Response**: Returns the updated bid details and wallet balance

## User

### Get User Profile

- **URL**: `/api/user`
- **Method**: `GET`
- **Description**: Get the authenticated user's profile
- **Authentication**: Required

### User Verification Endpoints (Admin Only)

#### Verify User for Role

- **URL**: `/api/admin/users/{user}/verify`
- **Method**: `POST`
- **Description**: Verify or unverify a user for a specific role
- **Authentication**: Required
- **Authorization**: Admin role required
- **Request Body**:
  ```json
  {
    "role": "vendor",
    "verified": true
  }
  ```
- **Response**: Returns updated user data with verification status

#### Get User Verification Status

- **URL**: `/api/admin/users/{user}/verification-status`
- **Method**: `GET`
- **Description**: Get detailed verification status for a user
- **Authentication**: Required
- **Authorization**: Admin role required
- **Response**: Returns user verification status for all roles
  ```json
  {
    "user_id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "verification_status": {
      "buyer": true,
      "vendor": false,
      "admin": false
    },
    "roles": ["buyer", "vendor"]
  }
  ```

### Get All Users (Admin Only)

- **URL**: `/api/admin/users`
- **Method**: `GET`
- **Description**: Get a list of all users with comprehensive filtering (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Query Parameters**:
  - `role` (string): Filter by role (buyer, vendor, admin)
  - `is_active` (boolean): Filter by active status (based on email verification)
  - `search` (string): Search in user names and emails
  - `created_after` (date): Filter by registration date (from)
  - `created_before` (date): Filter by registration date (to)
  - `per_page` (integer): Number of users per page (default: 15, max: 100)
- **Response**: Returns a paginated list of users with vendor and wallet information

### Create User (Admin Only)

- **URL**: `/api/admin/users`
- **Method**: `POST`
- **Description**: Create a new user (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Request Body**:
  ```json
  {
    "name": "Amazon",
    "email": "<EMAIL>",
    "password": "Anan@123$%",
    "password_confirmation": "Anan@123$%",
    "role": "buyer"
  }
  ```
  The `role` field must be one of: `buyer`, `vendor`, or `admin`. When creating a vendor, a vendor profile will be automatically created.
- **Response**: Returns the created user details with a success message

### Get User by ID (Admin Only)

- **URL**: `/api/admin/users/{user_id}`
- **Method**: `GET`
- **Description**: Get details of a specific user (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Response**: Returns the user details including role and related information

### Update User (Admin Only)

- **URL**: `/api/admin/users/{user_id}`
- **Method**: `PUT`
- **Description**: Update a user's information (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Request Body**:
  ```json
  {
    "name": "Updated Name",
    "email": "<EMAIL>",
    "role": "vendor",
    "password": "newpassword123"
  }
  ```
- **Response**: Returns the updated user details

### Delete User (Admin Only)

- **URL**: `/api/admin/users/{user_id}`
- **Method**: `DELETE`
- **Description**: Delete a user (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Response**: Confirmation message

## Activity Logs (Admin Only)

### Get Activity Logs

- **URL**: `/api/admin/activity-logs`
- **Method**: `GET`
- **Description**: Get a list of activity logs (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Query Parameters**:
  - `user_id`: Filter by user ID
  - `action_type`: Filter by action type (auth, crud, system)
  - `entity_type`: Filter by entity type (user, product, auction, bid)
  - `entity_id`: Filter by entity ID (requires entity_type to be specified)
  - `start_date`: Filter by start date (YYYY-MM-DD)
  - `end_date`: Filter by end date (YYYY-MM-DD)
  - `per_page`: Number of logs per page (default: 15)
- **Response**: Returns a paginated list of activity logs

### Get Activity Log Statistics

- **URL**: `/api/admin/activity-logs/statistics`
- **Method**: `GET`
- **Description**: Get activity log statistics (admin access required)
- **Authentication**: Required
- **Authorization**: Admin role required
- **Query Parameters**:
  - `start_date`: Filter by start date (YYYY-MM-DD)
  - `end_date`: Filter by end date (YYYY-MM-DD)
- **Response**: Returns statistics about activity logs including:
  - Total logs count
  - Logs by action type
  - Logs by entity type
  - Top users by activity
  - Daily activity counts

## Wallet

### Get Wallet

- **URL**: `/api/wallet`
- **Method**: `GET`
- **Description**: Get the authenticated user's wallet information
- **Authentication**: Required
- **Response**: Returns wallet details including balance, held balance, and available balance

### Deposit Funds

- **URL**: `/api/wallet/deposit`
- **Method**: `POST`
- **Description**: Deposit funds into the authenticated user's wallet
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "amount": 100.0,
    "payment_method": "credit_card",
    "payment_details": {
      "card_number": "****************",
      "expiry_month": 12,
      "expiry_year": 2025,
      "cvc": "123"
    }
  }
  ```
- **Response**: Returns transaction details and updated wallet balance

### Withdraw Funds

- **URL**: `/api/wallet/withdraw`
- **Method**: `POST`
- **Description**: Withdraw funds from the authenticated user's wallet
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "amount": 50.0,
    "withdrawal_method": "bank_transfer",
    "withdrawal_details": {
      "bank_name": "Example Bank",
      "account_number": "**********",
      "routing_number": "*********"
    }
  }
  ```
- **Response**: Returns transaction details and updated wallet balance

### Get Wallet Transactions

- **URL**: `/api/wallet/transactions`
- **Method**: `GET`
- **Description**: Get the authenticated user's wallet transactions
- **Authentication**: Required
- **Query Parameters**:
  - `type`: Filter by transaction type (deposit, withdrawal, hold, release, payment)
  - `per_page`: Number of transactions per page (default: 15)
- **Response**: Returns a paginated list of transactions

### Get Wallet Holds

- **URL**: `/api/wallet/holds`
- **Method**: `GET`
- **Description**: Get the authenticated user's wallet holds
- **Authentication**: Required
- **Query Parameters**:
  - `status`: Filter by hold status (active, released, applied)
  - `per_page`: Number of holds per page (default: 15)
- **Response**: Returns a paginated list of holds

## Filtering and Pagination

### Pagination

All list endpoints support pagination with the following parameters:

- `per_page` (integer): Number of items per page (default: 15, max: 100)
- Responses include pagination metadata: `current_page`, `last_page`, `total`, `per_page`, etc.

### Common Filter Parameters

- `search` (string): Case-insensitive search in relevant text fields
- `category_id` (integer): Filter by category (for products, auctions, bids)
- `vendor_id` (integer): Filter by vendor (for products, auctions)
- `status` (string): Filter by status (varies by endpoint)
- `per_page` (integer): Items per page (1-100)

### Date Range Filters

- `created_after` / `date_from` (date): Start date filter
- `created_before` / `date_to` (date): End date filter
- Format: `YYYY-MM-DD`

### Price Range Filters

- `min_price` / `min_amount` (numeric): Minimum price/amount
- `max_price` / `max_amount` (numeric): Maximum price/amount

### Boolean Filters

- `has_auction` (boolean): Products with/without auctions
- `has_products` (boolean): Categories with/without products
- `is_active` (boolean): Active/inactive status
- `ending_soon` (boolean): Auctions ending within 24 hours

### Performance Notes

- Complex filters with multiple parameters are logged for monitoring
- Database indexes are recommended for frequently filtered columns
- Use specific filters rather than broad searches for better performance

## Error Handling

All API endpoints return appropriate HTTP status codes:

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Not authorized to perform the action
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation errors

Error responses include a message explaining the error.
