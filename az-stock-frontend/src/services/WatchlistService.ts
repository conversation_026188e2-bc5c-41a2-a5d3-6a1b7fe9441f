import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import {
  WatchlistResponse,
  WatchlistItemModel,
  WatchlistStatsModel,
} from "@/types/auction";

export default class WatchlistService {
  methods = {
    async getWatchlist(): Promise<WatchlistItemModel[]> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to access your watchlist");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<WatchlistResponse>(
          `/watchlist`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        return response.data || [];
      } catch (error) {
        console.error("Error fetching watchlist:", error);
        return [];
      }
    },

    async addToWatchlist(auctionId: number): Promise<WatchlistItemModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to add items to your watchlist");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<{ data: WatchlistItemModel; message: string }>(
          `/watchlist`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({ auction_id: auctionId }),
          }
        );

        if (!response || !response.data) {
          throw new Error("Invalid response format from watchlist API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error adding to watchlist:", error);
        if (error.message) {
          throw new Error(error.message);
        } else {
          throw new Error("Failed to add item to watchlist. Please try again.");
        }
      }
    },

    async removeFromWatchlist(auctionId: number): Promise<void> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to remove items from your watchlist");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        await GlobalFetchJson<{ message: string }>(
          `/watchlist/${auctionId}`,
          {
            method: "DELETE",
            headers: headers,
          }
        );
      } catch (error: any) {
        console.error("Error removing from watchlist:", error);
        if (error.message) {
          throw new Error(error.message);
        } else {
          throw new Error("Failed to remove item from watchlist. Please try again.");
        }
      }
    },

    async checkWatchlistStatus(auctionId: number): Promise<boolean> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          return false;
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<{ is_in_watchlist: boolean }>(
          `/watchlist/check/${auctionId}`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        return response.is_in_watchlist || false;
      } catch (error) {
        console.error("Error checking watchlist status:", error);
        return false;
      }
    },

    async getWatchlistStats(): Promise<WatchlistStatsModel | null> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to access watchlist statistics");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<WatchlistStatsModel>(
          `/watchlist/stats`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        return response;
      } catch (error) {
        console.error("Error fetching watchlist stats:", error);
        return null;
      }
    },
  };
}
