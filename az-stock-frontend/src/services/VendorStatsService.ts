import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import {
  VendorStatsModel,
  VendorDashboardStatsModel,
} from "@/types/auction";

export default class VendorStatsService {
  methods = {
    async getVendorStats(): Promise<VendorStatsModel | null> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to access vendor statistics");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<VendorStatsModel>(
          `/vendor/stats`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        return response;
      } catch (error) {
        console.error("Error fetching vendor stats:", error);
        return null;
      }
    },

    async getDashboardStats(): Promise<VendorDashboardStatsModel | null> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to access dashboard statistics");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<VendorDashboardStatsModel>(
          `/vendor/dashboard-stats`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        return response;
      } catch (error) {
        console.error("Error fetching dashboard stats:", error);
        return null;
      }
    },
  };
}
