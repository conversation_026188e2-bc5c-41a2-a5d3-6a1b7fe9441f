import { apiClient } from './apiClient';
import { 
  PlaceBidRequest, 
  PlaceBidResponse, 
  UpdateBidRequest,
  CancelBidResponse,
  BidModel 
} from '@/types/auction';

export class BidService {
  /**
   * Place a bid on an auction
   */
  async placeBid(auctionId: number, bidData: PlaceBidRequest): Promise<PlaceBidResponse> {
    const response = await apiClient.post<PlaceBidResponse>(
      `/auctions/${auctionId}/bids`,
      bidData
    );
    return response.data;
  }

  /**
   * Update a bid (sealed auctions only)
   */
  async updateBid(bidId: number, bidData: UpdateBidRequest): Promise<PlaceBidResponse> {
    const response = await apiClient.put<PlaceBidResponse>(
      `/bids/${bidId}`,
      bidData
    );
    return response.data;
  }

  /**
   * Cancel a bid (sealed auctions only)
   */
  async cancelBid(bidId: number): Promise<CancelBidResponse> {
    const response = await apiClient.post<CancelBidResponse>(
      `/bids/${bidId}/cancel`
    );
    return response.data;
  }

  /**
   * Get user's bids with filtering options
   */
  async getMyBids(params?: {
    status?: 'active' | 'won' | 'lost' | 'cancelled';
    auction_status?: 'active' | 'ended' | 'upcoming';
    category_id?: number;
    search?: string;
    min_amount?: number;
    max_amount?: number;
    date_from?: string;
    date_to?: string;
    per_page?: number;
    page?: number;
  }) {
    const response = await apiClient.get('/my-bids', { params });
    return response.data;
  }

  /**
   * Check if a bid can be updated (sealed auctions only)
   */
  canUpdateBid(auction: { auction_type: string; status: string; end_time: string }, bid: BidModel): boolean {
    // Only sealed auctions allow bid updates
    if (auction.auction_type !== 'sealed') {
      return false;
    }

    // Bid must be active
    if (bid.status !== 'active') {
      return false;
    }

    // Auction must be active and not ended
    if (auction.status !== 'active') {
      return false;
    }

    // Check if auction hasn't ended yet
    const endTime = new Date(auction.end_time);
    const now = new Date();
    if (now >= endTime) {
      return false;
    }

    return true;
  }

  /**
   * Check if a bid can be canceled (sealed auctions only)
   */
  canCancelBid(auction: { auction_type: string; status: string; end_time: string }, bid: BidModel): boolean {
    // Only sealed auctions allow bid cancellation
    if (auction.auction_type !== 'sealed') {
      return false;
    }

    // Bid must be active
    if (bid.status !== 'active') {
      return false;
    }

    // Auction must be active and not ended
    if (auction.status !== 'active') {
      return false;
    }

    // Check if auction hasn't ended yet
    const endTime = new Date(auction.end_time);
    const now = new Date();
    if (now >= endTime) {
      return false;
    }

    return true;
  }

  /**
   * Get minimum bid amount for an auction
   */
  getMinimumBidAmount(auction: { auction_type: string; starting_price: string; current_price: string }): number {
    if (auction.auction_type === 'sealed') {
      // For sealed auctions, minimum is starting price
      return parseFloat(auction.starting_price);
    } else {
      // For online auctions, minimum is higher than current price
      const currentPrice = parseFloat(auction.current_price);
      const startingPrice = parseFloat(auction.starting_price);
      return Math.max(currentPrice + 0.01, startingPrice);
    }
  }

  /**
   * Check if bids should be visible for an auction
   */
  shouldShowBids(auction: { auction_type: string; status: string }): boolean {
    // Online auctions: always show bids
    if (auction.auction_type === 'online') {
      return true;
    }

    // Sealed auctions: only show bids after auction ends
    if (auction.auction_type === 'sealed') {
      return auction.status === 'completed' || auction.status === 'ended';
    }

    return false;
  }

  /**
   * Get bid validation message for different auction types
   */
  getBidValidationMessage(auction: { auction_type: string; starting_price: string; current_price: string }): string {
    const minAmount = this.getMinimumBidAmount(auction);
    
    if (auction.auction_type === 'sealed') {
      return `Minimum bid: $${minAmount.toFixed(2)} (starting price)`;
    } else {
      return `Minimum bid: $${minAmount.toFixed(2)} (higher than current bid)`;
    }
  }
}

export const bidService = new BidService();
