import GlobalError from "../../models/GlobalError";
import { API_URL } from "@/config/apiUrl";

// Custom fetch wrapper
export async function GlobalFetchJson<T>(
  url: string,
  params?: RequestInit,
  base_url?: string
): Promise<T> {
  let fullUrl: string;

  if (base_url) {
    if (base_url.startsWith("/api/proxy")) {
      // For proxy routes, construct the full URL properly
      if (typeof window !== "undefined") {
        // Client-side: use window.location.origin
        fullUrl = `${window.location.origin}${base_url}${url}`;
      } else {
        // Server-side: use the base URL as is (should not happen with proxy)
        fullUrl = `${base_url}${url}`;
      }
    } else {
      // For other base URLs
      fullUrl = `${base_url}${url}`;
    }
  } else {
    // Default to API_URL
    fullUrl = `${API_URL}${url}`;
  }

  const isProxy = base_url?.startsWith("/api/proxy");

  console.log("Fetching from URL:", fullUrl);
  console.log("URL components:", { url, base_url, isProxy });

  params = {
    method: params?.method ?? "GET",
    ...params,
  };

  // Only add Content-Type for non-FormData bodies
  const isFormData = params.body instanceof FormData;

  params.headers = {
    ...(isFormData ? {} : { "Content-Type": "application/json" }),
    ...params?.headers,
  };

  // For non-proxy requests, include credentials for session cookies
  if (!isProxy) {
    params.credentials = "include";

    // For direct API calls, try to get session client-side
    if (typeof window !== "undefined") {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();
        if (session?.accessToken) {
          (params.headers as Record<string, string>)[
            "Authorization"
          ] = `Bearer ${session.accessToken}`;
        }
      } catch (error) {
        console.warn("Could not get session for direct API call:", error);
      }
    }
  }

  // For proxy requests, add API-specific headers
  if (isProxy) {
    (params.headers as Record<string, string>)["Accept"] = "application/json";
    (params.headers as Record<string, string>)["X-Requested-With"] =
      "XMLHttpRequest";
  }

  try {
    const response = await fetch(fullUrl, params);
    const responseBody = await response.json(); // Only parse once!

    if (!response.ok) {
      console.error("API Error Response:", responseBody);
      throw responseBody as GlobalError;
    }

    console.log("API Success Response:", responseBody);
    return responseBody as T;
  } catch (error) {
    console.error("Fetch error:", error);
    throw error;
  }
}
