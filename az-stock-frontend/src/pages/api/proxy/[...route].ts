import { authOptions } from "@/utils/auth";
import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth";
import httpProxyMiddleware from "next-http-proxy-middleware";
const ProxyHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const session = await getServerSession(req, res, authOptions);
  httpProxyMiddleware(req, res, {
    target: process.env.NEXT_PUBLIC_API_URL,
    pathRewrite: [
      {
        patternStr: "/api/proxy",
        replaceStr: "",
      },
    ],
    onProxyInit: (proxy) => {
      /**
       * Check the list of bindable events in the `http-proxy` specification.
       * @see https://www.npmjs.com/package/http-proxy#listening-for-proxy-events
       */
      proxy.on("proxyReq", (proxyReq, req, res) => {
        if (session) {
          proxyReq.setHeader("Authorization", `Bearer ${session?.accessToken}`);
        }
      });
      proxy.on("proxyRes", (proxyRes, req, res) => {});
    },
  });
};
export const config = {
  api: {
    bodyParser: false,
    externalResolver: true,
  },
};
export default ProxyHandler;
