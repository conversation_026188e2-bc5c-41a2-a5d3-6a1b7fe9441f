import NextAuth from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: "admin" | "vendor" | "buyer";
      roles?: ("admin" | "vendor" | "buyer")[];
      verification_status?: {
        buyer?: boolean;
        vendor?: boolean;
        admin?: boolean;
      };
    };
    accessToken?: string;
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: "admin" | "vendor" | "buyer";
    roles?: ("admin" | "vendor" | "buyer")[];
    verification_status?: {
      buyer?: boolean;
      vendor?: boolean;
      admin?: boolean;
    };
    accessToken?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    role?: "admin" | "vendor" | "buyer";
    roles?: ("admin" | "vendor" | "buyer")[];
    verification_status?: {
      buyer?: boolean;
      vendor?: boolean;
      admin?: boolean;
    };
    accessToken?: string;
  }
}
