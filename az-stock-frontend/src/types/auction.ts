// Updated auction-based models to match the new API structure

export interface AuctionItemImageModel {
  id: number;
  url: string;
  thumbnail_url: string;
  original_name: string;
  file_size: number | null;
  created_at: string;
}

export interface AuctionItemModel {
  id: number;
  item_name: string;
  item_description: string | null;
  created_at: string;
  updated_at: string;
  images: AuctionItemImageModel[];
}

export interface CategoryModel {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  auctions_count?: number;
}

export interface VendorModel {
  id: number;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  address?: string;
  description?: string;
  website?: string;
  logo: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  auctions_count?: number;
  total_auctions_count?: number;
  active_auctions_count?: number;
  completed_auctions_count?: number;
  total_reviews_count?: number;
  average_rating?: number;
  member_since?: string;
}

export interface BidModel {
  id: number;
  bid_amount: string;
  created_at: string;
  updated_at: string;
  user_id: number;
  auction_id: number;
  status: 'active' | 'won' | 'lost' | 'cancelled';
}

export interface AuctionModel {
  id: number;
  title: string;
  description: string | null;
  auction_type: 'sealed' | 'online';
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  starting_price: string;
  current_price: string;
  reserve_price: string | null;
  featured_image?: string | null;
  start_time: string;
  end_time: string;
  created_at: string;
  updated_at: string;
  category: CategoryModel;
  vendor: VendorModel;
  auction_items: AuctionItemModel[];
  bids?: BidModel[];
  bids_count?: number;
  is_active: boolean;
}

// API Response interfaces
export interface AuctionResponse {
  data: AuctionModel[];
  links?: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface SingleAuctionResponse {
  data: AuctionModel;
}

export interface CategoryResponse {
  data: CategoryModel[];
  links?: any;
  meta?: any;
}

export interface SingleCategoryResponse {
  data: CategoryModel;
}

// Watchlist models
export interface WatchlistItemModel {
  id: number;
  auction: AuctionModel;
  added_at: string;
}

export interface WatchlistResponse {
  data: WatchlistItemModel[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface WatchlistStatsModel {
  total_items: number;
  active_auctions: number;
  ending_soon: number;
}

// Vendor statistics models
export interface VendorStatsModel {
  vendor_info: {
    id: number;
    name: string;
    email: string;
    is_active: boolean;
  };
  auction_stats: {
    total_auctions: number;
    active_auctions: number;
    completed_auctions: number;
    draft_auctions: number;
    success_rate: number;
  };
  bid_stats: {
    total_bids_received: number;
    unique_bidders: number;
    average_bids_per_auction: number;
  };
  revenue_stats: {
    total_revenue: number;
    average_sale_price: number;
    successful_auctions: number;
  };
  recent_activity: {
    auctions_last_30_days: number;
    bids_last_30_days: number;
  };
  top_auctions: any[];
  monthly_performance: any[];
  category_performance: any[];
}

export interface VendorDashboardStatsModel {
  active_auctions: number;
  ending_soon: number;
  recent_bids: number;
  this_month_revenue: number;
}

// Create auction request interface
export interface CreateAuctionRequest {
  title: string;
  description?: string;
  category_id: number;
  auction_type: 'sealed' | 'online';
  start_time: string;
  end_time: string;
  starting_price: number;
  reserve_price?: number;
  featured_image?: string;
  items: {
    item_name: string;
    item_description?: string;
    images?: string[];
  }[];
}

// Place bid request interface
export interface PlaceBidRequest {
  bid_amount: number;
}

// Update bid request interface (sealed auctions only)
export interface UpdateBidRequest {
  bid_amount: number;
}

// API response for placing/updating bid
export interface PlaceBidResponse {
  data: BidModel;
  message: string;
  wallet_balance?: {
    total: number;
    available: number;
    held: number;
  };
}

// API response for canceling bid
export interface CancelBidResponse {
  data: BidModel;
  message: string;
  wallet_balance?: {
    total: number;
    available: number;
    held: number;
  };
}

// API response for getting user's bids
export interface MyBidsResponse {
  data: BidModel[];
  current_page?: number;
  last_page?: number;
  per_page?: number;
  total?: number;
  wallet_balance?: {
    total: number;
    available: number;
    held: number;
  };
}
