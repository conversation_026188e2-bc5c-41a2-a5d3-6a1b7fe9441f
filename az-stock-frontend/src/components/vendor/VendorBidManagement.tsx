"use client";

import React, { useState, useEffect } from "react";
import { AuctionModel } from "@/types/auction";
import {
  vendorBidService,
  VendorBidsResponse,
  VendorBidModel,
  VendorBidsFilters,
} from "@/modules/vendor/services/VendorBidService";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import { ErrorMessage } from "@/components/common/ErrorMessage";

interface VendorBidManagementProps {
  auction: AuctionModel;
}

const VendorBidManagement: React.FC<VendorBidManagementProps> = ({
  auction,
}) => {
  const [bidsData, setBidsData] = useState<VendorBidsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<VendorBidsFilters>({
    sort_by: "amount",
    sort_order: "desc",
  });

  const fetchBids = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await vendorBidService.getAuctionBids(
        auction.id,
        filters
      );
      setBidsData(response);
    } catch (err: any) {
      setError(err.message || "Failed to load bids");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBids();
  }, [auction.id, filters]);

  const handleFilterChange = (newFilters: Partial<VendorBidsFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const canViewBids = vendorBidService.canViewBids(
    auction.auction_type,
    auction.status
  );

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Auction Bids
        </h3>
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Auction Bids
        </h3>
        <ErrorMessage message={error} onRetry={fetchBids} />
      </div>
    );
  }

  if (!bidsData) {
    return null;
  }

  // Sealed auction with hidden bids
  if (bidsData.bids_hidden) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Auction Bids
        </h3>
        <div className="text-center py-8">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
            <svg
              className="h-6 w-6 text-yellow-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            Sealed Auction in Progress
          </h4>
          <p className="text-gray-600 mb-4">
            {vendorBidService.getBidVisibilityMessage(
              auction.auction_type,
              auction.status,
              bidsData.total_bids || bidsData.bid_count || 0
            )}
          </p>
          <div className="bg-blue-50 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Total Bids:</strong>{" "}
              {bidsData.total_bids || bidsData.bid_count || 0}
            </p>
            <p className="text-sm text-blue-600 mt-1">
              Bid details will be revealed when the auction ends.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const statistics = vendorBidService.getBidStatistics(bidsData.data);

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Auction Bids
            {auction.auction_type === "sealed" && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Sealed
              </span>
            )}
          </h3>
          <div className="text-sm text-gray-500">
            {vendorBidService.getBidVisibilityMessage(
              auction.auction_type,
              auction.status,
              bidsData.total_bids || 0
            )}
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {bidsData.total_bids || 0}
            </div>
            <div className="text-sm text-gray-600">Total Bids</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {bidsData.unique_bidders || 0}
            </div>
            <div className="text-sm text-gray-600">Unique Bidders</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {vendorBidService.formatBidAmount(bidsData.highest_bid || 0)}
            </div>
            <div className="text-sm text-gray-600">Highest Bid</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {vendorBidService.formatBidAmount(
                bidsData.data.length > 0
                  ? bidsData.data.reduce(
                      (sum, bid) => sum + bid.bid_amount,
                      0
                    ) / bidsData.data.length
                  : 0
              )}
            </div>
            <div className="text-sm text-gray-600">Average Bid</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status || ""}
              onChange={(e) =>
                handleFilterChange({
                  status: (e.target.value as any) || undefined,
                })
              }
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="won">Won</option>
              <option value="lost">Lost</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              value={filters.sort_by || "amount"}
              onChange={(e) =>
                handleFilterChange({
                  sort_by: e.target.value as "amount" | "date",
                })
              }
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="amount">Bid Amount</option>
              <option value="date">Date Placed</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Order
            </label>
            <select
              value={filters.sort_order || "desc"}
              onChange={(e) =>
                handleFilterChange({
                  sort_order: e.target.value as "asc" | "desc",
                })
              }
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="desc">High to Low</option>
              <option value="asc">Low to High</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bids List */}
      <div className="divide-y divide-gray-200">
        {bidsData.data.length === 0 ? (
          <div className="px-6 py-8 text-center">
            <p className="text-gray-500">No bids found for this auction.</p>
          </div>
        ) : (
          bidsData.data.map((bid, index) => (
            <BidRow key={bid.id} bid={bid} rank={index + 1} />
          ))
        )}
      </div>
    </div>
  );
};

interface BidRowProps {
  bid: VendorBidModel;
  rank: number;
}

const BidRow: React.FC<BidRowProps> = ({ bid, rank }) => {
  return (
    <div className="px-6 py-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium text-gray-600">
              #{rank}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">
              {bid.user_name}
            </div>
            <div className="text-sm text-gray-500">{bid.user_email}</div>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-lg font-semibold text-gray-900">
              {vendorBidService.formatBidAmount(bid.bid_amount)}
            </div>
            <div className="text-sm text-gray-500">
              {new Date(bid.created_at).toLocaleDateString()}
            </div>
          </div>
          <div>
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${vendorBidService.getBidStatusColor(
                bid.status,
                bid.is_winning
              )}`}
            >
              {vendorBidService.getBidStatusText(bid.status, bid.is_winning)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VendorBidManagement;
