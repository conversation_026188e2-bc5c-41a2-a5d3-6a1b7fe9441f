"use client";

import { useSession } from "next-auth/react";

const SessionDebug: React.FC = () => {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <div>Loading session...</div>;
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-2">Session Debug</h3>
      <div className="space-y-2">
        <div><strong>Status:</strong> {status}</div>
        <div><strong>Session exists:</strong> {session ? "Yes" : "No"}</div>
        {session && (
          <>
            <div><strong>User ID:</strong> {session.user?.id}</div>
            <div><strong>User Name:</strong> {session.user?.name}</div>
            <div><strong>User Email:</strong> {session.user?.email}</div>
            <div><strong>User Role:</strong> {(session.user as any)?.role || "Not found"}</div>
            <div><strong>Access Token:</strong> {(session as any)?.accessToken ? "Present" : "Missing"}</div>
          </>
        )}
        <details className="mt-4">
          <summary className="cursor-pointer font-medium">Full Session Object</summary>
          <pre className="mt-2 p-2 bg-white rounded text-xs overflow-auto">
            {JSON.stringify(session, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default SessionDebug;
