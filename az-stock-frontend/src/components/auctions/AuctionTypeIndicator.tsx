'use client';

import React from 'react';

interface AuctionTypeIndicatorProps {
  auctionType: 'sealed' | 'online';
  size?: 'sm' | 'md' | 'lg';
  showDescription?: boolean;
  className?: string;
}

export default function AuctionTypeIndicator({ 
  auctionType, 
  size = 'md', 
  showDescription = false,
  className = '' 
}: AuctionTypeIndicatorProps) {
  const isSealed = auctionType === 'sealed';
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const iconSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <div className={className}>
      <div className={`inline-flex items-center space-x-1 font-medium rounded-full ${
        isSealed 
          ? 'bg-purple-100 text-purple-800' 
          : 'bg-blue-100 text-blue-800'
      } ${sizeClasses[size]}`}>
        {/* Icon */}
        <div className={iconSizeClasses[size]}>
          {isSealed ? (
            // Lock icon for sealed auctions
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          ) : (
            // Eye icon for online auctions
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          )}
        </div>
        
        {/* Label */}
        <span>
          {isSealed ? 'Sealed' : 'Online'}
        </span>
      </div>

      {/* Description */}
      {showDescription && (
        <div className="mt-2 text-sm text-gray-600">
          {isSealed ? (
            <div>
              <p className="font-medium text-purple-800 mb-1">Sealed Auction</p>
              <ul className="space-y-1 text-xs">
                <li>• Bids are private and hidden</li>
                <li>• You can update or cancel your bid</li>
                <li>• Highest bid wins when auction ends</li>
              </ul>
            </div>
          ) : (
            <div>
              <p className="font-medium text-blue-800 mb-1">Online Auction</p>
              <ul className="space-y-1 text-xs">
                <li>• All bids are visible to everyone</li>
                <li>• Each bid must be higher than current</li>
                <li>• Real-time bidding competition</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
