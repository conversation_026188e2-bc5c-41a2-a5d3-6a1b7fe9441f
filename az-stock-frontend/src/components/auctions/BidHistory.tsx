"use client";

import React from "react";
import { AuctionModel, BidModel } from "@/types/auction";
import BidService from "@/modules/auctions/services/BidService";
import useGlobalService from "@/core/hook/useGlobalService";
import { formatDistanceToNow } from "date-fns";

interface BidHistoryProps {
  auction: AuctionModel;
  className?: string;
}

export default function BidHistory({
  auction,
  className = "",
}: BidHistoryProps) {
  const bidService = useGlobalService(BidService);
  const shouldShowBids = bidService.shouldShowBids(auction);
  const bids = auction.bids || [];

  // Don't render anything if bids shouldn't be shown
  if (!shouldShowBids) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Bid History
        </h3>
        <div className="text-center py-8">
          <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            Sealed Auction
          </h4>
          <p className="text-gray-600">
            {auction.auction_type === "sealed"
              ? auction.status === "active"
                ? "Bids are hidden until the auction ends to ensure fair bidding."
                : "This auction has ended. Bid history will be revealed shortly."
              : "Bid history is not available for this auction type."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Bid History</h3>
        <div className="flex items-center space-x-2">
          <span
            className={`px-2 py-1 text-xs font-medium rounded-full ${
              auction.auction_type === "sealed"
                ? "bg-purple-100 text-purple-800"
                : "bg-blue-100 text-blue-800"
            }`}
          >
            {auction.auction_type === "sealed" ? "Sealed" : "Online"}
          </span>
          {bids.length > 0 && (
            <span className="text-sm text-gray-500">
              {bids.length} bid{bids.length !== 1 ? "s" : ""}
            </span>
          )}
        </div>
      </div>

      {bids.length === 0 ? (
        <div className="text-center py-8">
          <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            No Bids Yet
          </h4>
          <p className="text-gray-600">
            Be the first to place a bid on this auction!
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {bids.map((bid, index) => (
            <div
              key={bid.id}
              className={`flex items-center justify-between p-3 rounded-lg border ${
                index === 0
                  ? "bg-green-50 border-green-200"
                  : "bg-gray-50 border-gray-200"
              }`}
            >
              <div className="flex items-center space-x-3">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index === 0
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-600"
                  }`}
                >
                  {index + 1}
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">
                      Bidder #{bid.user_id}
                    </span>
                    {index === 0 && (
                      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                        Highest Bid
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatDistanceToNow(new Date(bid.created_at), {
                      addSuffix: true,
                    })}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div
                  className={`text-lg font-bold ${
                    index === 0 ? "text-green-600" : "text-gray-900"
                  }`}
                >
                  ${parseFloat(bid.bid_amount.toString()).toFixed(2)}
                </div>
              </div>
            </div>
          ))}

          {/* Show auction type specific information */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              {auction.auction_type === "sealed" ? (
                <>
                  <strong>Note:</strong> This sealed auction has ended,
                  revealing all bids. The highest bid wins the auction.
                </>
              ) : (
                <>
                  <strong>Live Bidding:</strong> Bids are shown in real-time.
                  Place a higher bid to become the leading bidder.
                </>
              )}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
