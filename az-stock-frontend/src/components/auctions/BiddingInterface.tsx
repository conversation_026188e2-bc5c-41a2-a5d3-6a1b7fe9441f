"use client";

import React, { useState, useEffect } from "react";
import { AuctionModel, BidModel } from "@/types/auction";
import { bidService } from "@/services/bidService";
import { useSession } from "next-auth/react";

interface BiddingInterfaceProps {
  auction: AuctionModel;
  userBid?: BidModel; // User's current bid on this auction
  onBidPlaced?: (bid: BidModel) => void;
  onBidUpdated?: (bid: BidModel) => void;
  onBidCanceled?: () => void;
}

export default function BiddingInterface({
  auction,
  userBid,
  onBidPlaced,
  onBidUpdated,
  onBidCanceled,
}: BiddingInterfaceProps) {
  const { data: session, status } = useSession();
  const user = session?.user;
  const [bidAmount, setBidAmount] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>("");
  const [isEditing, setIsEditing] = useState(false);

  const minBidAmount = bidService.getMinimumBidAmount(auction);
  const canUpdate = userBid ? bidService.canUpdateBid(auction, userBid) : false;
  const canCancel = userBid ? bidService.canCancelBid(auction, userBid) : false;

  // Set initial bid amount when editing
  useEffect(() => {
    if (isEditing && userBid) {
      setBidAmount(userBid.bid_amount.toString());
    } else {
      setBidAmount("");
    }
  }, [isEditing, userBid]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!user) {
      setError("Please log in to place a bid");
      return;
    }

    const amount = parseFloat(bidAmount);
    if (isNaN(amount) || amount < minBidAmount) {
      setError(`Bid amount must be at least $${minBidAmount.toFixed(2)}`);
      return;
    }

    setIsSubmitting(true);

    try {
      if (isEditing && userBid) {
        // Update existing bid
        const response = await bidService.updateBid(userBid.id, {
          bid_amount: amount,
        });
        onBidUpdated?.(response.data);
        setIsEditing(false);
        setBidAmount("");
      } else {
        // Place new bid
        const response = await bidService.placeBid(auction.id, {
          bid_amount: amount,
        });
        onBidPlaced?.(response.data);
        setBidAmount("");
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to place bid");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = async () => {
    if (!userBid) return;

    setIsSubmitting(true);
    setError("");

    try {
      await bidService.cancelBid(userBid.id);
      onBidCanceled?.();
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to cancel bid");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    setError("");
  };

  // Don't show bidding interface if auction is not active
  if (auction.status !== "active" || !auction.is_active) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {auction.auction_type === "sealed" ? "Place Sealed Bid" : "Place Bid"}
        </h3>
        <div className="flex items-center space-x-2">
          <span
            className={`px-2 py-1 text-xs font-medium rounded-full ${
              auction.auction_type === "sealed"
                ? "bg-purple-100 text-purple-800"
                : "bg-blue-100 text-blue-800"
            }`}
          >
            {auction.auction_type === "sealed"
              ? "Sealed Auction"
              : "Online Auction"}
          </span>
        </div>
      </div>

      {/* Auction Type Information */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">
          {auction.auction_type === "sealed" ? (
            <>
              <strong>Sealed Auction:</strong> Your bid is private and hidden
              from other bidders. You can update or cancel your bid until the
              auction ends.
            </>
          ) : (
            <>
              <strong>Online Auction:</strong> All bids are visible to other
              bidders. Each new bid must be higher than the current highest bid.
            </>
          )}
        </p>
      </div>

      {/* Current Bid Display */}
      {userBid && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-900">
                Your Current Bid
              </p>
              <p className="text-lg font-bold text-blue-600">
                ${userBid.bid_amount}
              </p>
            </div>
            {auction.auction_type === "sealed" && (
              <div className="flex space-x-2">
                {canUpdate && (
                  <button
                    type="button"
                    onClick={handleEditToggle}
                    className="text-sm text-blue-600 hover:text-blue-800"
                    disabled={isSubmitting}
                  >
                    {isEditing ? "Cancel Edit" : "Edit Bid"}
                  </button>
                )}
                {canCancel && (
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="text-sm text-red-600 hover:text-red-800"
                    disabled={isSubmitting}
                  >
                    Cancel Bid
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Bidding Form */}
      {(!userBid || isEditing) && (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="bidAmount"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              {isEditing ? "New Bid Amount" : "Bid Amount"}
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                id="bidAmount"
                value={bidAmount}
                onChange={(e) => setBidAmount(e.target.value)}
                min={minBidAmount}
                step="0.01"
                className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder={minBidAmount.toFixed(2)}
                required
                disabled={isSubmitting}
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              {bidService.getBidValidationMessage(auction)}
            </p>
          </div>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}

          <div className="flex space-x-3">
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting
                ? "Processing..."
                : isEditing
                ? "Update Bid"
                : "Place Bid"}
            </button>
            {isEditing && (
              <button
                type="button"
                onClick={handleEditToggle}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                disabled={isSubmitting}
              >
                Cancel
              </button>
            )}
          </div>
        </form>
      )}

      {/* No bidding allowed for online auctions if user already has highest bid */}
      {auction.auction_type === "online" && userBid && !isEditing && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-600">
            You have the current highest bid. Wait for other bidders or the
            auction to end.
          </p>
        </div>
      )}
    </div>
  );
}
