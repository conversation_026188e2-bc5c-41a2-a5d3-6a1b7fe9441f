import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";

// Types for vendor bid visibility
export interface VendorBidModel {
  id: number;
  user_id: number;
  user_name: string;
  user_email: string;
  bid_amount: number;
  status: "active" | "won" | "lost" | "cancelled";
  created_at: string;
  updated_at: string;
  is_winning: boolean;
  wallet_hold_status: string | null;
}

export interface VendorBidsResponse {
  message: string;
  auction_type: "sealed" | "online";
  auction_status: string;
  bids_hidden: boolean;
  total_bids?: number; // For visible bids
  bid_count?: number; // For hidden bids
  unique_bidders?: number;
  highest_bid?: number;
  data: VendorBidModel[];
}

export interface VendorBidsFilters {
  status?: "active" | "won" | "lost" | "cancelled";
  sort_by?: "amount" | "date";
  sort_order?: "asc" | "desc";
}

class VendorBidService   {
  private baseUrl = "/api/vendor";

  /**
   * Get bids for a specific auction (vendor only)
   * Respects sealed auction visibility rules
   */
  async getAuctionBids(
    auctionId: number,
    filters?: VendorBidsFilters
  ): Promise<VendorBidsResponse> {
    const params = new URLSearchParams();
    
    if (filters?.status) {
      params.append("status", filters.status);
    }
    if (filters?.sort_by) {
      params.append("sort_by", filters.sort_by);
    }
    if (filters?.sort_order) {
      params.append("sort_order", filters.sort_order);
    }

    const url = `/vendor/auctions/${auctionId}/bids${
      params.toString() ? `?${params.toString()}` : ""
    }`;
        const headers = new Headers();
            const { getSession } = await import("next-auth/react");
            const session = await getSession();
    
            const token = session?.accessToken;
            headers?.set("Content-Type", "application/json");
            headers?.set("Authorization", `Bearer ${token}`);
            const response = await GlobalFetchJson<VendorBidsResponse>(
              url,
              {
                method: "GET",
                headers: headers,
                cache: "no-store",
              }
            );

   return response;

    // return await GlobalFetchJson<VendorBidsResponse>(url);
  }

  /**
   * Check if bids are visible for a sealed auction
   */
  canViewBids(auctionType: "sealed" | "online", auctionStatus: string): boolean {
    if (auctionType === "online") {
      return true; // Online auction bids are always visible
    }
    
    // For sealed auctions, bids are only visible after auction ends
    return auctionStatus === "ended" || auctionStatus === "completed";
  }

  /**
   * Get bid visibility message for sealed auctions
   */
  getBidVisibilityMessage(
    auctionType: "sealed" | "online",
    auctionStatus: string,
    bidCount: number
  ): string {
    if (auctionType === "online") {
      return `${bidCount} bid${bidCount !== 1 ? "s" : ""} placed`;
    }

    if (auctionStatus === "active") {
      return `${bidCount} sealed bid${bidCount !== 1 ? "s" : ""} placed (hidden until auction ends)`;
    }

    return `${bidCount} bid${bidCount !== 1 ? "s" : ""} revealed`;
  }

  /**
   * Format bid amount for display
   */
  formatBidAmount(amount: number): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  }

  /**
   * Get bid status badge color
   */
  getBidStatusColor(status: string, isWinning: boolean): string {
    if (isWinning) return "bg-green-100 text-green-800";
    
    switch (status) {
      case "active":
        return "bg-blue-100 text-blue-800";
      case "won":
        return "bg-green-100 text-green-800";
      case "lost":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  /**
   * Get bid status display text
   */
  getBidStatusText(status: string, isWinning: boolean): string {
    if (isWinning) return "Winner";
    
    switch (status) {
      case "active":
        return "Active";
      case "won":
        return "Won";
      case "lost":
        return "Lost";
      case "cancelled":
        return "Cancelled";
      default:
        return status;
    }
  }

  /**
   * Sort bids by different criteria
   */
  sortBids(
    bids: VendorBidModel[],
    sortBy: "amount" | "date" = "amount",
    sortOrder: "asc" | "desc" = "desc"
  ): VendorBidModel[] {
    return [...bids].sort((a, b) => {
      let comparison = 0;
      
      if (sortBy === "amount") {
        comparison = a.bid_amount - b.bid_amount;
      } else if (sortBy === "date") {
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      }
      
      return sortOrder === "desc" ? -comparison : comparison;
    });
  }

  /**
   * Filter bids by status
   */
  filterBids(
    bids: VendorBidModel[],
    status?: "active" | "won" | "lost" | "cancelled"
  ): VendorBidModel[] {
    if (!status) return bids;
    return bids.filter(bid => bid.status === status);
  }

  /**
   * Get bid statistics
   */
  getBidStatistics(bids: VendorBidModel[]) {
    const activeBids = bids.filter(bid => bid.status === "active");
    const winningBid = bids.find(bid => bid.is_winning);
    const totalAmount = bids.reduce((sum, bid) => sum + bid.bid_amount, 0);
    const averageAmount = bids.length > 0 ? totalAmount / bids.length : 0;
    const uniqueBidders = new Set(bids.map(bid => bid.user_id)).size;

    return {
      totalBids: bids.length,
      activeBids: activeBids.length,
      uniqueBidders,
      highestBid: Math.max(...bids.map(bid => bid.bid_amount), 0),
      lowestBid: Math.min(...bids.map(bid => bid.bid_amount), 0),
      averageBid: averageAmount,
      winningBid: winningBid || null,
      totalValue: totalAmount,
    };
  }
}

export const vendorBidService = new VendorBidService();
