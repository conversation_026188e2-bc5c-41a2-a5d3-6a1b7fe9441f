import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import GlobalHorizontalSlider from "@/modules/common/components/GlobalSlider/GlobalSlider";

const Slider = () => {
  return (
    <GlobalHorizontalSlider>
      {" "}
      <div className=" !w-[100vw]  text-white relative flex flex-col justify-center">
        <GlobalImage
          alt=""
          width={400}
          height={400}
          src={"/store.png"}
          className="absolute -z-10 w-full"
        />
        <div className="w-screen p-16">
          <h2 className="font-bold text-xl">
            Welcome to AZ-Stock: The largest B2B marketplace for returned and
            overstock inventory
          </h2>
          <p>
            Direct access to merchandise from top retailers and brands across
            all categories, conditions, and lot sizes!
          </p>
        </div>
      </div>
      <div className=" !w-[100vw]  text-white relative flex flex-col justify-center">
        <GlobalImage
          alt=""
          width={400}
          height={400}
          src={"/store.png"}
          className="absolute -z-10 w-full"
        />
        <div className="w-screen p-16">
          <h2 className="font-bold text-xl">
            Contract Agreements and Direct Sales Now Available
          </h2>
          <p>
            Looking for a long-term sourcing solution? Lock in inventory at
            consistent and high volumes from top sellers on B-Stock Direct!
          </p>
        </div>
      </div>
      <div className=" !w-[100vw]  text-white relative flex flex-col justify-center">
        <GlobalImage
          alt=""
          src={"/store.png"}
          width={400}
          height={400}
          className="absolute -z-10 w-full"
        />
        <div className="w-screen p-16">
          <h2 className="font-bold text-xl">
            Shop Electronics, Wearables, and Appliances at Best Buy
          </h2>
          <p>
            The Best Buy storefront offers inventory from some of the most
            trusted consumer brands and manufacturers. Check out new product
            categories available now!
          </p>
        </div>
      </div>
    </GlobalHorizontalSlider>
  );
};
export default Slider;
