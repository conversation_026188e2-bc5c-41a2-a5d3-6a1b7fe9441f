"use client";
import GlobalSelect from "@/modules/common/components/GlobalSelect";
import { useRouter } from "next/navigation";

const CategoryFilter = ({ categories }: { categories: string[] }) => {
  const router = useRouter();
  const data = [{ label: "All", value: "" }];
  categories?.map((d) => {
    data.push({ label: d, value: d });
  });
  return (
    <GlobalSelect
      name="category"
      data={data ?? []}
      onchange={(e) => {
        router.push(`?category=${e?.value}`, { scroll: false });
      }}
      placeholder="الكل"
    />
  );
};

export default CategoryFilter;
