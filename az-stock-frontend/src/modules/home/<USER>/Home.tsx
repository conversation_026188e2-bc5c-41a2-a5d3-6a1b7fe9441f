"use client";
import { AuctionModel } from "@/modules/auctions/models/AuctionModel";
import AuctionList from "@/modules/auctions/components/AuctionList";
import Container from "@/modules/common/components/Container";
import { useState } from "react";
import Link from "next/link";
import Slider from "./Slider";

interface HomeProps {
  auctions?: AuctionModel[];
  categories?: Array<{ id: number; name: string }>;
}

const Home: React.FC<HomeProps> = ({ auctions = [], categories = [] }) => {
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);

  // Make sure auctions is an array before filtering
  const auctionsArray = Array.isArray(auctions) ? auctions : [];

  const filteredAuctions = selectedCategory
    ? auctionsArray.filter(
        (auction) =>
          // This is a placeholder. In a real app, you would have a category_id in the auction model
          // or you would filter by product category
          auction.id % (selectedCategory + 1) === 0
      )
    : auctionsArray;

  return (
    <>
      {" "}
      <Slider />
      <Container className="py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Live Auctions</h1>
            <p className="text-gray-600">
              Discover unique items and place your bids
            </p>
          </div>

          <div className="mt-4 md:mt-0">
            <Link
              href="/auctions"
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              View All Auctions
            </Link>
          </div>
        </div>

        {Array.isArray(categories) && categories.length > 0 && (
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory(null)}
                className={`px-4 py-2 rounded-full text-sm font-medium ${
                  selectedCategory === null
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                }`}
              >
                All Categories
              </button>

              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium ${
                    selectedCategory === category.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        )}

        {filteredAuctions.length > 0 ? (
          <AuctionList auctions={filteredAuctions} />
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">
              No auctions found in this category
            </h3>
            <p className="mt-2 text-gray-500">
              Try selecting a different category or check back later
            </p>
          </div>
        )}
      </Container>
    </>
  );
};

export default Home;
