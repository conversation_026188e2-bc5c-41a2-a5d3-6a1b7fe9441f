"use client";
import Container from "@/modules/common/components/Container";
import GlobalInput from "@/modules/common/components/GlobaInput";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import GlobalHorizontalSlider from "@/modules/common/components/GlobalSlider/GlobalSlider";
import ProductCard from "@/modules/common/components/ProductCard";
import ProductModel from "@/modules/products/models/ProductModel";
import React, { Suspense } from "react";
import CategoryFilter from "../CategoryFilter";
import { AuctionModel } from "@/modules/auctions/models/AuctionModel";

const Home = ({
  auctions,
  categories,
}: {
  auctions: AuctionModel[];
  categories: {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    products_count: number;
  }[];
}) => {
  return (
    <Container className="bg-white rounded-lg !p-4 shadow-sm mb-10">
      <GlobalHorizontalSlider>
        <div style={{ flex: "0 0 100%" }}>
          <GlobalImage
            src="/01.png"
            alt="image"
            width={400}
            height={400}
            className="w-full max-h-screen object-cover "
          />
        </div>
        <div style={{ flex: "0 0 100%" }}>
          {" "}
          <GlobalImage
            src="/02.png"
            alt="image"
            width={400}
            height={400}
            className="w-full max-h-screen object-cover"
          />
        </div>
        <div style={{ flex: "0 0 100%" }}>
          {" "}
          <GlobalImage
            src="/03.png"
            alt="image"
            width={400}
            height={400}
            className="w-full max-h-screen object-cover"
          />
        </div>
      </GlobalHorizontalSlider>

      <div className="flex justify-between gap-4 w-full py-5">
        <GlobalInput
          name="search"
          placeholder="ادخل اسم المنتح.."
          className="!w-full flex-auto"
          containerProps={{ className: "w-full" }}
        />

        {/* <CategoryFilter categories={categories} /> */}
      </div>
      <div
        className="grid md:grid-cols-3 lg:grid-cols-4 xs:grid-cols-2 gap-5"
        data-aos="fade-in"
      >
        <Suspense fallback={<span>Loading..</span>}>
          {auctions?.map((d) => (
            <ProductCard key={d.id} data={d} />
          ))}
        </Suspense>
      </div>
    </Container>
  );
};

export default Home;
