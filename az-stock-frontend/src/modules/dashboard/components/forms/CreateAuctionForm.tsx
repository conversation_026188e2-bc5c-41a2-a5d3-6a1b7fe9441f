"use client";

import React, { useState, useEffect } from "react";
import {
  CreateAuctionForm,
  CreateAuctionItemForm,
  CategoryModel,
} from "../../models/DashboardModels";
import ImageUpload from "../shared/ImageUpload";
import { vercelBlobService } from "@/services/VercelBlobService";

interface CreateAuctionFormProps {
  categories: CategoryModel[];
  onSubmit: (auctionData: CreateAuctionForm) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const CreateAuctionFormComponent: React.FC<CreateAuctionFormProps> = ({
  categories,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [formData, setFormData] = useState<CreateAuctionForm>({
    title: "",
    description: "",
    category_id: 0,
    auction_type: "online",
    start_time: "",
    end_time: "",
    starting_price: 0,
    reserve_price: 0,
    items: [],
  });

  const [featuredImage, setFeaturedImage] = useState<File | null>(null);
  const [featuredImagePreview, setFeaturedImagePreview] = useState<
    string | null
  >(null);

  const [auctionItems, setAuctionItems] = useState<CreateAuctionItemForm[]>([
    {
      item_name: "",
      item_description: "",
      images: [],
    },
  ]);

  const [uploading, setUploading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Set default start time to now and end time to 7 days from now
  useEffect(() => {
    const now = new Date();
    const endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    setFormData((prev) => ({
      ...prev,
      start_time: now.toISOString().slice(0, 16), // Format for datetime-local input
      end_time: endTime.toISOString().slice(0, 16),
    }));
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Auction title is required";
    }

    if (!formData.description?.trim()) {
      newErrors.description = "Auction description is required";
    }

    if (formData.category_id === 0) {
      newErrors.category_id = "Please select a category";
    }

    if (!formData.start_time) {
      newErrors.start_time = "Start time is required";
    }

    if (!formData.end_time) {
      newErrors.end_time = "End time is required";
    }

    if (formData.starting_price <= 0) {
      newErrors.starting_price = "Starting price must be greater than 0";
    }

    if (
      formData.reserve_price &&
      formData.reserve_price < formData.starting_price
    ) {
      newErrors.reserve_price =
        "Reserve price must be greater than or equal to starting price";
    }

    // Validate auction items
    auctionItems.forEach((item, index) => {
      if (!item.item_name.trim()) {
        newErrors[`item_${index}_name`] = `Item ${index + 1} name is required`;
      }
    });

    if (auctionItems.length === 0) {
      newErrors.items = "At least one auction item is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFeaturedImageChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      setFeaturedImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setFeaturedImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeFeaturedImage = () => {
    setFeaturedImage(null);
    setFeaturedImagePreview(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setUploading(true);

      // Upload featured image if provided
      let featuredImageUrl: string | undefined;
      if (featuredImage) {
        try {
          const uploadResult = await vercelBlobService.uploadFiles(
            [featuredImage],
            `auctions/featured/${Date.now()}`
          );
          featuredImageUrl = uploadResult.successful[0]?.url;
        } catch (error) {
          console.error("Error uploading featured image:", error);
        }
      }

      // Upload images for each auction item and convert to URLs
      const itemsWithImageUrls = await Promise.all(
        auctionItems.map(async (item) => {
          let imageUrls: string[] = [];

          if (item.images && item.images.length > 0) {
            try {
              const uploadResult = await vercelBlobService.uploadFiles(
                item.images,
                `auctions/items/${Date.now()}`
              );
              imageUrls = uploadResult.successful.map((result) => result.url);
            } catch (error) {
              console.error(
                "Error uploading images for item:",
                item.item_name,
                error
              );
            }
          }

          return {
            item_name: item.item_name,
            item_description: item.item_description || undefined,
            images: imageUrls,
          };
        })
      );

      const auctionData: CreateAuctionForm = {
        ...formData,
        featured_image: featuredImageUrl,
        items: itemsWithImageUrls,
      };

      await onSubmit(auctionData);
    } catch (error) {
      console.error("Error creating auction:", error);
    } finally {
      setUploading(false);
    }
  };

  const addAuctionItem = () => {
    setAuctionItems([
      ...auctionItems,
      {
        item_name: "",
        item_description: "",
        images: [],
      },
    ]);
  };

  const removeAuctionItem = (index: number) => {
    if (auctionItems.length > 1) {
      setAuctionItems(auctionItems.filter((_, i) => i !== index));
    }
  };

  const updateAuctionItem = (
    index: number,
    field: keyof CreateAuctionItemForm,
    value: any
  ) => {
    const updatedItems = [...auctionItems];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };
    setAuctionItems(updatedItems);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Auction Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Auction Details</h3>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Auction Title *
          </label>
          <input
            type="text"
            required
            value={formData.title}
            onChange={(e) =>
              setFormData({ ...formData, title: e.target.value })
            }
            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
              errors.title ? "border-red-300" : ""
            }`}
            placeholder="Enter auction title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Description *
          </label>
          <textarea
            rows={3}
            required
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
              errors.description ? "border-red-300" : ""
            }`}
            placeholder="Describe your auction and what's included"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description}</p>
          )}
        </div>

        {/* Featured Image */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Featured Image
          </label>
          <div className="flex items-center space-x-4">
            {/* Preview */}
            <div className="flex-shrink-0">
              {featuredImagePreview ? (
                <div className="relative">
                  <img
                    src={featuredImagePreview}
                    alt="Featured image preview"
                    className="h-20 w-20 rounded-lg object-cover border-2 border-gray-300"
                  />
                  <button
                    type="button"
                    onClick={removeFeaturedImage}
                    className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1"
                  >
                    <svg
                      className="h-3 w-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              ) : (
                <div className="h-20 w-20 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <svg
                    className="h-8 w-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Upload Button */}
            <div className="flex-1">
              <input
                type="file"
                accept="image/*"
                onChange={handleFeaturedImageChange}
                className="hidden"
                id="featured-image-upload"
              />
              <label
                htmlFor="featured-image-upload"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
              >
                <svg
                  className="h-4 w-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                {featuredImage
                  ? "Change Featured Image"
                  : "Upload Featured Image"}
              </label>
              <p className="text-xs text-gray-500 mt-1">
                Optional: This image will be prominently displayed in auction
                listings. JPG, PNG, or GIF. Max 5MB.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Category *
            </label>
            <select
              required
              value={formData.category_id}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  category_id: parseInt(e.target.value),
                })
              }
              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                errors.category_id ? "border-red-300" : ""
              }`}
            >
              <option value={0}>Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            {errors.category_id && (
              <p className="mt-1 text-sm text-red-600">{errors.category_id}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Auction Type *
            </label>
            <select
              required
              value={formData.auction_type}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  auction_type: e.target.value as "sealed" | "online",
                })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="online">Online Auction</option>
              <option value="sealed">Sealed Bid Auction</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Start Time *
            </label>
            <input
              type="datetime-local"
              required
              value={formData.start_time}
              onChange={(e) =>
                setFormData({ ...formData, start_time: e.target.value })
              }
              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                errors.start_time ? "border-red-300" : ""
              }`}
            />
            {errors.start_time && (
              <p className="mt-1 text-sm text-red-600">{errors.start_time}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              End Time *
            </label>
            <input
              type="datetime-local"
              required
              value={formData.end_time}
              onChange={(e) =>
                setFormData({ ...formData, end_time: e.target.value })
              }
              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                errors.end_time ? "border-red-300" : ""
              }`}
            />
            {errors.end_time && (
              <p className="mt-1 text-sm text-red-600">{errors.end_time}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Starting Price ($) *
            </label>
            <input
              type="number"
              step="0.01"
              min="0.01"
              required
              value={formData.starting_price}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  starting_price: parseFloat(e.target.value) || 0,
                })
              }
              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                errors.starting_price ? "border-red-300" : ""
              }`}
            />
            {errors.starting_price && (
              <p className="mt-1 text-sm text-red-600">
                {errors.starting_price}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Reserve Price ($)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.reserve_price || ""}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  reserve_price: parseFloat(e.target.value) || undefined,
                })
              }
              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                errors.reserve_price ? "border-red-300" : ""
              }`}
              placeholder="Optional minimum selling price"
            />
            {errors.reserve_price && (
              <p className="mt-1 text-sm text-red-600">
                {errors.reserve_price}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Auction Items Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Auction Items</h3>
          <button
            type="button"
            onClick={addAuctionItem}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Add Item
          </button>
        </div>

        {errors.items && <p className="text-sm text-red-600">{errors.items}</p>}

        {auctionItems.map((item, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-lg p-4 space-y-4"
          >
            <div className="flex justify-between items-center">
              <h4 className="text-md font-medium text-gray-800">
                Item {index + 1}
              </h4>
              {auctionItems.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeAuctionItem(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Item Name *
              </label>
              <input
                type="text"
                required
                value={item.item_name}
                onChange={(e) =>
                  updateAuctionItem(index, "item_name", e.target.value)
                }
                className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                  errors[`item_${index}_name`] ? "border-red-300" : ""
                }`}
                placeholder="Enter item name"
              />
              {errors[`item_${index}_name`] && (
                <p className="mt-1 text-sm text-red-600">
                  {errors[`item_${index}_name`]}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Item Description
              </label>
              <textarea
                rows={2}
                value={item.item_description}
                onChange={(e) =>
                  updateAuctionItem(index, "item_description", e.target.value)
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Describe this item (optional)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Item Images
              </label>
              <ImageUpload
                images={item.images || []}
                onImagesChange={(images) =>
                  updateAuctionItem(index, "images", images)
                }
                maxImages={5}
                maxSizePerImage={5}
                showPreview={true}
              />
              <p className="text-xs text-gray-500 mt-1">
                Upload up to 5 images per item (max 5MB each)
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          disabled={loading || uploading}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading || uploading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {loading || uploading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {uploading ? "Uploading..." : "Creating..."}
            </div>
          ) : (
            "Create Auction"
          )}
        </button>
      </div>
    </form>
  );
};

export default CreateAuctionFormComponent;
