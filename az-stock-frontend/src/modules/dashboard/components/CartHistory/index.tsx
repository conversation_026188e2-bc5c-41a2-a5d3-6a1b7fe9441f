import useGlobalService from "@/core/hook/useGlobalService";
import CartModel from "@/modules/cart/models/CartModel";
import Container from "@/modules/common/components/Container";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import ProductModel from "@/modules/products/models/ProductModel";
import ProductService from "@/modules/products/services/ProductService";
import Link from "next/link";

const CartHistory = ({ data }: { data: CartModel[] }) => {
  return (
    <Container className="flex flex-col gap-4 !px-0" data-aos="fade-in">
      {data.map((d) => (
        <div key={d.id} className="shadow rounded-lg bg-white p-4">
          <h3> السلة: {d.id} </h3>

          <div>
            {d.products.map((product) => (
              <ProductItem quantity={product.quantity} id={product.productId} />
            ))}
          </div>
        </div>
      ))}
    </Container>
  );
};

export default CartHistory;

const ProductItem = async ({
  id,
  quantity,
}: {
  id: number;
  quantity: number;
}) => {
  const productService = useGlobalService(ProductService);
  const d = await productService.methods.getProduct({ id: id });
  return (
    <Link
      href={`/products/${d.id}`}
      key={d.id}
      className="flex justify-between p-3 hover:bg-gray-50 rounded"
    >
      <div className="flex gap-4">
        <GlobalImage
          src={d.image}
          alt={d.title}
          className="object-cover w-10 h-10 rounded"
          width={50}
          height={50}
        />
        <div>
          <h5>{d.title}</h5>
          <span className="flex gap-2">
            <span>x{quantity}</span>
            <span>SAR {d.price}</span>
          </span>
        </div>
      </div>
    </Link>
  );
};
