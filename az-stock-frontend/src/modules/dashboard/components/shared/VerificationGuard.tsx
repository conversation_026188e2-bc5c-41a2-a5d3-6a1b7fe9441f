"use client";

import React from "react";
import { useSession } from "next-auth/react";

interface VerificationGuardProps {
  requiredRole: "buyer" | "vendor" | "admin";
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showMessage?: boolean;
}

const VerificationGuard: React.FC<VerificationGuardProps> = ({
  requiredRole,
  children,
  fallback,
  showMessage = true,
}) => {
  const { data: session } = useSession();

  // Check if user has the required role
  const hasRole = session?.user?.roles?.includes(requiredRole) || 
                  session?.user?.role === requiredRole;

  // Check if user is verified for the required role
  const isVerified = session?.user?.verification_status?.[requiredRole];

  // If user doesn't have the role, don't show anything
  if (!hasRole) {
    return null;
  }

  // If user has the role but is not verified, show verification message
  if (!isVerified) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showMessage) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Verification Required
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Your account needs to be verified for the <strong>{requiredRole}</strong> role 
                  before you can perform this action. Please contact an administrator for verification.
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return null;
  }

  // User is verified, show the children
  return <>{children}</>;
};

export default VerificationGuard;
