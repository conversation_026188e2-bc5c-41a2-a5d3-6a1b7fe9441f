"use client";

import React, { useState } from "react";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import { ProductImageModel } from "@/modules/dashboard/models/DashboardModels";

interface ProductImageGalleryProps {
  images: ProductImageModel[];
  onDeleteImage: (imageId: number) => Promise<void>;
  onUploadImages?: (images: File[], replaceAll?: boolean) => Promise<void>;
  showUpload?: boolean;
  className?: string;
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  images,
  onDeleteImage,
  onUploadImages,
  showUpload = false,
  className = "",
}) => {
  const [deletingImageId, setDeletingImageId] = useState<number | null>(null);
  const [uploadingImages, setUploadingImages] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [replaceMode, setReplaceMode] = useState(false);

  const handleDeleteImage = async (imageId: number) => {
    if (!confirm("Are you sure you want to delete this image?")) return;

    try {
      setDeletingImageId(imageId);
      await onDeleteImage(imageId);
    } catch (error) {
      console.error("Error deleting image:", error);
      alert("Failed to delete image. Please try again.");
    } finally {
      setDeletingImageId(null);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0 || !onUploadImages) return;

    try {
      setIsUploading(true);
      setUploadingImages(files);
      await onUploadImages(files, replaceMode);
      setUploadingImages([]);
      setReplaceMode(false); // Reset replace mode after upload
      // Reset the input
      e.target.value = "";
    } catch (error) {
      console.error("Error uploading images:", error);
      alert("Failed to upload images. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Section */}
      {showUpload && onUploadImages && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
          <div className="text-center">
            <svg
              className="mx-auto h-8 w-8 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="mt-2">
              <label htmlFor="image-upload" className="cursor-pointer">
                <span className="text-sm font-medium text-indigo-600 hover:text-indigo-500">
                  {replaceMode
                    ? "Replace all images"
                    : "Upload additional images"}
                </span>
                <input
                  id="image-upload"
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  disabled={isUploading}
                />
              </label>
            </div>
            <div className="mt-2">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={replaceMode}
                  onChange={(e) => setReplaceMode(e.target.checked)}
                  className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-xs text-gray-600">
                  Replace all existing images
                </span>
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              PNG, JPG, WEBP up to 5MB each
            </p>
          </div>
        </div>
      )}

      {/* Uploading Preview */}
      {isUploading && uploadingImages.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-blue-800">
              Uploading {uploadingImages.length} image(s)...
            </span>
          </div>
        </div>
      )}

      {/* Images Grid */}
      {images?.length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border">
                <GlobalImage
                  src={image.url}
                  alt={image.original_name}
                  fill
                  className="object-cover"
                />
              </div>

              {/* Image Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                <button
                  type="button"
                  onClick={() => handleDeleteImage(image.id)}
                  disabled={deletingImageId === image.id}
                  className="opacity-0 group-hover:opacity-100 bg-red-500 hover:bg-red-600 text-white rounded-full p-2 transition-all duration-200 disabled:opacity-50"
                >
                  {deletingImageId === image.id ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  )}
                </button>
              </div>

              {/* Image Info */}
              <div className="mt-2 space-y-1">
                <div
                  className="text-xs text-gray-600 truncate"
                  title={image.original_name}
                >
                  {image.original_name}
                </div>
                <div className="text-xs text-gray-500">
                  {formatFileSize(image.file_size || 0)}
                </div>
                <div className="text-xs text-gray-400">
                  {new Date(image.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <p className="mt-2 text-sm">No images uploaded yet</p>
          {showUpload && (
            <p className="text-xs text-gray-400">
              Use the upload area above to add images
            </p>
          )}
        </div>
      )}

      {/* Summary */}
      {images?.length > 0 && (
        <div className="text-sm text-gray-500 text-center">
          {images.length} image(s) • Total size:{" "}
          {formatFileSize(
            images.reduce((sum, img) => sum + (img.file_size || 0), 0)
          )}
        </div>
      )}
    </div>
  );
};

export default ProductImageGallery;
