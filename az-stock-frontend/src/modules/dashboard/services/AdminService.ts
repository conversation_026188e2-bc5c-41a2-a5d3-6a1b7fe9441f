import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import {
  UserModel,
  PaginatedResponse,
  SingleResponse,
  CreateUserForm,
  UpdateUserForm,
  UserFilters,
  ActivityLogModel,
  ActivityLogFilters,
  ActivityLogStats,
  DashboardStats,
  UserVerificationStatus,
  VerifyUserForm,
} from "../models/DashboardModels";

export class AdminService {
  private baseUrl = "/api/proxy";

  // User Management
  async getUsers(filters?: UserFilters): Promise<PaginatedResponse<UserModel>> {
    const params = new URLSearchParams();

    // Apply filters only if they are provided
    if (filters?.role) params.append("role", filters.role);
    if (filters?.is_active !== undefined)
      params.append("is_active", filters.is_active.toString());
    if (filters?.search) params.append("search", filters.search);
    if (filters?.created_after)
      params.append("created_after", filters.created_after);
    if (filters?.created_before)
      params.append("created_before", filters.created_before);
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/admin/users${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson<PaginatedResponse<UserModel>>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  async getUserById(userId: number): Promise<SingleResponse<UserModel>> {
    return await GlobalFetchJson<SingleResponse<UserModel>>(
      `/admin/users/${userId}`,
      { method: "GET" },
      this.baseUrl
    );
  }

  async createUser(
    userData: CreateUserForm
  ): Promise<SingleResponse<UserModel>> {
    return await GlobalFetchJson<SingleResponse<UserModel>>(
      `/admin/users`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData),
      },
      this.baseUrl
    );
  }

  async updateUser(
    userId: number,
    userData: UpdateUserForm
  ): Promise<SingleResponse<UserModel>> {
    return await GlobalFetchJson<SingleResponse<UserModel>>(
      `/admin/users/${userId}`,
      {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData),
      },
      this.baseUrl
    );
  }

  async deleteUser(userId: number): Promise<{ message: string }> {
    return await GlobalFetchJson<{ message: string }>(
      `/admin/users/${userId}`,
      { method: "DELETE" },
      this.baseUrl
    );
  }

  // User Verification Management
  async getUserVerificationStatus(
    userId: number
  ): Promise<UserVerificationStatus> {
    return await GlobalFetchJson<UserVerificationStatus>(
      `/admin/users/${userId}/verification-status`,
      { method: "GET" },
      this.baseUrl
    );
  }

  async verifyUser(
    userId: number,
    verificationData: VerifyUserForm
  ): Promise<UserModel> {
    return await GlobalFetchJson<UserModel>(
      `/admin/users/${userId}/verify`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(verificationData),
      },
      this.baseUrl
    );
  }

  // Activity Logs
  async getActivityLogs(
    filters?: ActivityLogFilters
  ): Promise<PaginatedResponse<ActivityLogModel>> {
    const params = new URLSearchParams();
    if (filters?.user_id) params.append("user_id", filters.user_id.toString());
    if (filters?.action_type) params.append("action_type", filters.action_type);
    if (filters?.entity_type) params.append("entity_type", filters.entity_type);
    if (filters?.entity_id)
      params.append("entity_id", filters.entity_id.toString());
    if (filters?.start_date) params.append("start_date", filters.start_date);
    if (filters?.end_date) params.append("end_date", filters.end_date);
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/admin/activity-logs${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson<PaginatedResponse<ActivityLogModel>>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  async getActivityLogStats(
    startDate?: string,
    endDate?: string
  ): Promise<ActivityLogStats> {
    const params = new URLSearchParams();
    if (startDate) params.append("start_date", startDate);
    if (endDate) params.append("end_date", endDate);

    const queryString = params.toString();
    const url = `/admin/activity-logs/statistics${
      queryString ? `?${queryString}` : ""
    }`;

    return await GlobalFetchJson<ActivityLogStats>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  // Dashboard Statistics (custom endpoint - may need to be implemented)
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      return await GlobalFetchJson<DashboardStats>(
        `/admin/dashboard/stats`,
        { method: "GET" },
        this.baseUrl
      );
    } catch (error) {
      // Fallback: calculate stats from individual endpoints
      console.warn(
        "Dashboard stats endpoint not available, calculating manually"
      );
      return this.calculateDashboardStats();
    }
  }

  private async calculateDashboardStats(): Promise<DashboardStats> {
    try {
      // Get basic counts from existing endpoints
      const [usersResponse, auctionsResponse] = await Promise.all([
        this.getUsers({ per_page: 1 }),
        GlobalFetchJson<PaginatedResponse<any>>(
          "/auctions",
          { method: "GET" },
          this.baseUrl
        ),
      ]);

      return {
        totalUsers: usersResponse.meta?.total || 0,
        totalAuctions: auctionsResponse.meta?.total || 0,
        // Add more calculations as needed
      };
    } catch (error) {
      console.error("Error calculating dashboard stats:", error);
      return {};
    }
  }

  // Auction Management (Admin view of all auctions)
  async getAllAuctions(filters?: {
    status?: string;
    per_page?: number;
    page?: number;
  }) {
    const params = new URLSearchParams();
    if (filters?.status) params.append("status", filters.status);
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/auctions${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson(url, { method: "GET" }, this.baseUrl);
  }

  async endAuction(auctionId: number) {
    return await GlobalFetchJson(
      `/auctions/${auctionId}/end`,
      { method: "POST" },
      this.baseUrl
    );
  }

  async processRefunds(auctionId: number) {
    return await GlobalFetchJson(
      `/auctions/${auctionId}/refunds`,
      { method: "POST" },
      this.baseUrl
    );
  }

  // Category Management (Admin can manage all categories)
  async getAllCategories(filters?: { per_page?: number; page?: number }) {
    const params = new URLSearchParams();
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/categories${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson(url, { method: "GET" }, this.baseUrl);
  }

  async getCategories(): Promise<PaginatedResponse<any>> {
    return await GlobalFetchJson<PaginatedResponse<any>>(
      `/categories`,
      { method: "GET" },
      this.baseUrl
    );
  }

  async createCategory(name: string) {
    return await GlobalFetchJson(
      `/categories`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name }),
      },
      this.baseUrl
    );
  }

  async updateCategory(categoryId: number, name: string) {
    return await GlobalFetchJson(
      `/categories/${categoryId}`,
      {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name }),
      },
      this.baseUrl
    );
  }

  async deleteCategory(categoryId: number) {
    return await GlobalFetchJson(
      `/categories/${categoryId}`,
      { method: "DELETE" },
      this.baseUrl
    );
  }

  // Product Management (Admin view of all products)
  async getAllProducts(filters?: {
    category_id?: number;
    vendor_id?: number;
    per_page?: number;
    page?: number;
  }) {
    const params = new URLSearchParams();
    if (filters?.category_id)
      params.append("category_id", filters.category_id.toString());
    if (filters?.vendor_id)
      params.append("vendor_id", filters.vendor_id.toString());
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/products${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson(url, { method: "GET" }, this.baseUrl);
  }
}

export const adminService = new AdminService();
