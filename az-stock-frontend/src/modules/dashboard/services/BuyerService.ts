import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import {
  BidModel,
  AuctionModel,
  WalletModel,
  TransactionModel,
  WalletHoldModel,
  PaginatedResponse,
  SingleResponse,
  BidFilters,
  TransactionFilters,
  WalletDepositForm,
  WalletWithdrawForm,
} from "../models/DashboardModels";

export class BuyerService {
  private baseUrl = "/api/proxy";

  // Bid Management
  async getMyBids(filters?: BidFilters): Promise<PaginatedResponse<BidModel>> {
    const params = new URLSearchParams();
    if (filters?.status) params.append("status", filters.status);
    if (filters?.auction_status)
      params.append("auction_status", filters.auction_status);
    if (filters?.category_id)
      params.append("category_id", filters.category_id.toString());
    if (filters?.search) params.append("search", filters.search);
    if (filters?.min_amount)
      params.append("min_amount", filters.min_amount.toString());
    if (filters?.max_amount)
      params.append("max_amount", filters.max_amount.toString());
    if (filters?.date_from) params.append("date_from", filters.date_from);
    if (filters?.date_to) params.append("date_to", filters.date_to);
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/my-bids${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson<PaginatedResponse<BidModel>>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  async placeBid(
    auctionId: number,
    amount: number
  ): Promise<SingleResponse<BidModel>> {
    return await GlobalFetchJson<SingleResponse<BidModel>>(
      `/auctions/${auctionId}/bids`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ bid_amount: amount }),
      },
      this.baseUrl
    );
  }

  async cancelBid(
    bidId: number
  ): Promise<{ message: string; bid: BidModel; wallet_balance: string }> {
    return await GlobalFetchJson(
      `/bids/${bidId}/cancel`,
      { method: "POST" },
      this.baseUrl
    );
  }

  // Auction Browsing
  async getAuctions(filters?: {
    status?: "active" | "ended" | "upcoming";
    vendor_id?: number;
    category_id?: number;
    search?: string;
    min_price?: number;
    max_price?: number;
    ending_soon?: boolean;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<AuctionModel>> {
    const params = new URLSearchParams();
    if (filters?.status) params.append("status", filters.status);
    if (filters?.vendor_id)
      params.append("vendor_id", filters.vendor_id.toString());
    if (filters?.category_id)
      params.append("category_id", filters.category_id.toString());
    if (filters?.search) params.append("search", filters.search);
    if (filters?.min_price)
      params.append("min_price", filters.min_price.toString());
    if (filters?.max_price)
      params.append("max_price", filters.max_price.toString());
    if (filters?.ending_soon !== undefined)
      params.append("ending_soon", filters.ending_soon.toString());
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/auctions${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson<PaginatedResponse<AuctionModel>>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  async getAuctionById(
    auctionId: number
  ): Promise<SingleResponse<AuctionModel>> {
    return await GlobalFetchJson<SingleResponse<AuctionModel>>(
      `/auctions/${auctionId}`,
      { method: "GET" },
      this.baseUrl
    );
  }

  // Wallet Management
  async getWallet(): Promise<SingleResponse<WalletModel>> {
    return await GlobalFetchJson<SingleResponse<WalletModel>>(
      `/wallet`,
      { method: "GET" },
      this.baseUrl
    );
  }

  async depositFunds(
    depositData: WalletDepositForm
  ): Promise<{ transaction: TransactionModel; wallet: WalletModel }> {
    return await GlobalFetchJson(
      `/wallet/deposit`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(depositData),
      },
      this.baseUrl
    );
  }

  async withdrawFunds(
    withdrawData: WalletWithdrawForm
  ): Promise<{ transaction: TransactionModel; wallet: WalletModel }> {
    return await GlobalFetchJson(
      `/wallet/withdraw`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(withdrawData),
      },
      this.baseUrl
    );
  }

  async getWalletTransactions(
    filters?: TransactionFilters
  ): Promise<PaginatedResponse<TransactionModel>> {
    const params = new URLSearchParams();
    if (filters?.type) params.append("type", filters.type);
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/wallet/transactions${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson<PaginatedResponse<TransactionModel>>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  async getWalletHolds(filters?: {
    status?: "active" | "released" | "applied";
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<WalletHoldModel>> {
    const params = new URLSearchParams();
    if (filters?.status) params.append("status", filters.status);
    if (filters?.per_page)
      params.append("per_page", filters.per_page.toString());
    if (filters?.page) params.append("page", filters.page.toString());

    const queryString = params.toString();
    const url = `/wallet/holds${queryString ? `?${queryString}` : ""}`;

    return await GlobalFetchJson<PaginatedResponse<WalletHoldModel>>(
      url,
      { method: "GET" },
      this.baseUrl
    );
  }

  // User Profile
  async getUserProfile(): Promise<SingleResponse<any>> {
    return await GlobalFetchJson<SingleResponse<any>>(
      `/user`,
      { method: "GET" },
      this.baseUrl
    );
  }

  // Dashboard Statistics
  async getBuyerStats(): Promise<{
    totalBids: number;
    activeBids: number;
    wonAuctions: number;
    lostBids: number;
    totalSpent: string;
    walletBalance: string;
    heldBalance: string;
  }> {
    try {
      // Try to get from a dedicated stats endpoint
      return await GlobalFetchJson(
        `/buyer/stats`,
        { method: "GET" },
        this.baseUrl
      );
    } catch (error) {
      // Fallback: calculate from individual endpoints
      return this.calculateBuyerStats();
    }
  }

  private async calculateBuyerStats() {
    try {
      const [bidsResponse, walletResponse] = await Promise.all([
        this.getMyBids({ per_page: 1 }),
        this.getWallet(),
      ]);

      const activeBidsResponse = await this.getMyBids({
        status: "active",
        per_page: 1,
      });
      const wonBidsResponse = await this.getMyBids({
        status: "won",
        per_page: 1,
      });
      const lostBidsResponse = await this.getMyBids({
        status: "lost",
        per_page: 1,
      });

      return {
        totalBids: bidsResponse.meta?.total || 0,
        activeBids: activeBidsResponse.meta?.total || 0,
        wonAuctions: wonBidsResponse.meta?.total || 0,
        lostBids: lostBidsResponse.meta?.total || 0,
        totalSpent: "0.00", // Would need to calculate from won bids
        walletBalance: walletResponse.data.balance,
        heldBalance: walletResponse.data.held_balance,
      };
    } catch (error) {
      console.error("Error calculating buyer stats:", error);
      return {
        totalBids: 0,
        activeBids: 0,
        wonAuctions: 0,
        lostBids: 0,
        totalSpent: "0.00",
        walletBalance: "0.00",
        heldBalance: "0.00",
      };
    }
  }

  // Watchlist/Favorites (if implemented in the future)
  async getWatchlist(): Promise<PaginatedResponse<AuctionModel>> {
    try {
      return await GlobalFetchJson<PaginatedResponse<AuctionModel>>(
        `/watchlist`,
        { method: "GET" },
        this.baseUrl
      );
    } catch (error) {
      // Return empty list if not implemented
      return {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          last_page: 1,
          links: [],
          path: "",
          per_page: 15,
          to: 0,
          total: 0,
        },
      };
    }
  }

  async addToWatchlist(auctionId: number): Promise<{ message: string }> {
    return await GlobalFetchJson(
      `/watchlist/${auctionId}`,
      { method: "POST" },
      this.baseUrl
    );
  }

  async removeFromWatchlist(auctionId: number): Promise<{ message: string }> {
    return await GlobalFetchJson(
      `/watchlist/${auctionId}`,
      { method: "DELETE" },
      this.baseUrl
    );
  }

  // Categories (for filtering)
  async getCategories(): Promise<PaginatedResponse<any>> {
    return await GlobalFetchJson<PaginatedResponse<any>>(
      `/categories`,
      { method: "GET" },
      this.baseUrl
    );
  }
}

export const buyerService = new BuyerService();
