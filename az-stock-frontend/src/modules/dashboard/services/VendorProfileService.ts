import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import {
  VendorModel,
  PaginatedResponse,
  SingleResponse,
} from "../models/DashboardModels";

export interface CreateVendorForm {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  description?: string;
  website?: string;
  logo?: File;
}

export interface UpdateVendorForm {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  description?: string;
  website?: string;
  logo?: File;
  is_active?: boolean;
}

export interface VendorFilters {
  search?: string;
  is_active?: boolean;
  per_page?: number;
  page?: number;
}

export interface VendorStatistics {
  auctions: {
    total: number;
    active: number;
    completed: number;
    draft: number;
    cancelled: number;
  };
  revenue: {
    total: number;
    this_month: number;
    last_month: number;
  };
  reviews: {
    total: number;
    average_rating: number;
    rating_distribution: {
      [key: string]: number;
    };
  };
  performance: {
    success_rate: number;
    average_auction_duration: number;
    repeat_bidders: number;
  };
  recent_activity: {
    recent_auctions: any[];
    recent_reviews: any[];
  };
}

export default class VendorProfileService implements GlobalService {
  methods = {
    // Get all vendors (public)
    async getVendors(
      filters?: VendorFilters
    ): Promise<PaginatedResponse<VendorModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.search) params.append("search", filters.search);
        if (filters?.is_active !== undefined)
          params.append("is_active", filters.is_active.toString());
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());
        if (filters?.page) params.append("page", filters.page.toString());

        const queryString = params.toString();
        const url = `/vendors${queryString ? `?${queryString}` : ""}`;

        const response = await GlobalFetchJson<PaginatedResponse<VendorModel>>(
          url,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Get vendor by ID (public)
    async getVendorById(vendorId: number): Promise<SingleResponse<VendorModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<VendorModel>>(
          `/vendors/${vendorId}`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Get vendor by slug (public)
    async getVendorBySlug(slug: string): Promise<SingleResponse<VendorModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<VendorModel>>(
          `/vendors/${slug}`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Get vendor statistics (public)
    async getVendorStatistics(vendorId: number): Promise<VendorStatistics> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<VendorStatistics>(
          `/vendors/${vendorId}/statistics`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Get current vendor profile (private - for dashboard)
    async getMyProfile(): Promise<SingleResponse<VendorModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<VendorModel>>(
          `/vendor/profile`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Create vendor profile
    async createVendor(
      vendorData: CreateVendorForm
    ): Promise<SingleResponse<VendorModel>> {
      try {
        const formData = new FormData();
        
        // Add text fields
        formData.append("name", vendorData.name);
        formData.append("email", vendorData.email);
        if (vendorData.phone) formData.append("phone", vendorData.phone);
        if (vendorData.address) formData.append("address", vendorData.address);
        if (vendorData.description) formData.append("description", vendorData.description);
        if (vendorData.website) formData.append("website", vendorData.website);
        
        // Add logo file if provided
        if (vendorData.logo) {
          formData.append("logo", vendorData.logo);
        }

        const response = await GlobalFetchJson<SingleResponse<VendorModel>>(
          `/vendors`,
          {
            method: "POST",
            body: formData,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Update vendor profile
    async updateVendor(
      vendorId: number,
      vendorData: UpdateVendorForm
    ): Promise<SingleResponse<VendorModel>> {
      try {
        const formData = new FormData();
        
        // Add text fields only if they exist
        if (vendorData.name) formData.append("name", vendorData.name);
        if (vendorData.email) formData.append("email", vendorData.email);
        if (vendorData.phone !== undefined) formData.append("phone", vendorData.phone || "");
        if (vendorData.address !== undefined) formData.append("address", vendorData.address || "");
        if (vendorData.description !== undefined) formData.append("description", vendorData.description || "");
        if (vendorData.website !== undefined) formData.append("website", vendorData.website || "");
        if (vendorData.is_active !== undefined) formData.append("is_active", vendorData.is_active.toString());
        
        // Add logo file if provided
        if (vendorData.logo) {
          formData.append("logo", vendorData.logo);
        }

        // Use PUT method with _method override for file uploads
        formData.append("_method", "PUT");

        const response = await GlobalFetchJson<SingleResponse<VendorModel>>(
          `/vendors/${vendorId}`,
          {
            method: "POST", // Laravel expects POST with _method override for file uploads
            body: formData,
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    // Delete vendor
    async deleteVendor(vendorId: number): Promise<{ message: string }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{ message: string }>(
          `/vendors/${vendorId}`,
          {
            method: "DELETE",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },
  };
}
