import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import {
  ProductModel,
  AuctionModel,
  CategoryModel,
  PaginatedResponse,
  SingleResponse,
  CreateProductForm,
  CreateAuctionForm,
  UpdateAuctionForm,
  ProductFilters,
  AuctionFilters,
  CategoryFilters,
} from "../models/DashboardModels";

export default class VendorService implements GlobalService {
  methods = {
    // Product Management
    async getMyProducts(
      filters?: ProductFilters
    ): Promise<PaginatedResponse<ProductModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.category_id)
          params.append("category_id", filters.category_id.toString());
        if (filters?.search) params.append("search", filters.search);
        if (filters?.has_auction !== undefined)
          params.append("has_auction", filters.has_auction.toString());
        if (filters?.status) params.append("status", filters.status);
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());
        if (filters?.page) params.append("page", filters.page.toString());

        const queryString = params.toString();
        const url = `/my-products${queryString ? `?${queryString}` : ""}`;

        const response = await GlobalFetchJson<PaginatedResponse<ProductModel>>(
          url,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async getProductById(
      productId: number
    ): Promise<SingleResponse<ProductModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<ProductModel>>(
          `/products/${productId}`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async createProduct(
      productData: CreateProductForm
    ): Promise<SingleResponse<ProductModel>> {
      try {
        let featuredImageUrl: string | undefined;
        let galleryImageUrls: string[] = [];

        // Step 1: Upload featured image if provided
        if (productData.featuredImage) {
          const featuredUploadResponse = await this.uploadImages([
            productData.featuredImage,
          ]);
          featuredImageUrl = featuredUploadResponse.images[0]?.url;
        }

        // Step 2: Upload gallery images if provided
        if (productData.images && productData.images.length > 0) {
          const galleryUploadResponse = await this.uploadImages(
            productData.images
          );
          galleryImageUrls = galleryUploadResponse.images.map((img) => img.url);
        }

        // Step 3: Create the product with image URLs
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const jsonData: any = {
          name: productData.name,
          description: productData.description,
          category_id: productData.category_id,
        };

        // Add featured image URL if provided
        if (featuredImageUrl) {
          jsonData.image_url = featuredImageUrl;
        }

        // Add gallery image URLs if provided
        if (galleryImageUrls.length > 0) {
          jsonData.image_urls = galleryImageUrls;
        }

        const response = await GlobalFetchJson<SingleResponse<ProductModel>>(
          `/products`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify(jsonData),
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    async updateProduct(
      productId: number,
      productData: Partial<CreateProductForm>,
      replaceImages: boolean = false
    ): Promise<SingleResponse<ProductModel>> {
      try {
        let featuredImageUrl: string | undefined;
        let galleryImageUrls: string[] = [];

        // Step 1: Upload featured image if provided
        if (productData.featuredImage) {
          const featuredUploadResponse = await this.uploadImages([
            productData.featuredImage,
          ]);
          featuredImageUrl = featuredUploadResponse.images[0]?.url;
        }
        // Step 2: Upload gallery images if provided
        if (productData.images && productData.images.length > 0) {
          const galleryUploadResponse = await this.uploadImages(
            productData.images
          );
          galleryImageUrls = galleryUploadResponse.images.map(
            (img: any) => img.url
          );
        }

        // Step 3: Update the product with image URLs
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const jsonData: any = {};
        if (productData.name) jsonData.name = productData.name;
        if (productData.description)
          jsonData.description = productData.description;
        if (productData.category_id)
          jsonData.category_id = productData.category_id;

        // Add featured image URL if provided
        if (featuredImageUrl) {
          jsonData.image_url = featuredImageUrl;
        }

        // Add gallery image URLs if provided
        if (galleryImageUrls.length > 0) {
          jsonData.image_urls = galleryImageUrls;
          jsonData.replace_images = replaceImages;
        }

        const response = await GlobalFetchJson<SingleResponse<ProductModel>>(
          `/products/${productId}`,
          {
            method: "PUT",
            headers: headers,
            body: JSON.stringify(jsonData),
          },
          "/api/proxy"
        );

        return response;
      } catch (error) {
        throw error;
      }
    },

    async deleteProduct(productId: number): Promise<{ message: string }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{ message: string }>(
          `/products/${productId}`,
          {
            method: "DELETE",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    // Image Management Methods
    async uploadImages(images: File[]): Promise<{
      message: string;
      images: Array<{
        url: string;
        thumbnail_url: string;
        original_name: string;
        path: string;
      }>;
      upload_errors: string[];
    }> {
      try {
        // Import Vercel Blob service dynamically to avoid SSR issues
        const { vercelBlobService } = await import('@/services/VercelBlobService');

        if (!vercelBlobService.isConfigured()) {
          throw new Error('Vercel Blob Storage is not configured');
        }

        // Generate directory for this upload session
        const directory = `temp/${Date.now()}`;

        // Upload images to Vercel Blob
        const uploadResult = await vercelBlobService.uploadFiles(images, directory);

        if (uploadResult.failed.length > 0) {
          console.warn('Some uploads failed:', uploadResult.failed);
        }

        // Send URLs to backend for processing
        const imageData = uploadResult.successful.map((result, index) => ({
          url: result.url,
          original_name: images[index]?.name || 'unknown',
          size: result.size,
        }));

        if (imageData.length > 0) {
          // Send to backend to validate and process
          const backendResponse = await GlobalFetchJson<{
            message: string;
            images: Array<{
              url: string;
              thumbnail_url: string;
              original_name: string;
              path: string;
            }>;
            upload_errors: string[];
          }>(
            `/images/upload`,
            {
              method: "POST",
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ images: imageData }),
            },
            "/api/proxy"
          );

          return backendResponse;
        }

        // If no successful uploads, return error
        const uploadErrors = uploadResult.failed.map(failure =>
          `${failure.file.name}: ${failure.error}`
        );

        return {
          message: 'No images were uploaded successfully',
          images: [],
          upload_errors: uploadErrors,
        };
      } catch (error) {
        throw error;
      }
    },

    async getProductImages(productId: number): Promise<{
      images: {
        id: number;
        url: string;
        thumbnail_url: string;
        original_name: string;
        file_size: number | null;
        created_at: string;
      }[];
    }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{
          images: {
            id: number;
            url: string;
            thumbnail_url: string;
            original_name: string;
            file_size: number | null;
            created_at: string;
          }[];
        }>(
          `/products/${productId}/images`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async deleteProductImage(imageId: number): Promise<{ message: string }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{ message: string }>(
          `/images/${imageId}`,
          {
            method: "DELETE",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    // Auction Management
    async getMyAuctions(
      filters?: AuctionFilters
    ): Promise<PaginatedResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.status) params.append("status", filters.status);
        if (filters?.category_id)
          params.append("category_id", filters.category_id.toString());
        if (filters?.search) params.append("search", filters.search);
        if (filters?.ending_soon !== undefined)
          params.append("ending_soon", filters.ending_soon.toString());
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());
        if (filters?.page) params.append("page", filters.page.toString());

        const queryString = params.toString();
        const url = `/my-auctions${queryString ? `?${queryString}` : ""}`;

        const response = await GlobalFetchJson<PaginatedResponse<AuctionModel>>(
          url,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async getAuctionById(
      auctionId: number
    ): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<AuctionModel>>(
          `/auctions/${auctionId}`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async createAuction(
      auctionData: CreateAuctionForm
    ): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<AuctionModel>>(
          `/auctions`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify(auctionData),
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async updateAuction(
      auctionId: number,
      auctionData: UpdateAuctionForm
    ): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<AuctionModel>>(
          `/auctions/${auctionId}`,
          {
            method: "PUT",
            headers: headers,
            body: JSON.stringify(auctionData),
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async endAuction(auctionId: number): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<AuctionModel>>(
          `/auctions/${auctionId}/end`,
          {
            method: "POST",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async processRefunds(auctionId: number): Promise<{
      message: string;
      processed_bids: number;
      failed_bids: number;
    }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{
          message: string;
          processed_bids: number;
          failed_bids: number;
        }>(
          `/auctions/${auctionId}/refunds`,
          {
            method: "POST",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    // Categories
    async getCategories(
      filters?: CategoryFilters
    ): Promise<PaginatedResponse<CategoryModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.search) params.append("search", filters.search);
        if (filters?.has_products !== undefined)
          params.append("has_products", filters.has_products.toString());
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());

        const queryString = params.toString();
        const url = `/categories${queryString ? `?${queryString}` : ""}`;

        const response = await GlobalFetchJson<
          PaginatedResponse<CategoryModel>
        >(
          url,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    // Analytics
    async getVendorStats(): Promise<{
      totalProducts: number;
      totalAuctions: number;
      activeAuctions: number;
      totalBids: number;
      endedAuctions: number;
      totalRevenue: string;
      averageBidAmount: string;
    }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{
          totalProducts: number;
          totalAuctions: number;
          activeAuctions: number;
          totalBids: number;
          endedAuctions: number;
          totalRevenue: string;
          averageBidAmount: string;
        }>(
          `/vendor/stats`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        // Fallback to basic stats
        return {
          totalProducts: 0,
          totalAuctions: 0,
          activeAuctions: 0,
          totalBids: 0,
          endedAuctions: 0,
          totalRevenue: "0",
          averageBidAmount: "0",
        };
      }
    },

    async getAnalytics(): Promise<{
      totalProducts: number;
      totalAuctions: number;
      activeAuctions: number;
      totalBids: number;
      totalRevenue: string;
      averageBidAmount: string;
      topPerformingProducts: Array<{
        product_name: string;
        auction_count: number;
        total_bids: number;
        highest_bid: string;
      }>;
      recentActivity: Array<{
        type: string;
        description: string;
        created_at: string;
      }>;
    }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{
          totalProducts: number;
          totalAuctions: number;
          activeAuctions: number;
          totalBids: number;
          totalRevenue: string;
          averageBidAmount: string;
          topPerformingProducts: Array<{
            product_name: string;
            auction_count: number;
            total_bids: number;
            highest_bid: string;
          }>;
          recentActivity: Array<{
            type: string;
            description: string;
            created_at: string;
          }>;
        }>(
          `/vendor/analytics`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        // Fallback to basic stats
        const stats = await this.getVendorStats();
        return {
          ...stats,
          topPerformingProducts: [],
          recentActivity: [],
        };
      }
    },
  };
}
