import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import { BidModel } from "@/modules/common/models/BidModel";
import { _revalidate } from "@/utils";
import { authOptions } from "@/utils/auth";

export default class BidService implements GlobalService {
  methods = {
    async getMyBids(): Promise<{ data: BidModel[] }> {
      try {
        const headers = new Headers();

        const response = await GlobalFetchJson<{ data: BidModel[] }>(
          `/my-bids`,
          {
            method: "GET",
            headers: headers,
          }
        );
        console.log("test", response);
        return response;
      } catch (error) {
        throw error;
      }
    },
  };
}
