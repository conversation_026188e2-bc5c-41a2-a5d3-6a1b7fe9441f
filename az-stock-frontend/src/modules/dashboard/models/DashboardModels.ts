// Dashboard-specific models based on API documentation

export interface UserModel {
  id: number;
  name: string;
  email: string;
  roles: ("admin" | "vendor" | "buyer")[];
  role?: "admin" | "vendor" | "buyer"; // Legacy field for backward compatibility
  email_verified_at: string | null;
  verification_status?: {
    buyer?: boolean;
    vendor?: boolean;
    admin?: boolean;
  };
  created_at: string;
  updated_at: string;
  vendor?: VendorModel;
}

export interface VendorModel {
  id: number;
  name: string;
  slug: string;
  email: string;
  logo: string | null;
  is_active: boolean;
  user_id?: number;
  user?: UserModel;
}

export interface CategoryModel {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  products_count?: number;
}

export interface ProductImageModel {
  id: number;
  url: string;
  thumbnail_url: string;
  original_name: string;
  file_size: number | null;
  created_at: string;
}

export interface ProductModel {
  id: number;
  name: string;
  description: string | null;
  image: string | null; // Legacy field - now represents featured image
  featured_image_url?: string | null; // New dedicated featured image field
  category_id: number;
  vendor_id: number;
  created_at: string | null;
  updated_at: string | null;
  category?: CategoryModel;
  vendor?: VendorModel;
  images?: ProductImageModel[];
}

export interface BidModel {
  id: number;
  bid_amount: string;
  status: "active" | "won" | "lost" | "cancelled";
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    name: string;
  };
  auction?: AuctionModel;
}

export interface AuctionModel {
  id: number;
  start_time: string;
  end_time: string;
  starting_price: string;
  current_price: string;
  reserve_price: string;
  status: string;
  created_at: string;
  updated_at: string;
  product: ProductModel;
  vendor: VendorModel;
  bids?: BidModel[];
  is_active: boolean;
  winning_bid?: BidModel;
  // Additional properties for compatibility
  starting_bid?: number;
  current_bid?: number;
  bids_count?: number;
}

export interface WalletModel {
  id: number;
  user_id: number;
  balance: string;
  held_balance: string;
  available_balance: string;
  created_at: string;
  updated_at: string;
}

export interface TransactionModel {
  id: number;
  wallet_id: number;
  type: "deposit" | "withdrawal" | "hold" | "release" | "payment";
  amount: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ActivityLogModel {
  id: number;
  user_id: number;
  action_type: "auth" | "crud" | "system";
  entity_type: "user" | "product" | "auction" | "bid";
  entity_id: number | null;
  description: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  user?: UserModel;
}

export interface WalletHoldModel {
  id: number;
  wallet_id: number;
  amount: string;
  status: "active" | "released" | "applied";
  reason: string;
  created_at: string;
  updated_at: string;
}

// Response interfaces
export interface PaginationLinks {
  first: string;
  last: string;
  prev: string | null;
  next: string | null;
}

export interface PaginationMeta {
  current_page: number;
  from: number;
  last_page: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
  path: string;
  per_page: number;
  to: number;
  total: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  links?: PaginationLinks;
  meta?: PaginationMeta;
}

export interface SingleResponse<T> {
  data: T;
}

// Dashboard-specific interfaces
export interface DashboardStats {
  totalUsers?: number;
  totalAuctions?: number;
  totalBids?: number;
  totalRevenue?: string;
  activeAuctions?: number;
  endedAuctions?: number;
  totalProducts?: number;
  totalCategories?: number;
}

export interface ActivityLogStats {
  total_logs: number;
  logs_by_action_type: Record<string, { action_type: string; count: number }>;
  logs_by_entity_type: Record<string, { action_type: string; count: number }>;
  top_users: Array<{
    user_id: number;
    user_name: string;
    activity_count: number;
  }>;
  daily_activity: Array<{
    date: string;
    count: number;
  }>;
}

// Verification interfaces
export interface UserVerificationStatus {
  user_id: number;
  name: string;
  email: string;
  verification_status: {
    buyer: boolean;
    vendor: boolean;
    admin: boolean;
  };
  roles: ("admin" | "vendor" | "buyer")[];
}

export interface VerifyUserForm {
  role: "buyer" | "vendor" | "admin";
  verified: boolean;
}

// Form interfaces
export interface CreateUserForm {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  role?: "admin" | "vendor" | "buyer"; // Legacy single role
  roles?: ("admin" | "vendor" | "buyer")[]; // New multi-role support
}

export interface UpdateUserForm {
  name?: string;
  email?: string;
  role?: "admin" | "vendor" | "buyer"; // Legacy single role
  roles?: ("admin" | "vendor" | "buyer")[]; // New multi-role support
  password?: string;
}

export interface CreateCategoryForm {
  name: string;
}

export interface CreateProductForm {
  name: string;
  description: string;
  category_id: number;
  featuredImage?: File;
  images?: File[];
}

export interface CreateAuctionForm {
  product_id: number;
  start_time: string;
  end_time: string;
  starting_price: number;
  reserve_price: number;
}

export interface UpdateAuctionForm {
  end_time?: string;
  reserve_price?: number;
  status?: string;
}

export interface WalletDepositForm {
  amount: number;
  payment_method: string;
  payment_details: {
    card_number: string;
    expiry_month: number;
    expiry_year: number;
    cvc: string;
  };
}

export interface WalletWithdrawForm {
  amount: number;
  withdrawal_method: string;
  withdrawal_details: {
    bank_name: string;
    account_number: string;
    routing_number: string;
  };
}

// Filter interfaces
export interface UserFilters {
  role?: "admin" | "vendor" | "buyer";
  is_active?: boolean;
  search?: string;
  created_after?: string;
  created_before?: string;
  per_page?: number;
  page?: number;
}

export interface AuctionFilters {
  status?: "active" | "ended" | "upcoming";
  category_id?: number;
  vendor_id?: number;
  search?: string;
  min_price?: number;
  max_price?: number;
  ending_soon?: boolean;
  per_page?: number;
  page?: number;
}

export interface BidFilters {
  status?: "active" | "won" | "lost" | "cancelled";
  auction_status?: "active" | "ended" | "upcoming";
  category_id?: number;
  search?: string;
  min_amount?: number;
  max_amount?: number;
  date_from?: string;
  date_to?: string;
  per_page?: number;
  page?: number;
}

export interface ActivityLogFilters {
  user_id?: number;
  action_type?: "auth" | "crud" | "system";
  entity_type?: "user" | "product" | "auction" | "bid";
  entity_id?: number;
  start_date?: string;
  end_date?: string;
  per_page?: number;
  page?: number;
}

export interface ProductFilters {
  category_id?: number;
  vendor_id?: number;
  search?: string;
  min_price?: number;
  max_price?: number;
  has_auction?: boolean;
  status?: string;
  per_page?: number;
  page?: number;
}

export interface CategoryFilters {
  search?: string;
  has_products?: boolean;
  per_page?: number;
  page?: number;
}

export interface TransactionFilters {
  type?: "deposit" | "withdrawal" | "hold" | "release" | "payment";
  per_page?: number;
  page?: number;
}
