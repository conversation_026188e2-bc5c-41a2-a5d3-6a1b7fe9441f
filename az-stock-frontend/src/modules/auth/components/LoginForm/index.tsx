"use client";
import Container from "@/modules/common/components/Container";
import GlobalInput from "@/modules/common/components/GlobaInput";
import GlobalButton from "@/modules/common/components/GlobalButton";
import GlobalForm from "@/modules/common/components/GlobalForm";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { IoCloseCircle } from "react-icons/io5";
import Link from "next/link";
import { z } from "zod";

const schema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(5, "Password should contain at least 5 characters"),
});

const LoginForm = () => {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | undefined>(
    undefined
  );
  const [loading, setLoading] = useState(false);

  const onSubmit = async (data: { email: string; password: string }) => {
    try {
      setLoading(true);
      console.log("Attempting login with:", data);

      const res = await signIn("signin", {
        ...data,
        redirect: false,
        callbackUrl: "/dashboard",
      });

      console.log("Login response:", res);

      if (res?.ok) {
        router.push("/dashboard");
      } else if (res?.error) {
        console.error("Login failed:", res.error);
        setErrorMessage("Invalid email or password. Please try again.");
      }
    } catch (error) {
      console.error("An error occurred during login:", error);
      setErrorMessage("An error occurred during login. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  return (
    <Container
      className="bg-white rounded-lg !p-4 shadow !max-w-3xl "
      data-aos="fade-in"
    >
      <div className="flex flex-col gap-1 items-center">
        <h1 className="text-xl"> Login</h1>{" "}
      </div>
      <GlobalForm
        schema={schema}
        className="flex flex-col gap-4 pt-5"
        onSubmit={(e) => {
          onSubmit(e);
        }}
      >
        <GlobalInput
          name="email"
          type="email"
          placeholder="<EMAIL>"
          label="Email"
        />
        <GlobalInput
          name="password"
          type="password"
          placeholder="password"
          label="Password"
        />

        <div className="flex gap-2">
          <div className="w-full">
            {" "}
            <GlobalButton
              loading={loading}
              onClick={() => {
                setLoading(true);
              }}
            >
              Login
            </GlobalButton>
          </div>
          <span className="text-text-primary btn btn-ghost underline font-light">
            Forgot Password?
          </span>
        </div>
        <div className="text-center mt-2">
          Don't have an account?{" "}
          <Link href="/register" className="text-blue-500 hover:underline">
            Register
          </Link>
        </div>
      </GlobalForm>
      <div className="mt-10">
        {errorMessage && (
          <div className="alert alert-error shadow-lg">
            <div className="flex gap-3">
              <IoCloseCircle
                size="20"
                color="#000"
                onClick={() => {
                  setErrorMessage(undefined);
                }}
              />
              <span>{errorMessage}</span>
            </div>
          </div>
        )}
      </div>
    </Container>
  );
};

export default LoginForm;
