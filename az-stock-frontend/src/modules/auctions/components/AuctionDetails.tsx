"use client";
import { AuctionModel } from "../models/AuctionModel";
import { formatDistanceToNow, format } from "date-fns";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Container from "@/modules/common/components/Container";
import AuctionService from "../services/AuctionService";
import { useRouter } from "next/navigation";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";

interface AuctionDetailsProps {
  data: AuctionModel;
}

const AuctionDetails: React.FC<AuctionDetailsProps> = ({
  data: initialData,
}) => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const isAuthenticated = status === "authenticated";
  const [bidAmount, setBidAmount] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [data, setData] = useState<AuctionModel>(initialData);
  const [refreshing, setRefreshing] = useState(false);
  const isAuctionActive =
    new Date(data.end_time) > new Date() && data.status === "active";
  const minBidAmount = parseFloat(data.current_price) + 0.01;

  const auctionService = new AuctionService();

  // Function to refresh auction data
  const refreshAuctionData = async () => {
    try {
      setRefreshing(true);
      const refreshedData = await auctionService.methods.getAuction({
        id: data.id,
      });

      // No need to modify bids, we'll use bid_amount directly

      setData(refreshedData);
    } catch (error) {
      console.error("Error refreshing auction data:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Auto-refresh the auction data every 30 seconds if it's active
  useEffect(() => {
    if (!isAuctionActive) return;

    const intervalId = setInterval(() => {
      refreshAuctionData();
    }, 30000); // 30 seconds

    return () => clearInterval(intervalId);
  }, [data.id, isAuctionActive]);

  // Track bid count to notify when new bids are placed
  const [previousBidCount, setPreviousBidCount] = useState<number>(
    data.bids?.length || 0
  );

  useEffect(() => {
    const currentBidCount = data.bids?.length || 0;

    // If this isn't the initial load and we have new bids
    if (previousBidCount > 0 && currentBidCount > previousBidCount) {
      // Show a notification about new bids
      const newBidsCount = currentBidCount - previousBidCount;
      setSuccessMessage(
        `${newBidsCount} new bid${
          newBidsCount > 1 ? "s" : ""
        } placed! Current price: $${data.current_price}`
      );

      // Clear the message after 5 seconds
      const timerId = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timerId);
    }

    // Update the previous bid count
    setPreviousBidCount(currentBidCount);
  }, [data.bids?.length]);

  const handleBidSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isAuthenticated) {
      setError("You must be logged in to place a bid");
      return;
    }

    if (!isAuctionActive) {
      setError("This auction is no longer active");
      return;
    }

    const amount = parseFloat(bidAmount);
    if (isNaN(amount) || amount < minBidAmount) {
      setError(`Bid must be at least $${minBidAmount.toFixed(2)}`);
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Place the bid using our service
      await auctionService.methods.placeBid({
        auctionId: data.id,
        amount: amount,
      });

      // Show success message
      setSuccessMessage(`Your bid of $${amount.toFixed(2)} has been placed!`);
      setBidAmount("");

      // Refresh the auction data to show the new bid
      await refreshAuctionData();
    } catch (err: any) {
      console.error("Bid error:", err);
      // Handle different types of errors
      if (typeof err === "string") {
        setError(err);
      } else if (err.message) {
        setError(err.message);
      } else if (err.errors) {
        // Handle Laravel validation errors
        if (Array.isArray(err.errors)) {
          setError(err.errors.join(", "));
        } else if (typeof err.errors === "object") {
          const errorMessages = Object.values(err.errors).flat();
          setError(errorMessages.join(", "));
        } else {
          setError(String(err.errors));
        }
      } else {
        setError("Failed to place bid. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container className="bg-white rounded-lg shadow-md p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left column - Image */}
        <div>
          <div className="relative h-80 bg-gray-200 rounded-lg overflow-hidden">
            {data.product.image ? (
              <GlobalImage
                src={data.product.image}
                alt=""
                className="w-full h-full object-contain"
                width={600}
                height={600}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500 text-xl font-bold">
                {data.product.name || "No Product Name"}
              </div>
            )}
            <div className="absolute top-0 right-0 bg-blue-600 text-white px-3 py-1 text-sm font-bold">
              {data.status.toUpperCase()}
            </div>
          </div>
          {data.product.images[0] && (
            <div className="flex gap-4 items-center pt-5">
              {data.product.images.map((d) => (
                <GlobalImage
                  src={d.url}
                  alt={d.original_name}
                  className="w-20 h-full"
                  width={400}
                  height={400}
                />
              ))}
            </div>
          )}
        </div>

        {/* Right column - Details */}
        <div>
          <h1 className="text-2xl font-bold mb-4">{data.product.name}</h1>

          <div className="mb-6">
            <p className="text-gray-700">
              {data.product.description || "No description available."}
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-gray-600 text-sm">Current Bid:</p>
              <p className="text-2xl font-bold text-green-600">
                ${data.current_price}
              </p>
            </div>
            <div>
              <p className="text-gray-600 text-sm">Starting Price:</p>
              <p className="text-lg">${data.starting_price}</p>
            </div>
            <div>
              <p className="text-gray-600 text-sm">Seller:</p>
              <p className="font-medium">{data.vendor.name}</p>
            </div>
            <div>
              <p className="text-gray-600 text-sm">Status:</p>
              <p className="font-medium capitalize">{data.status}</p>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center">
              <p className="text-gray-600 text-sm">Auction Ends:</p>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  refreshAuctionData();
                }}
                disabled={refreshing}
                className="text-blue-500 text-sm hover:text-blue-700 flex items-center"
              >
                {refreshing ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Refreshing...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <svg
                      className="h-4 w-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      ></path>
                    </svg>
                    Refresh
                  </span>
                )}
              </button>
            </div>
            <p className="font-medium">
              {format(new Date(data.end_time), "PPP 'at' p")}
              {isAuctionActive && (
                <span className="text-sm text-gray-500 ml-2">
                  (
                  {formatDistanceToNow(new Date(data.end_time), {
                    addSuffix: true,
                  })}
                  )
                </span>
              )}
            </p>
          </div>

          {isAuctionActive ? (
            <div>
              {isAuthenticated ? (
                <form onSubmit={handleBidSubmit} className="space-y-4">
                  <div>
                    <label
                      htmlFor="bidAmount"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Your Bid (min. ${minBidAmount.toFixed(2)})
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">$</span>
                      </div>
                      <input
                        type="number"
                        name="bid_amount"
                        id="bidAmount"
                        step="0.01"
                        min={minBidAmount}
                        value={bidAmount}
                        onChange={(e) => setBidAmount(e.target.value)}
                        className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                        placeholder="0.00"
                        required
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50"
                  >
                    {isSubmitting ? "Placing Bid..." : "Place Bid"}
                  </button>

                  {error && (
                    <div
                      className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mt-2"
                      role="alert"
                    >
                      <span className="block sm:inline">{error}</span>
                    </div>
                  )}

                  {successMessage && (
                    <div
                      className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mt-2"
                      role="alert"
                    >
                      <span className="block sm:inline">{successMessage}</span>
                    </div>
                  )}
                </form>
              ) : (
                <div className="bg-gray-100 p-4 rounded-md">
                  <p className="text-center mb-2">
                    You need to be logged in to place a bid
                  </p>
                  <Link
                    href="/login"
                    className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                  >
                    Log In to Bid
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gray-100 p-4 rounded-md">
              <p className="text-center text-gray-700">
                This auction has ended
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Bid History Section */}
      <div className="mt-10">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Bid History</h2>
          {refreshing && (
            <span className="text-sm text-gray-500 flex items-center">
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Updating...
            </span>
          )}
        </div>

        {data.bids && data.bids.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bidder
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.bids.map((bid) => (
                  <tr key={bid.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {/* {bid?.user?.name} */}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        ${bid.bid_amount}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {format(new Date(bid.created_at), "PPP 'at' p")}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="bg-gray-50 p-8 text-center rounded-lg">
            <p className="text-gray-500">No bids have been placed yet.</p>
            {isAuctionActive && isAuthenticated && (
              <p className="text-gray-600 mt-2">
                Be the first to place a bid on this auction!
              </p>
            )}
          </div>
        )}
      </div>
    </Container>
  );
};

export default AuctionDetails;
