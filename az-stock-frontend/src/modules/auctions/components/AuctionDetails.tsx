"use client";
import { AuctionModel } from "../models/AuctionModel";
import { formatDistanceToNow, format } from "date-fns";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Container from "@/modules/common/components/Container";
import AuctionService from "../services/AuctionService";
import { useRouter } from "next/navigation";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import BiddingInterface from "@/components/auctions/BiddingInterface";
import BidHistory from "@/components/auctions/BidHistory";
import AuctionTypeIndicator from "@/components/auctions/AuctionTypeIndicator";
import BidService from "@/modules/auctions/services/BidService";
import useGlobalService from "@/core/hook/useGlobalService";

interface AuctionDetailsProps {
  data: AuctionModel;
}

const AuctionDetails: React.FC<AuctionDetailsProps> = ({
  data: initialData,
}) => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const isAuthenticated = status === "authenticated";
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [data, setData] = useState<AuctionModel>(initialData);
  const [refreshing, setRefreshing] = useState(false);
  const bidService = useGlobalService(BidService);
  const isAuctionActive =
    new Date(data.end_time) > new Date() && data.status === "active";
  const minBidAmount = bidService.getMinimumBidAmount(data);
  const shouldShowBids = bidService.shouldShowBids(data);

  // Find user's current bid on this auction
  const userBid = data.bids?.find(
    (bid) =>
      bid.user_id === parseInt(session?.user?.id || "0") &&
      bid.status === "active"
  );

  // Check if current user is the vendor of this auction
  const isVendor = session?.user?.vendor?.id === data.vendor?.id;

  // Check if current user can view bids based on auction type and their role
  const canViewBids = () => {
    if (data.auction_type === "online") {
      return true; // Online auction bids are always visible
    }

    if (data.auction_type === "sealed") {
      if (data.status === "ended" || data.status === "completed") {
        return true; // Sealed auction bids are visible after auction ends
      }

      // During active sealed auction, only vendor can see bid count (not details)
      // and bidders can see their own bids
      return false;
    }

    return false;
  };

  const auctionService = new AuctionService();

  // Function to refresh auction data
  const refreshAuctionData = async () => {
    try {
      setRefreshing(true);
      const refreshedData = await auctionService.methods.getAuction({
        id: data.id,
      });

      // No need to modify bids, we'll use bid_amount directly

      setData(refreshedData);
    } catch (error) {
      console.error("Error refreshing auction data:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Auto-refresh the auction data every 30 seconds if it's active
  useEffect(() => {
    if (!isAuctionActive) return;

    const intervalId = setInterval(() => {
      refreshAuctionData();
    }, 30000); // 30 seconds

    return () => clearInterval(intervalId);
  }, [data.id, isAuctionActive]);

  // Track bid count to notify when new bids are placed
  const [previousBidCount, setPreviousBidCount] = useState<number>(
    data.bids?.length || 0
  );

  useEffect(() => {
    const currentBidCount = data.bids?.length || 0;

    // If this isn't the initial load and we have new bids
    if (previousBidCount > 0 && currentBidCount > previousBidCount) {
      // Show a notification about new bids
      const newBidsCount = currentBidCount - previousBidCount;
      setSuccessMessage(
        `${newBidsCount} new bid${
          newBidsCount > 1 ? "s" : ""
        } placed! Current price: $${data.current_price}`
      );

      // Clear the message after 5 seconds
      const timerId = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timerId);
    }

    // Update the previous bid count
    setPreviousBidCount(currentBidCount);
  }, [data.bids?.length]);

  return (
    <Container className="bg-white rounded-lg shadow-md p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left column - Images */}
        <div>
          <div className="relative h-80 bg-gray-200 rounded-lg overflow-hidden">
            {data.auction_items &&
            data.auction_items.length > 0 &&
            data.auction_items[0].images &&
            data.auction_items[0].images.length > 0 ? (
              <GlobalImage
                src={data.auction_items[0].images[0].url}
                alt={data.auction_items[0].item_name}
                className="w-full h-full object-contain"
                width={600}
                height={600}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500 text-xl font-bold">
                {data.title || "No Auction Title"}
              </div>
            )}
            <div className="absolute top-0 right-0 bg-blue-600 text-white px-3 py-1 text-sm font-bold">
              {data.status.toUpperCase()}
            </div>
            <div className="absolute top-0 left-0 m-2">
              <AuctionTypeIndicator auctionType={data.auction_type} size="sm" />
            </div>
          </div>
          {/* Display all images from all auction items */}
          {data.auction_items && data.auction_items.length > 0 && (
            <div className="flex gap-4 items-center pt-5 overflow-x-auto">
              {data.auction_items.map((item) =>
                item.images?.map((image, index) => (
                  <GlobalImage
                    key={`${item.id}-${index}`}
                    src={image.url}
                    alt={image.original_name}
                    className="w-20 h-20 object-cover rounded flex-shrink-0"
                    width={80}
                    height={80}
                  />
                ))
              )}
            </div>
          )}
        </div>

        {/* Right column - Details */}
        <div>
          <div className="flex items-start justify-between mb-4">
            <h1 className="text-2xl font-bold">{data.title}</h1>
            <AuctionTypeIndicator
              auctionType={data.auction_type}
              size="md"
              showDescription={false}
            />
          </div>

          <div className="mb-6">
            <p className="text-gray-700">
              {data.description || "No description available."}
            </p>
          </div>

          {/* Auction Type Information */}
          <div className="mb-6">
            <AuctionTypeIndicator
              auctionType={data.auction_type}
              size="sm"
              showDescription={true}
            />
          </div>

          {/* Auction Items Section */}
          {data.auction_items && data.auction_items.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">
                Items in this auction:
              </h3>
              <div className="space-y-3">
                {data.auction_items.map((item) => (
                  <div
                    key={item.id}
                    className="border-l-4 border-blue-500 pl-4"
                  >
                    <h4 className="font-medium text-gray-900">
                      {item.item_name}
                    </h4>
                    {item.item_description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {item.item_description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-gray-600 text-sm">
                {data.auction_type === "sealed"
                  ? "Starting Price:"
                  : "Current Bid:"}
              </p>
              <p className="text-2xl font-bold text-green-600">
                $
                {data.auction_type === "sealed"
                  ? data.starting_price
                  : data.current_price}
              </p>
              {data.auction_type === "sealed" && (
                <p className="text-xs text-gray-500 mt-1">
                  Current bids are hidden
                </p>
              )}
            </div>
            <div>
              <p className="text-gray-600 text-sm">
                {data.auction_type === "sealed"
                  ? "Minimum Bid:"
                  : "Starting Price:"}
              </p>
              <p className="text-lg">
                $
                {data.auction_type === "sealed"
                  ? data.starting_price
                  : data.starting_price}
              </p>
            </div>
            <div>
              <p className="text-gray-600 text-sm">Seller:</p>
              <p className="font-medium">{data.vendor.name}</p>
            </div>
            <div>
              <p className="text-gray-600 text-sm">Status:</p>
              <p className="font-medium capitalize">{data.status}</p>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center">
              <p className="text-gray-600 text-sm">Auction Ends:</p>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  refreshAuctionData();
                }}
                disabled={refreshing}
                className="text-blue-500 text-sm hover:text-blue-700 flex items-center"
              >
                {refreshing ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Refreshing...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <svg
                      className="h-4 w-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      ></path>
                    </svg>
                    Refresh
                  </span>
                )}
              </button>
            </div>
            <p className="font-medium">
              {format(new Date(data.end_time), "PPP 'at' p")}
              {isAuctionActive && (
                <span className="text-sm text-gray-500 ml-2">
                  (
                  {formatDistanceToNow(new Date(data.end_time), {
                    addSuffix: true,
                  })}
                  )
                </span>
              )}
            </p>
          </div>

          {/* Bidding Interface */}
          {isAuthenticated ? (
            <BiddingInterface
              auction={data}
              userBid={userBid}
              onBidPlaced={(bid) => {
                setSuccessMessage("Bid placed successfully!");
                refreshAuctionData();
              }}
              onBidUpdated={(bid) => {
                setSuccessMessage("Bid updated successfully!");
                refreshAuctionData();
              }}
              onBidCanceled={() => {
                setSuccessMessage("Bid canceled successfully!");
                refreshAuctionData();
              }}
            />
          ) : (
            <div className="bg-gray-100 p-4 rounded-md">
              <p className="text-center mb-2">
                You need to be logged in to place a bid
              </p>
              <Link
                href="/login"
                className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Log In to Bid
              </Link>
            </div>
          )}

          {/* Success Message */}
          {successMessage && (
            <div className="mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative">
              <span className="block sm:inline">{successMessage}</span>
            </div>
          )}
        </div>
      </div>

      {/* Bid History Section */}
      <div className="mt-10">
        <BidHistory auction={data} />
      </div>
    </Container>
  );
};

export default AuctionDetails;
