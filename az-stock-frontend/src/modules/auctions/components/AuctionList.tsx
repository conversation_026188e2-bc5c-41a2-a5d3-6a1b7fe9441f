"use client";
import { AuctionModel } from "../models/AuctionModel";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";

interface AuctionListProps {
  auctions: AuctionModel[];
}

const AuctionList: React.FC<AuctionListProps> = ({ auctions }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {auctions.map((auction) => (
        <Link
          href={`/auctions/${auction.id}`}
          key={auction.id}
          className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
        >
          <div className="relative h-48 bg-gray-200">
            {auction.auction_items &&
            auction.auction_items.length > 0 &&
            auction.auction_items[0].images &&
            auction.auction_items[0].images.length > 0 ? (
              <GlobalImage
                src={auction.auction_items[0].images[0].url}
                alt={auction.auction_items[0].item_name}
                width={400}
                height={400}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                {auction.title || "No Auction Title"}
              </div>
            )}
            <div className="absolute top-0 right-0 bg-blue-600 text-white px-2 py-1 text-xs font-bold">
              {auction.status.toUpperCase()}
            </div>
            {/* Show item count if multiple items */}
            {auction.auction_items && auction.auction_items.length > 1 && (
              <div className="absolute bottom-0 left-0 bg-black bg-opacity-70 text-white px-2 py-1 text-xs">
                {auction.auction_items.length} items
              </div>
            )}
          </div>
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-2 truncate">
              {auction.title}
            </h3>

            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600 text-sm">Current Bid:</span>
              <span className="text-green-600 font-bold">
                ${auction.current_price}
              </span>
            </div>

            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600 text-sm">Seller:</span>
              <span className="text-gray-800">{auction.vendor.name}</span>
            </div>

            <div className="text-sm text-gray-500 mt-2">
              {new Date(auction.end_time) > new Date() ? (
                <span>
                  Ends{" "}
                  {formatDistanceToNow(new Date(auction.end_time), {
                    addSuffix: true,
                  })}
                </span>
              ) : (
                <span className="text-red-500">Auction ended</span>
              )}
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
};

export default AuctionList;
