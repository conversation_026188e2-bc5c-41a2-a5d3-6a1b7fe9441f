import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import {
  AuctionModel,
  AuctionResponse,
  SingleAuctionResponse,
} from "../models/AuctionModel";
import { BidModel } from "@/modules/common/models/BidModel";

interface PlaceBidResponse {
  data: BidModel;
  message: string;
}

export default class AuctionService implements GlobalService {
  methods = {
    async getAuction({ id }: { id?: string | number }): Promise<AuctionModel> {
      try {
        const headers = new Headers();
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        const token = session?.accessToken;
        headers?.set("Content-Type", "application/json");
        headers?.set("Athorization", `Bearer ${token}`);
        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions/${id}`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        console.log("Auction response:", response);
        return response.data;
      } catch (error) {
        console.error("Error fetching auction:", error);
        throw error;
      }
    },

    async getAuctions({
      limit,
      category,
    }: {
      limit?: string;
      category?: string;
    }): Promise<AuctionModel[]> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        let url = `/auctions`;
        const queryParams = [];

        if (limit) {
          queryParams.push(`limit=${limit}`);
        }

        if (category) {
          queryParams.push(`category=${category}`);
        }

        if (queryParams.length > 0) {
          url += `?${queryParams.join("&")}`;
        }

        console.log(`Fetching auctions with URL: ${url}`);

        const response = await GlobalFetchJson<AuctionResponse>(url, {
          method: "GET",
          headers: headers,
          cache: "no-store",
        });

        console.log("Auctions response:", response);

        if (!response.data || !Array.isArray(response.data)) {
          console.error("Invalid auctions response format:", response);
          return [];
        }

        return response.data;
      } catch (error) {
        console.error("Error fetching auctions:", error);
        return [];
      }
    },

    async placeBid({
      auctionId,
      amount,
    }: {
      auctionId: number | string;
      amount: number | string;
    }): Promise<BidModel> {
      try {
        // Get the session to access the token
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to place a bid");
        }

        const headers = new Headers();
        // Set Content-Type to match the curl command
        headers.set("Content-Type", "application/json");
        // Set Authorization header with the token
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        console.log(
          `Placing bid on auction ${auctionId} with amount ${amount}`
        );
        console.log("Using token:", session.accessToken);

        const response = await GlobalFetchJson<PlaceBidResponse>(
          `/auctions/${auctionId}/bids`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({ bid_amount: amount }),
          }
        );

        console.log("Bid response:", response);

        // Check if the response has the expected structure
        if (!response || !response.data) {
          console.error("Invalid bid response format:", response);
          throw new Error("Invalid response format from bid API");
        }

        // Return the bid data directly
        return response.data;
      } catch (error: any) {
        console.error("Error placing bid:", error);

        // Format error message for better user experience
        if (error.message) {
          throw new Error(error.message);
        } else if (error.errors && typeof error.errors === "object") {
          // Handle Laravel validation errors
          const errorMessages = Object.values(error.errors).flat();
          throw new Error(errorMessages.join(", "));
        } else {
          throw new Error("Failed to place bid. Please try again.");
        }
      }
    },
  };
}
