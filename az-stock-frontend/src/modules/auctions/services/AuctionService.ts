import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import {
  AuctionModel,
  AuctionResponse,
  SingleAuctionResponse,
  CreateAuctionRequest,
  PlaceBidRequest,
  PlaceBidResponse,
  BidModel,
} from "@/types/auction";

export default class AuctionService implements GlobalService {
  methods = {
    async getAuction({ id }: { id?: string | number }): Promise<AuctionModel> {
      try {
        const headers = new Headers();
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        const token = session?.accessToken;
        headers?.set("Content-Type", "application/json");
        headers?.set("Authorization", `Bearer ${token}`);
        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions/${id}`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        console.log("Auction response:", response);
        return response.data;
      } catch (error) {
        console.error("Error fetching auction:", error);
        throw error;
      }
    },

    async getAuctions({
      status,
      auction_type,
      category_id,
      vendor_id,
      search,
      min_price,
      max_price,
      ending_soon,
      per_page,
    }: {
      status?: string;
      auction_type?: string;
      category_id?: number;
      vendor_id?: number;
      search?: string;
      min_price?: number;
      max_price?: number;
      ending_soon?: boolean;
      per_page?: number;
    } = {}): Promise<AuctionModel[]> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        let url = `/auctions`;
        const queryParams = [];

        if (status) queryParams.push(`status=${status}`);
        if (auction_type) queryParams.push(`auction_type=${auction_type}`);
        if (category_id) queryParams.push(`category_id=${category_id}`);
        if (vendor_id) queryParams.push(`vendor_id=${vendor_id}`);
        if (search) queryParams.push(`search=${encodeURIComponent(search)}`);
        if (min_price) queryParams.push(`min_price=${min_price}`);
        if (max_price) queryParams.push(`max_price=${max_price}`);
        if (ending_soon) queryParams.push(`ending_soon=${ending_soon}`);
        if (per_page) queryParams.push(`per_page=${per_page}`);

        if (queryParams.length > 0) {
          url += `?${queryParams.join("&")}`;
        }

        console.log(`Fetching auctions with URL: ${url}`);

        const response = await GlobalFetchJson<AuctionResponse>(url, {
          method: "GET",
          headers: headers,
          cache: "no-store",
        });

        console.log("Auctions response:", response);

        if (!response.data || !Array.isArray(response.data)) {
          console.error("Invalid auctions response format:", response);
          return [];
        }

        return response.data;
      } catch (error) {
        console.error("Error fetching auctions:", error);
        return [];
      }
    },

    async getMyAuctions({
      status,
      category_id,
      search,
      ending_soon,
      per_page,
    }: {
      status?: string;
      category_id?: number;
      search?: string;
      ending_soon?: boolean;
      per_page?: number;
    } = {}): Promise<AuctionModel[]> {
      try {
        const headers = new Headers();
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        const token = session?.accessToken;
        headers?.set("Content-Type", "application/json");
        headers?.set("Authorization", `Bearer ${token}`);

        let url = `/my-auctions`;
        const queryParams = [];

        if (status) queryParams.push(`status=${status}`);
        if (category_id) queryParams.push(`category_id=${category_id}`);
        if (search) queryParams.push(`search=${encodeURIComponent(search)}`);
        if (ending_soon) queryParams.push(`ending_soon=${ending_soon}`);
        if (per_page) queryParams.push(`per_page=${per_page}`);

        if (queryParams.length > 0) {
          url += `?${queryParams.join("&")}`;
        }

        const response = await GlobalFetchJson<AuctionResponse>(url, {
          method: "GET",
          headers: headers,
          cache: "no-store",
        });

        return response.data || [];
      } catch (error) {
        console.error("Error fetching my auctions:", error);
        return [];
      }
    },

    async createAuction(auctionData: CreateAuctionRequest): Promise<AuctionModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to create an auction");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify(auctionData),
          }
        );

        if (!response || !response.data) {
          throw new Error("Invalid response format from auction creation API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error creating auction:", error);
        if (error.message) {
          throw new Error(error.message);
        } else if (error.errors && typeof error.errors === "object") {
          const errorMessages = Object.values(error.errors).flat();
          throw new Error(errorMessages.join(", "));
        } else {
          throw new Error("Failed to create auction. Please try again.");
        }
      }
    },

    async placeBid({
      auctionId,
      bidData,
    }: {
      auctionId: number | string;
      bidData: PlaceBidRequest;
    }): Promise<BidModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to place a bid");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        console.log(
          `Placing bid on auction ${auctionId} with amount ${bidData.bid_amount}`
        );

        const response = await GlobalFetchJson<PlaceBidResponse>(
          `/auctions/${auctionId}/bids`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({ bid_amount: bidData.bid_amount }),
          }
        );

        console.log("Bid response:", response);

        if (!response || !response.data) {
          console.error("Invalid bid response format:", response);
          throw new Error("Invalid response format from bid API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error placing bid:", error);

        if (error.message) {
          throw new Error(error.message);
        } else if (error.errors && typeof error.errors === "object") {
          const errorMessages = Object.values(error.errors).flat();
          throw new Error(errorMessages.join(", "));
        } else {
          throw new Error("Failed to place bid. Please try again.");
        }
      }
    },
  };
}
