import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import {
  BidModel,
  PlaceBidRequest,
  PlaceBidResponse,
  UpdateBidRequest,
  CancelBidResponse,
  MyBidsResponse,
} from "@/types/auction";

export default class BidService implements GlobalService {
  methods = {
    /**
     * Place a bid on an auction
     */
    async placeBid({
      auctionId,
      bidData,
    }: {
      auctionId: number | string;
      bidData: PlaceBidRequest;
    }): Promise<BidModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to place a bid");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        console.log(
          `Placing bid on auction ${auctionId} with amount ${bidData.bid_amount}`
        );

        const response = await GlobalFetchJson<PlaceBidResponse>(
          `/auctions/${auctionId}/bids`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({ bid_amount: bidData.bid_amount }),
          }
        );

        console.log("Bid response:", response);

        if (!response || !response.data) {
          console.error("Invalid bid response format:", response);
          throw new Error("Invalid response format from bid API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error placing bid:", error);

        if (error.message) {
          throw new Error(error.message);
        } else if (error.errors && typeof error.errors === "object") {
          const errorMessages = Object.values(error.errors).flat();
          throw new Error(errorMessages.join(", "));
        } else {
          throw new Error("Failed to place bid. Please try again.");
        }
      }
    },

    /**
     * Update a bid (sealed auctions only)
     */
    async updateBid({
      bidId,
      bidData,
    }: {
      bidId: number | string;
      bidData: UpdateBidRequest;
    }): Promise<BidModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to update a bid");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        console.log(
          `Updating bid ${bidId} with amount ${bidData.bid_amount}`
        );

        const response = await GlobalFetchJson<PlaceBidResponse>(
          `/bids/${bidId}`,
          {
            method: "PUT",
            headers: headers,
            body: JSON.stringify({ bid_amount: bidData.bid_amount }),
          }
        );

        console.log("Update bid response:", response);

        if (!response || !response.data) {
          console.error("Invalid update bid response format:", response);
          throw new Error("Invalid response format from update bid API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error updating bid:", error);

        if (error.message) {
          throw new Error(error.message);
        } else if (error.errors && typeof error.errors === "object") {
          const errorMessages = Object.values(error.errors).flat();
          throw new Error(errorMessages.join(", "));
        } else {
          throw new Error("Failed to update bid. Please try again.");
        }
      }
    },

    /**
     * Cancel a bid (sealed auctions only)
     */
    async cancelBid({
      bidId,
    }: {
      bidId: number | string;
    }): Promise<BidModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to cancel a bid");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        console.log(`Canceling bid ${bidId}`);

        const response = await GlobalFetchJson<CancelBidResponse>(
          `/bids/${bidId}/cancel`,
          {
            method: "POST",
            headers: headers,
          }
        );

        console.log("Cancel bid response:", response);

        if (!response || !response.data) {
          console.error("Invalid cancel bid response format:", response);
          throw new Error("Invalid response format from cancel bid API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error canceling bid:", error);

        if (error.message) {
          throw new Error(error.message);
        } else if (error.errors && typeof error.errors === "object") {
          const errorMessages = Object.values(error.errors).flat();
          throw new Error(errorMessages.join(", "));
        } else {
          throw new Error("Failed to cancel bid. Please try again.");
        }
      }
    },

    /**
     * Get user's bids with filtering options
     */
    async getMyBids({
      status,
      auction_status,
      category_id,
      search,
      min_amount,
      max_amount,
      date_from,
      date_to,
      per_page,
      page,
    }: {
      status?: "active" | "won" | "lost" | "cancelled";
      auction_status?: "active" | "ended" | "upcoming";
      category_id?: number;
      search?: string;
      min_amount?: number;
      max_amount?: number;
      date_from?: string;
      date_to?: string;
      per_page?: number;
      page?: number;
    } = {}): Promise<BidModel[]> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to view your bids");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        let url = `/my-bids`;
        const queryParams = [];

        if (status) queryParams.push(`status=${status}`);
        if (auction_status) queryParams.push(`auction_status=${auction_status}`);
        if (category_id) queryParams.push(`category_id=${category_id}`);
        if (search) queryParams.push(`search=${encodeURIComponent(search)}`);
        if (min_amount) queryParams.push(`min_amount=${min_amount}`);
        if (max_amount) queryParams.push(`max_amount=${max_amount}`);
        if (date_from) queryParams.push(`date_from=${date_from}`);
        if (date_to) queryParams.push(`date_to=${date_to}`);
        if (per_page) queryParams.push(`per_page=${per_page}`);
        if (page) queryParams.push(`page=${page}`);

        if (queryParams.length > 0) {
          url += `?${queryParams.join("&")}`;
        }

        console.log(`Fetching my bids with URL: ${url}`);

        const response = await GlobalFetchJson<MyBidsResponse>(url, {
          method: "GET",
          headers: headers,
          cache: "no-store",
        });

        console.log("My bids response:", response);

        if (!response.data || !Array.isArray(response.data)) {
          console.error("Invalid my bids response format:", response);
          return [];
        }

        return response.data;
      } catch (error: any) {
        console.error("Error fetching my bids:", error);
        return [];
      }
    },
  };

  /**
   * Check if a bid can be updated (sealed auctions only)
   */
  canUpdateBid(
    auction: { auction_type: string; status: string; end_time: string },
    bid: BidModel
  ): boolean {
    // Only sealed auctions allow bid updates
    if (auction.auction_type !== "sealed") {
      return false;
    }

    // Bid must be active
    if (bid.status !== "active") {
      return false;
    }

    // Auction must be active and not ended
    if (auction.status !== "active") {
      return false;
    }

    // Check if auction hasn't ended yet
    const endTime = new Date(auction.end_time);
    const now = new Date();
    if (now >= endTime) {
      return false;
    }

    return true;
  }

  /**
   * Check if a bid can be canceled (sealed auctions only)
   */
  canCancelBid(
    auction: { auction_type: string; status: string; end_time: string },
    bid: BidModel
  ): boolean {
    // Only sealed auctions allow bid cancellation
    if (auction.auction_type !== "sealed") {
      return false;
    }

    // Bid must be active
    if (bid.status !== "active") {
      return false;
    }

    // Auction must be active and not ended
    if (auction.status !== "active") {
      return false;
    }

    // Check if auction hasn't ended yet
    const endTime = new Date(auction.end_time);
    const now = new Date();
    if (now >= endTime) {
      return false;
    }

    return true;
  }

  /**
   * Get minimum bid amount for an auction
   */
  getMinimumBidAmount(auction: {
    auction_type: string;
    starting_price: string;
    current_price: string;
  }): number {
    if (auction.auction_type === "sealed") {
      // For sealed auctions, minimum is starting price
      return parseFloat(auction.starting_price);
    } else {
      // For online auctions, minimum is higher than current price
      const currentPrice = parseFloat(auction.current_price);
      const startingPrice = parseFloat(auction.starting_price);
      return Math.max(currentPrice + 0.01, startingPrice);
    }
  }

  /**
   * Check if bids should be visible for an auction
   */
  shouldShowBids(auction: { auction_type: string; status: string }): boolean {
    // Online auctions: always show bids
    if (auction.auction_type === "online") {
      return true;
    }

    // Sealed auctions: only show bids after auction ends
    if (auction.auction_type === "sealed") {
      return auction.status === "completed" || auction.status === "ended";
    }

    return false;
  }

  /**
   * Get bid validation message for different auction types
   */
  getBidValidationMessage(auction: {
    auction_type: string;
    starting_price: string;
    current_price: string;
  }): string {
    const minAmount = this.getMinimumBidAmount(auction);

    if (auction.auction_type === "sealed") {
      return `Minimum bid: $${minAmount.toFixed(2)} (starting price)`;
    } else {
      return `Minimum bid: $${minAmount.toFixed(2)} (higher than current bid)`;
    }
  }
}
