// Re-export types from the centralized auction types
export {
  type AuctionModel,
  type AuctionItemModel,
  type AuctionItemImageModel,
  type CategoryModel,
  type VendorModel,
  type BidModel,
  type AuctionResponse,
  type SingleAuctionResponse,
  type CreateAuctionRequest,
  type PlaceBidRequest,
  type PlaceBidResponse,
} from "@/types/auction";

// Legacy exports for backward compatibility (deprecated)
/** @deprecated Use AuctionItemModel instead */
export type ProductModel = AuctionItemModel;
