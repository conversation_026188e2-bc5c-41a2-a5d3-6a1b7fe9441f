import { BidModel } from "@/modules/common/models/BidModel";

export interface ProductModel {
  id: number;
  name: string;
  description: string | null;
  image: string | null;
  images: {
    id: number;
    url: string;
    thumbnail_url: string;
    original_name: string;
    file_size: number | null;
    created_at: string;
  }[];
  created_at: string | null;
  updated_at: string | null;
}

export interface VendorModel {
  id: number;
  name: string;
  slug: string;
  email: string;
  logo: string | null;
  is_active: boolean;
}

export interface AuctionModel {
  id: number;
  start_time: string;
  end_time: string;
  starting_price: string;
  current_price: string;
  reserve_price: string;
  status: string;
  created_at: string;
  updated_at: string;
  product: ProductModel;
  vendor: VendorModel;
  bids?: BidModel[];
  is_active: boolean;
}

export interface AuctionResponse {
  data: AuctionModel[];
}

export interface SingleAuctionResponse {
  data: AuctionModel;
}
