"use client";
import Container from "@/modules/common/components/Container";
import GlobalButton from "@/modules/common/components/GlobalButton";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import QuantityComponent from "@/modules/common/components/QuantityComponent";
import { CartContext } from "@/modules/common/contexts/Store";
import ProductModel from "@/modules/products/models/ProductModel";
import { useContext, useState } from "react";

const ProductDetails = ({ data }: { data: ProductModel }) => {
  const { setData: setCart } = useContext(CartContext);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);

  const handleIncrease = () => setQuantity((prev) => prev + 1);
  const handleDecrease = () => setQuantity((prev) => (prev > 1 ? prev - 1 : 1));
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value, 10);
    setQuantity(isNaN(value) || value < 1 ? 1 : value);
  };

  const addToCart = async () => {
    setLoading(true); // Start loading
    try {
      // Simulate a delay for the operation (e.g., network request)
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setCart((prev: any) => {
        const existingProduct = prev.find((item: any) => item.id === data.id);
        if (existingProduct) {
          return prev.map((item: any) =>
            item.id === data.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        } else {
          return [...prev, { ...data, quantity }];
        }
      });
    } catch (error) {
      console.error("Failed to add to cart:", error);
    } finally {
      setLoading(false); // Stop loading
    }
  };
  return (
    <Container
      className="bg-white rounded-lg !p-4 shadow-sm min-h-[60vh]"
      data-aos="fade-in"
    >
      <div className="flex gap-4 xs:flex-col">
        <GlobalImage
          src={data.image}
          alt={data.title}
          width={400}
          height={400}
        />
        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-4xl"> {data.title}</h1>
            <span className="text-gray-400 text-xs">
              الاصدار الاحدث و الافضل حتى اليوم
            </span>
          </div>
          <span className="pt-4">SAR {data.price}</span>

          <p className="pt-4">{data.description}</p>
          <div className="flex gap-4 items-center w-full">
            <QuantityComponent
              handleChange={handleChange}
              handleDecrease={handleDecrease}
              handleIncrease={handleIncrease}
              quantity={quantity}
            />
            <div className="w-full">
              <GlobalButton onClick={addToCart} loading={loading}>
                إضافة للسلة
              </GlobalButton>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default ProductDetails;
