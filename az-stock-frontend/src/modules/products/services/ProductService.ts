import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import { _revalidate } from "@/utils";
import ProductModel from "@/modules/products/models/ProductModel";

export default class ProductService implements GlobalService {
  methods = {
    async getProduct({ id }: { id?: string | number }): Promise<ProductModel> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");
        const response = await GlobalFetchJson<ProductModel>(
          `/products/${id}`,
          {
            method: "GET",
            headers: headers,
          }
        );
        return response;
      } catch (error) {
        throw error;
      }
    },
    async getProducts({
      limit,
      category,
    }: {
      limit?: string;
      category?: string;
    }): Promise<ProductModel[]> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");
        const response = await GlobalFetchJson<ProductModel[]>(
          `/products${category ? `/category/${category}` : ""}${
            limit ? `?limit=${limit}` : ""
          }`,
          {
            method: "GET",
            headers: headers,
          }
        );
        return response;
      } catch (error) {
        throw error;
      }
    },
  };
}
