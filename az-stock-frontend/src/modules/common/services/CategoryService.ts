import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import { _revalidate } from "@/utils";
import {
  CategoryModel,
  CategoryResponse,
  SingleCategoryResponse,
} from "@/types/auction";

// Legacy interface for backward compatibility
/** @deprecated Use CategoryModel from @/types/auction instead */
interface Category extends CategoryModel {
  /** @deprecated Use auctions_count instead */
  products_count?: number;
}

export default class CategoryService implements GlobalService {
  methods = {
    async getCategories({
      search,
      has_auctions,
      per_page,
    }: {
      search?: string;
      has_auctions?: boolean;
      per_page?: number;
    } = {}): Promise<CategoryModel[]> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        let url = `/categories`;
        const queryParams = [];

        if (search) queryParams.push(`search=${encodeURIComponent(search)}`);
        if (has_auctions !== undefined) queryParams.push(`has_auctions=${has_auctions}`);
        if (per_page) queryParams.push(`per_page=${per_page}`);

        if (queryParams.length > 0) {
          url += `?${queryParams.join("&")}`;
        }

        console.log(`Fetching categories with URL: ${url}`);

        const response = await GlobalFetchJson<CategoryResponse>(url, {
          method: "GET",
          headers: headers,
          cache: "no-store",
        });

        console.log("Categories response:", response);

        if (!response.data || !Array.isArray(response.data)) {
          console.error("Invalid categories response format:", response);
          return [];
        }

        return response.data;
      } catch (error) {
        console.error("Error fetching categories:", error);
        return [];
      }
    },

    async getCategory(id: number): Promise<CategoryModel | null> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleCategoryResponse>(
          `/categories/${id}`,
          {
            method: "GET",
            headers: headers,
            cache: "no-store",
          }
        );

        return response.data || null;
      } catch (error) {
        console.error("Error fetching category:", error);
        return null;
      }
    },

    async createCategory(name: string): Promise<CategoryModel> {
      try {
        const { getSession } = await import("next-auth/react");
        const session = await getSession();

        if (!session || !session.accessToken) {
          throw new Error("You must be logged in to create a category");
        }

        const headers = new Headers();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", `Bearer ${session.accessToken}`);

        const response = await GlobalFetchJson<SingleCategoryResponse>(
          `/categories`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({ name }),
          }
        );

        if (!response || !response.data) {
          throw new Error("Invalid response format from category creation API");
        }

        return response.data;
      } catch (error: any) {
        console.error("Error creating category:", error);
        if (error.message) {
          throw new Error(error.message);
        } else {
          throw new Error("Failed to create category. Please try again.");
        }
      }
    },
  };
}
