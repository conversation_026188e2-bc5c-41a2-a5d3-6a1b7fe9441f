"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import Container from "../Container";
import Image from "next/image";
import { useSession } from "next-auth/react";

export default function Header() {
  const { status } = useSession();
  const isAuthenticated = status === "authenticated";
  const [showBuyerDropdown, setShowBuyerDropdown] = useState(false);
  const [showSellerDropdown, setShowSellerDropdown] = useState(false);

  return (
    <Container className="flex justify-between items-center gap-2 ">
      <div className="flex">
        {/* Replace this with your actual logo component or img */}
        <Link href={"/"} className="w-28 h-auto">
          <Image
            width={100}
            height={100}
            src="/logo.png"
            alt="Logo"
            className="w-full h-auto"
          />
        </Link>
      </div>

      <nav className="flex items-center justify-end font-semibold gap-5">
        <Link href="/auctions">Shop All Auctions</Link>

        {/* Buyers Dropdown */}
        <div
          className="relative"
          onMouseEnter={() => setShowBuyerDropdown(true)}
          onMouseLeave={() => setShowBuyerDropdown(false)}
        >
          <div tabIndex={0} role="button" className="m-1 cursor-pointer">
            For Buyers
          </div>
          {showBuyerDropdown && (
            <ul className="absolute dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
              <li>
                <Link href="/how-it-works">How it works</Link>
              </li>
              <li>
                <Link href="/buyer-resource-center">Buyer resource center</Link>
              </li>
            </ul>
          )}
        </div>

        {/* Sellers Dropdown */}
        <div
          className="relative"
          onMouseEnter={() => setShowSellerDropdown(true)}
          onMouseLeave={() => setShowSellerDropdown(false)}
        >
          <div tabIndex={0} role="button" className="m-1 cursor-pointer">
            For Sellers
          </div>
          {showSellerDropdown && (
            <ul className="absolute dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
              <li>
                <Link href="/sellers">Want to sell with us?</Link>
              </li>
              <li>
                <Link href="/sellers/enterprise">Enterprise seller</Link>
              </li>
            </ul>
          )}
        </div>

        {isAuthenticated ? (
          <Link href="/dashboard">Dashboard</Link>
        ) : (
          <>
            <Link href="/login">Log in</Link>
            <Link href="/register">Register</Link>
          </>
        )}
      </nav>
    </Container>
  );
}
