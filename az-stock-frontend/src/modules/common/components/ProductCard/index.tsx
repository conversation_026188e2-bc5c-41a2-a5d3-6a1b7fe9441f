"use client";
import Link from "next/link";
import GlobalImage from "../GlobalImage/GlobalImage";
import GlobalButton from "../GlobalButton";
import { useContext, useState } from "react";
import { CartContext } from "../../contexts/Store";
import ProductModel from "../../../products/models/ProductModel";
import { AuctionModel } from "@/modules/auctions/models/AuctionModel";

const ProductCard = ({ data }: { data: AuctionModel }) => {
  const { setData: setCart } = useContext(CartContext);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);

  const addToCart = async () => {
    setLoading(true); // Start loading
    try {
      // Simulate a delay for the operation (e.g., network request)
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setCart((prev: any) => {
        const existingProduct = prev.find((item: any) => item.id === data.id);
        if (existingProduct) {
          return prev.map((item: any) =>
            item.id === data.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        } else {
          return [...prev, { ...data, quantity }];
        }
      });
    } catch (error) {
      console.error("Failed to add to cart:", error);
    } finally {
      setLoading(false); // Stop loading
    }
  };
  return (
    <div className="bg-white  flex flex-col justify-between gap-2 items-center text-center p-2 shadow rounded-lg">
      <div className="flex flex-col items-center gap-2">
        {" "}
        <Link href={`/products/${data.id}`}>
          {" "}
          {/* <GlobalImage
            src={data.image}
            alt={data.title}
            width={400}
            height={400}
            className="rounded-lg h-44 xs:h-20 w-full  object-cover"
          /> */}
        </Link>
        <Link href={`/products/${data.id}`}>
          <h4 className="text-text-primary">{data.product.name}</h4>
        </Link>
        <span className="truncate overflow-hidden xs:w-24  text-xs w-52">
          {data.product.description}
        </span>
      </div>
      <div className="w-full flex flex-col gap-2">
        {" "}
        {/* <span className="self-start font-semibold">SAR {data.}</span> */}
        <GlobalButton
          className="bg-background-primary text-white btn w-full"
          onClick={addToCart}
          loading={loading}
        >
          أضف للسلة
        </GlobalButton>
      </div>
    </div>
  );
};
export default ProductCard;
