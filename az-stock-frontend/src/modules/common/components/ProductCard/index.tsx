"use client";
import Link from "next/link";
import GlobalImage from "../GlobalImage/GlobalImage";
import GlobalButton from "../GlobalButton";
import { useState } from "react";
import { AuctionModel } from "@/types/auction";
import { formatDistanceToNow } from "date-fns";

// Renamed from ProductCard to AuctionCard to reflect new functionality
const AuctionCard = ({ data }: { data: AuctionModel }) => {
  const [isInWatchlist, setIsInWatchlist] = useState(false);
  const [loading, setLoading] = useState(false);

  const addToWatchlist = async () => {
    setLoading(true);
    try {
      // TODO: Implement watchlist functionality
      // This would call the WatchlistService
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsInWatchlist(!isInWatchlist);
    } catch (error) {
      console.error("Failed to update watchlist:", error);
    } finally {
      setLoading(false);
    }
  };

  const isAuctionActive =
    new Date(data.end_time) > new Date() && data.status === "active";
  return (
    <div className="bg-white flex flex-col justify-between gap-2 items-center text-center p-4 shadow rounded-lg hover:shadow-lg transition-shadow">
      <div className="flex flex-col items-center gap-2 w-full">
        <Link href={`/auctions/${data.id}`} className="w-full">
          <div className="relative h-44 w-full bg-gray-200 rounded-lg overflow-hidden">
            {data.auction_items &&
            data.auction_items.length > 0 &&
            data.auction_items[0].images &&
            data.auction_items[0].images.length > 0 ? (
              <GlobalImage
                src={data.auction_items[0].images[0].url}
                alt={data.auction_items[0].item_name}
                width={400}
                height={400}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                {data.title || "No Image"}
              </div>
            )}
            <div className="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 text-xs font-bold rounded">
              {data.status.toUpperCase()}
            </div>
            {data.auction_items && data.auction_items.length > 1 && (
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 text-xs rounded">
                {data.auction_items.length} items
              </div>
            )}
          </div>
        </Link>

        <Link href={`/auctions/${data.id}`} className="w-full">
          <h4 className="text-text-primary font-semibold truncate">
            {data.title}
          </h4>
        </Link>

        <span className="truncate overflow-hidden text-xs w-full text-gray-600">
          {data.description || "No description"}
        </span>

        <div className="w-full text-sm">
          <div className="flex justify-between items-center mb-1">
            <span className="text-gray-600">Current Bid:</span>
            <span className="font-bold text-green-600">
              ${data.current_price}
            </span>
          </div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-gray-600">Vendor:</span>
            <span className="text-gray-800">{data.vendor.name}</span>
          </div>
          <div className="text-center text-xs text-gray-500">
            {isAuctionActive ? (
              <span>
                Ends{" "}
                {formatDistanceToNow(new Date(data.end_time), {
                  addSuffix: true,
                })}
              </span>
            ) : (
              <span className="text-red-500">Auction ended</span>
            )}
          </div>
        </div>
      </div>

      <div className="w-full flex flex-col gap-2">
        <GlobalButton
          className={`w-full ${
            isInWatchlist
              ? "bg-red-500 hover:bg-red-600"
              : "bg-blue-500 hover:bg-blue-600"
          } text-white`}
          onClick={addToWatchlist}
          loading={loading}
        >
          {isInWatchlist ? "Remove from Watchlist" : "Add to Watchlist"}
        </GlobalButton>

        <Link href={`/auctions/${data.id}`} className="w-full">
          <GlobalButton className="bg-green-600 hover:bg-green-700 text-white w-full">
            {isAuctionActive ? "Place Bid" : "View Auction"}
          </GlobalButton>
        </Link>
      </div>
    </div>
  );
};

// Export as both AuctionCard and ProductCard for backward compatibility
export default AuctionCard;
export { AuctionCard as ProductCard };
