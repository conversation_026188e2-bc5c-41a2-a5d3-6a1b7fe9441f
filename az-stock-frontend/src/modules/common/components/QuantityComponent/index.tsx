"use client";
import React from "react";

const QuantityComponent = ({
  quantity,
  handleIncrease,
  handleDecrease,
  handleChange,
}: {
  quantity: number;
  handleIncrease: () => void;
  handleDecrease: () => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => {
  return (
    <div className="flex items-center justify-center border rounded">
      <button
        className="px-4 py-1 text-gray-500 rounded "
        onClick={handleDecrease}
      >
        -
      </button>
      <input
        type="text"
        className="w-8 text-center rounded focus:outline-none appearance-none bg-transparent"
        value={quantity ?? 0}
        onChange={handleChange}
      />
      <button
        className="px-4 py-1 text-gray-500 rounded"
        onClick={handleIncrease}
      >
        +
      </button>
    </div>
  );
};

export default QuantityComponent;
