import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import { _revalidate } from "@/utils";
import CartModel from "../models/CartModel";

export default class CartService implements GlobalService {
  methods = {
    async getCart({ id }: { id?: string }): Promise<CartModel> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");
        const response = await GlobalFetchJson<CartModel>(`/carts/${id}`, {
          method: "GET",
          headers: headers,
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    async getUserCarts({ id }: { id: number }): Promise<CartModel[]> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");
        const response = await GlobalFetchJson<CartModel[]>(
          `/carts/user/${id}`,
          {
            method: "GET",
            headers: headers,
          }
        );
        return response;
      } catch (error) {
        throw error;
      }
    },
  };
}
