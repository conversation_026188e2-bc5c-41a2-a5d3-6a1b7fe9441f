"use client";
import { useContext } from "react";
import CartModel from "../../models/CartModel";
import { CartContext, CartItem } from "@/modules/common/contexts/Store";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import Link from "next/link";
import QuantityComponent from "@/modules/common/components/QuantityComponent";
import { BiTrash } from "react-icons/bi";
import GlobalButton from "@/modules/common/components/GlobalButton";

const CartDetails = () => {
  const { data: cart, setData: setCart, totalPrice } = useContext(CartContext);

  const handleIncrease = (id: number) => {
    setCart((prev: CartItem[]) =>
      prev.map((item) =>
        item.id === id ? { ...item, quantity: item.quantity + 1 } : item
      )
    );
  };

  const handleDecrease = (id: number, quantity: number) => {
    if (quantity === 1) {
      handleRemove(id);
    } else {
      setCart((prev: CartItem[]) =>
        prev.map((item) =>
          item.id === id ? { ...item, quantity: item.quantity - 1 } : item
        )
      );
    }
  };

  const handleRemove = (id: number) => {
    setCart((prev: CartItem[]) => prev.filter((item) => item.id !== id));
  };

  return (
    <div className="flex flex-col gap-4 h-full justify-between min-h-[60vh]">
      <div className="flex flex-col gap-4">
        {!cart[0] ? (
          <div>السلة فارغة</div>
        ) : (
          cart.map((d) => (
            <div
              key={d.id}
              className="flex xs:flex-col gap-4 justify-between p-3 hover:bg-gray-50 rounded"
            >
              <Link href={`/products/${d.id}`} className="flex gap-4">
                <GlobalImage
                  src={d.image}
                  alt={d.title}
                  className="object-cover w-10 h-10 rounded"
                  width={50}
                  height={50}
                />
                <div>
                  <h5>{d.title}</h5>
                  <span className="flex gap-2">
                    <span>x{d.quantity}</span>
                    <span>SAR {d.price}</span>
                  </span>
                </div>
              </Link>

              <div className="flex gap-4 items-center">
                <QuantityComponent
                  handleChange={(e) => {
                    const value = parseInt(e.target.value, 10);
                    if (!isNaN(value) && value > 0) {
                      setCart((prev: CartItem[]) =>
                        prev.map((item) =>
                          item.id === d.id ? { ...item, quantity: value } : item
                        )
                      );
                    }
                  }}
                  handleDecrease={() => handleDecrease(d.id, d.quantity)}
                  handleIncrease={() => handleIncrease(d.id)}
                  quantity={d.quantity}
                />
                <button
                  className="p-2 border border-red-500 rounded-full"
                  onClick={() => handleRemove(d.id)}
                >
                  <BiTrash color="red" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
      <div className="justify-self-end ">
        <div className="mt-4 p-4 border-t flex justify-between font-bold text-xl ">
          <h3>اجمالي السلة</h3>
          <span className=" font-semibold">SAR {totalPrice}</span>
        </div>
        <GlobalButton>اتمام عملية الدفع</GlobalButton>
      </div>
    </div>
  );
};

export default CartDetails;
