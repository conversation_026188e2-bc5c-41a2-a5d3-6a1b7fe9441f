import Container from "@/modules/common/components/Container";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";

const Page = () => {
  return (
    <div className=" flex flex-col items-center overflow-hidden">
      <Container className="pb-20 flex flex-col justify-center gap-2 h-[80vh]">
        <div>
          <span>How to Buy Returns, Overstock, and Liquidation at Auction</span>
          <h1 className="text-4xl text-gray-900 font-bold">
            Welcome to AZ-Stock
          </h1>
        </div>
        <span className="max-w-xl text-black pt-4">
          At any given time there are thousands of listings open for bidding
          across dozens of liquidation marketplaces.
        </span>
        <GlobalImage
          src="/main.png"
          alt="Main"
          width={500}
          height={400}
          className="absolute right-0 -z-10"
        />
      </Container>

      <div className="flex flex-col items-center justify-center">
        <span className="text-blue-950 font-bold uppercase text-sm">
          Begin bidding on returns and overstock auctions
        </span>
        <h2 className="text-5xl text-center py-4 font-bold">
          Here’s what you need to
          <br /> know to start buying
        </h2>

        <div className="flex flex-col md:flex-row gap-4 items-center max-w-5xl pt-10">
          <div className="md:w-1/3 flex justify-center">
            <GlobalImage src="/flag.jpg" alt="Flag" width={300} height={200} />
          </div>
          <div className="md:w-2/3">
            <p className="text-2xl font-bold">
              Think of AZ-Stock as a starting point to help you find and view
              the type of merchandise you're seeking.
            </p>
            <p>
              Once you find an auction lot or retailer you are interested in,
              click on it, and AZ-Stock will take you to that marketplace where
              you will register to bid.
            </p>
          </div>
        </div>
      </div>

      <div className=" block h-[450px]">
        <div className="bg-gray-100 w-screen absolute left-0 my-20 flex justify-center">
          <div className="flex flex-col md:flex-row gap-4 items-center py-10 max-w-4xl">
            <div className="md:w-2/3">
              <p className="text-2xl font-bold">
                Think of B-Stock as a starting point to help you find and view
                the type of merchandise you're seeking.
              </p>
              <p>
                On each marketplace in B-Stock's network, you are buying
                directly from that retailer. Feel free to register on as many
                marketplaces as you like!
              </p>
            </div>
            <div className="md:w-1/3 flex justify-center">
              <GlobalImage
                src="/laptop-hands.png"
                alt="Laptop Hands"
                width={300}
                height={200}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col items-center justify-center py-10">
        <span className="text-blue-950 font-bold uppercase text-sm">
          Buying from our liquidation marketplaces
        </span>
        <h2 className="text-5xl text-center py-4 font-bold">
          Here’s How AZ-Stock Works
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-10 pt-8 max-w-6xl">
          {[
            {
              step: "Step 1",
              title: "Register",
              desc: "See one or more marketplaces you like? Apply to bid on that marketplace - be sure to have your resale certificate handy!",
            },
            {
              step: "Step 2",
              title: "Get Approved",
              desc: "We'll notify you as soon as your marketplace registration is approved. From there, you'll be up and running.",
            },
            {
              step: "Step 3",
              title: "Browse",
              desc: "View live auctions from top retailers and brands, across dozens of categories, conditions, and price points.",
            },
            {
              step: "Step 4",
              title: "Begin Bidding",
              desc: "See an auction you like? Submit your price! Our proxy bidding system will automatically bid on your behalf.",
            },
          ].map(({ step, title, desc }, idx) => (
            <div key={idx} className="border p-4 rounded-xl">
              <span className="text-xs">{step}</span>
              <h3 className="font-bold text-xl">{title}</h3>
              <p className="pt-3">{desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
export default Page;
