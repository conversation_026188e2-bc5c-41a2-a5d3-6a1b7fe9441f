import Loading from "@/app/loading";
import CartDetails from "@/modules/cart/components/CartDetails";
import Container from "@/modules/common/components/Container";
import { Metadata } from "next";
import { Suspense } from "react";
export async function generateMetadata(): Promise<Metadata> {
  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_URL!),
    title: "السلة - متجر تجريبي",

    openGraph: {
      url: `${process.env.NEXT_PUBLIC_URL!}/cart`,
    },
    alternates: {
      canonical: "/cart",
    },
  };
}
const Cart = async () => {
  return (
    <Container
      className="bg-white rounded-lg !p-4 shadow-sm min-h-[60vh] mb-10"
      data-aos="fade-in"
    >
      <h1 className="text-xl pb-5">سلة المشتريات</h1>
      <CartDetails />
    </Container>
  );
};
const Page = async () => {
  return (
    <Suspense fallback={<Loading />}>
      <Cart />
    </Suspense>
  );
};
export default Page;
