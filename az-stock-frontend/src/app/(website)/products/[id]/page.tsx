import Loading from "@/app/loading";
import useGlobalService from "@/core/hook/useGlobalService";
import ProductDetails from "@/modules/products/components/ProductDetails";
import ProductService from "@/modules/products/services/ProductService";
import { Metadata } from "next";
import { Suspense } from "react";

const productService = useGlobalService(ProductService);

export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const data = await productService.methods.getProduct({ id: params.id });

  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_URL!),
    title: `${data.title} - متجر تجريبي`,
    description: `${data.description}`,
    openGraph: {
      url: `${process.env.NEXT_PUBLIC_URL!}/products/${data.id}`,
      description: `${data.description}`,
    },
    alternates: {
      canonical: `/products/${data.id}`,
    },
  };
}
const Product = async ({ params }: { params: { id: string } }) => {
  const data = await productService.methods.getProduct({ id: params.id });
  return <ProductDetails data={data} />;
};

const Page = ({ params }: { params: { id: string } }) => {
  return (
    <Suspense fallback={<Loading />}>
      <Product params={params} />
    </Suspense>
  );
};
export default Page;
