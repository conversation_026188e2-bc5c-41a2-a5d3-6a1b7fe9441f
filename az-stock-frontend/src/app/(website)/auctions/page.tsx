"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Container from "@/modules/common/components/Container";
import useGlobalService from "@/core/hook/useGlobalService";
import AuctionService from "@/modules/auctions/services/AuctionService";
import CategoryService from "@/modules/common/services/CategoryService";
import { adminService } from "@/modules/dashboard/services/AdminService";
import {
  CategoryModel,
  AuctionFilters,
  PaginationMeta,
  UserModel,
} from "@/modules/dashboard/models/DashboardModels";
import { AuctionModel } from "@/modules/auctions/models/AuctionModel";

const PublicAuctionsPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const auctionService = useGlobalService(AuctionService);
  const categoryService = useGlobalService(CategoryService);

  const [auctions, setAuctions] = useState<AuctionModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationMeta | null>(null);
  const [filters, setFilters] = useState<AuctionFilters>({
    per_page: 12,
    page: 1,
    status: "active",
  });

  useEffect(() => {
    // Initialize filters from URL search params
    if (searchParams) {
      const initialFilters: AuctionFilters = {
        per_page: 12,
        page: parseInt(searchParams.get("page") || "1"),
        status: (searchParams.get("status") as any) || "active",
        search: searchParams.get("search") || undefined,
        category_id: searchParams.get("category_id")
          ? parseInt(searchParams.get("category_id")!)
          : undefined,
        vendor_id: searchParams.get("vendor_id")
          ? parseInt(searchParams.get("vendor_id")!)
          : undefined,
        ending_soon: searchParams.get("ending_soon")
          ? searchParams.get("ending_soon") === "true"
          : undefined,
        min_price: searchParams.get("min_price")
          ? parseFloat(searchParams.get("min_price")!)
          : undefined,
        max_price: searchParams.get("max_price")
          ? parseFloat(searchParams.get("max_price")!)
          : undefined,
      };
      setFilters(initialFilters);
    }
  }, [searchParams]);

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Use the service layer for data fetching
      const [auctionsData, categoriesData] = await Promise.all([
        auctionService.methods.getAuctions({
          per_page: filters.per_page,
          category_id: filters.category_id,
          status: filters.status,
        }),
        categoryService.methods.getCategories(),
      ]);

      // For now, we'll get all auctions and do client-side filtering
      // This is a limitation of the current AuctionService
      let filteredAuctions = auctionsData || [];

      // Apply client-side filtering
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredAuctions = filteredAuctions.filter(
          (auction) =>
            auction.title?.toLowerCase().includes(searchLower) ||
            auction.description?.toLowerCase().includes(searchLower) ||
            auction.auction_items?.some(
              (item) =>
                item.item_name?.toLowerCase().includes(searchLower) ||
                item.item_description?.toLowerCase().includes(searchLower)
            )
        );
      }

      if (filters.status) {
        filteredAuctions = filteredAuctions.filter(
          (auction) => auction.status === filters.status
        );
      }

      if (filters.vendor_id) {
        filteredAuctions = filteredAuctions.filter(
          (auction) => auction.vendor?.id === filters.vendor_id
        );
      }

      if (filters.min_price) {
        filteredAuctions = filteredAuctions.filter((auction) => {
          const currentPrice =
            parseFloat(auction.current_price) ||
            parseFloat(auction.starting_price) ||
            0;
          return currentPrice >= filters.min_price!;
        });
      }

      if (filters.max_price) {
        filteredAuctions = filteredAuctions.filter((auction) => {
          const currentPrice =
            parseFloat(auction.current_price) ||
            parseFloat(auction.starting_price) ||
            0;
          return currentPrice <= filters.max_price!;
        });
      }

      if (filters.ending_soon) {
        const now = new Date();
        const twentyFourHoursFromNow = new Date(
          now.getTime() + 24 * 60 * 60 * 1000
        );
        filteredAuctions = filteredAuctions.filter((auction) => {
          const endTime = new Date(auction.end_time);
          return endTime <= twentyFourHoursFromNow && endTime > now;
        });
      }

      setAuctions(filteredAuctions);
      setCategories(categoriesData || []);

      // Create simple pagination for client-side filtering
      const totalItems = filteredAuctions.length;
      const perPage = filters.per_page || 12;
      const currentPage = filters.page || 1;
      const totalPages = Math.ceil(totalItems / perPage);

      setPagination({
        current_page: currentPage,
        from: (currentPage - 1) * perPage + 1,
        last_page: totalPages,
        links: [],
        path: "/auctions",
        per_page: perPage,
        to: Math.min(currentPage * perPage, totalItems),
        total: totalItems,
      });
    } catch (error) {
      console.error("Error loading auctions:", error);
      // Set empty arrays on error to prevent crashes
      setAuctions([]);
      setCategories([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  const updateFilters = (newFilters: Partial<AuctionFilters>) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 };
    setFilters(updatedFilters);

    // Update URL
    const queryParams = new URLSearchParams();
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        queryParams.append(key, value.toString());
      }
    });

    router.push(`/auctions?${queryParams.toString()}`, { scroll: false });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getTimeRemaining = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-100 text-green-800",
      ended: "bg-red-100 text-red-800",
      upcoming: "bg-yellow-100 text-yellow-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status as keyof typeof statusColors] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <Container className="py-8">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Browse Auctions
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover amazing items and place your bids on active auctions
          </p>
        </div>

        {/* Advanced Filters */}
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Filter Auctions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Search
              </label>
              <input
                type="text"
                value={filters.search || ""}
                onChange={(e) => updateFilters({ search: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Search auctions..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  updateFilters({ status: e.target.value as any })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="upcoming">Upcoming</option>
                <option value="ended">Ended</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                value={filters.category_id || ""}
                onChange={(e) =>
                  updateFilters({
                    category_id: e.target.value
                      ? parseInt(e.target.value)
                      : undefined,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Ending Soon
              </label>
              <select
                value={filters.ending_soon?.toString() || ""}
                onChange={(e) =>
                  updateFilters({
                    ending_soon: e.target.value
                      ? e.target.value === "true"
                      : undefined,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Auctions</option>
                <option value="true">Ending Soon</option>
                <option value="false">Not Ending Soon</option>
              </select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Min Price
              </label>
              <input
                type="number"
                value={filters.min_price || ""}
                onChange={(e) =>
                  updateFilters({
                    min_price: e.target.value
                      ? parseFloat(e.target.value)
                      : undefined,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="0.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Max Price
              </label>
              <input
                type="number"
                value={filters.max_price || ""}
                onChange={(e) =>
                  updateFilters({
                    max_price: e.target.value
                      ? parseFloat(e.target.value)
                      : undefined,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="1000.00"
              />
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <p className="mt-2 text-gray-600">Loading auctions...</p>
          </div>
        )}

        {/* Auctions Grid */}
        {!loading && (
          <>
            {auctions.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {auctions.map((auction) => (
                  <div
                    key={auction.id}
                    className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
                  >
                    {/* Auction Image */}
                    {auction.auction_items &&
                      auction.auction_items.length > 0 &&
                      auction.auction_items[0].images &&
                      auction.auction_items[0].images.length > 0 && (
                        <div className="aspect-w-16 aspect-h-9">
                          <img
                            src={auction.auction_items[0].images[0].url}
                            alt={auction.auction_items[0].item_name}
                            className="w-full h-48 object-cover"
                          />
                        </div>
                      )}

                    {/* Auction Content */}
                    <div className="p-6">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {auction.title}
                        </h3>
                        {getStatusBadge(auction.status)}
                      </div>

                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {auction.description || "No description available"}
                      </p>

                      {/* Show auction items */}
                      {auction.auction_items &&
                        auction.auction_items.length > 0 && (
                          <div className="mb-3">
                            <p className="text-xs text-gray-500 mb-1">
                              Items in this auction:
                            </p>
                            <div className="space-y-1">
                              {auction.auction_items.slice(0, 2).map((item) => (
                                <p
                                  key={item.id}
                                  className="text-xs text-gray-700 truncate"
                                >
                                  • {item.item_name}
                                </p>
                              ))}
                              {auction.auction_items.length > 2 && (
                                <p className="text-xs text-gray-500">
                                  +{auction.auction_items.length - 2} more items
                                </p>
                              )}
                            </div>
                          </div>
                        )}

                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            Current Bid:
                          </span>
                          <span className="text-lg font-bold text-green-600">
                            {formatCurrency(
                              parseFloat(auction.current_price) ||
                                parseFloat(auction.starting_price) ||
                                0
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            Starting Bid:
                          </span>
                          <span className="text-sm text-gray-700">
                            {formatCurrency(
                              parseFloat(auction.starting_price) || 0
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">Bids:</span>
                          <span className="text-sm text-gray-700">
                            {auction.bids?.length || 0}
                          </span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mb-4">
                        <span className="text-sm text-gray-500">
                          Time Remaining:
                        </span>
                        <span
                          className={`text-sm font-medium ${
                            getTimeRemaining(auction.end_time) === "Ended"
                              ? "text-red-600"
                              : "text-blue-600"
                          }`}
                        >
                          {getTimeRemaining(auction.end_time)}
                        </span>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">
                          by {auction.vendor?.name || "Unknown Vendor"}
                        </span>
                        <button
                          onClick={() => router.push(`/auctions/${auction.id}`)}
                          className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition-colors duration-200"
                        >
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="mx-auto h-12 w-12 text-gray-400">
                  <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 className="mt-2 text-lg font-medium text-gray-900">
                  No auctions found
                </h3>
                <p className="mt-1 text-gray-500">
                  Try adjusting your filters or check back later for new
                  auctions.
                </p>
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.last_page > 1 && (
              <div className="flex justify-center items-center space-x-2 mt-8">
                <button
                  onClick={() =>
                    updateFilters({
                      page: Math.max(1, (filters.page || 1) - 1),
                    })
                  }
                  disabled={(filters.page || 1) <= 1}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                <div className="flex space-x-1">
                  {Array.from(
                    { length: Math.min(5, pagination.last_page) },
                    (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => updateFilters({ page })}
                          className={`px-3 py-2 text-sm font-medium rounded-md ${
                            filters.page === page
                              ? "bg-indigo-600 text-white"
                              : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
                          }`}
                        >
                          {page}
                        </button>
                      );
                    }
                  )}
                </div>

                <button
                  onClick={() =>
                    updateFilters({
                      page: Math.min(
                        pagination.last_page,
                        (filters.page || 1) + 1
                      ),
                    })
                  }
                  disabled={(filters.page || 1) >= pagination.last_page}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </Container>
  );
};

export default PublicAuctionsPage;
