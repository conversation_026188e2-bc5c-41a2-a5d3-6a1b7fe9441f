"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import Modal from "@/modules/dashboard/components/shared/Modal";
import ImageUpload from "@/modules/dashboard/components/shared/ImageUpload";
import FeaturedImageUpload from "@/modules/dashboard/components/shared/FeaturedImageUpload";
import ProductImageGallery from "@/modules/dashboard/components/shared/ProductImageGallery";
import VendorService from "@/modules/dashboard/services/VendorService";

const vendorService = new VendorService();
import {
  ProductModel,
  CategoryModel,
  CreateProductForm,
} from "@/modules/dashboard/models/DashboardModels";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";

const VendorProductsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [products, setProducts] = useState<ProductModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState({
    status: undefined as string | undefined,
    per_page: 15,
    page: 1,
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<ProductModel | null>(
    null
  );
  const [productForm, setProductForm] = useState<CreateProductForm>({
    name: "",
    description: "",
    category_id: 0,
    featuredImage: undefined,
    images: [],
  });
  const [showImageModal, setShowImageModal] = useState(false);
  const queryClient = useQueryClient();

  // Use React Query for product images
  const {
    data: productImages,
    isLoading: imagesLoading,
    error: imagesError,
  } = useQuery({
    queryKey: ["productImages", selectedProduct?.id],
    queryFn: async () => {
      try {
        const response = await vendorService.methods.getProductImages(
          selectedProduct!.id
        );
        return response.images || []; // Return the images array from the response
      } catch (error) {
        console.error("Error fetching product images:", error);
        return []; // Return empty array on error
      }
    },
    enabled: !!selectedProduct?.id && (showEditModal || showImageModal),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
  console.log(productImages);

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("vendor")) {
      router.push("/");
      return;
    }

    loadData();
  }, [session, status, router, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsResponse, categoriesResponse] = await Promise.all([
        vendorService.methods.getMyProducts(filters),
        vendorService.methods.getCategories(),
      ]);
      console.log(categoriesResponse);
      setProducts(productsResponse.data);
      setPagination(productsResponse.meta);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error("Error loading products:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProduct = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that at least one image is provided
    if (
      !productForm.featuredImage &&
      (!productForm.images || productForm.images.length === 0)
    ) {
      alert(
        "Please provide at least one image (either featured image or gallery images)."
      );
      return;
    }

    try {
      await vendorService.methods.createProduct(productForm);
      setShowCreateModal(false);
      resetForm();
      loadData();
    } catch (error) {
      console.error("Error creating product:", error);
      alert("Failed to create product. Please try again.");
    }
  };

  const handleUpdateProduct = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProduct) return;

    try {
      await vendorService.methods.updateProduct(
        selectedProduct.id,
        productForm
      );
      setShowEditModal(false);
      setSelectedProduct(null);
      resetForm();
      loadData();
    } catch (error) {
      console.error("Error updating product:", error);
      alert("Failed to update product. Please try again.");
    }
  };

  const handleDeleteProduct = async (productId: number) => {
    if (!confirm("Are you sure you want to delete this product?")) return;

    try {
      await vendorService.methods.deleteProduct(productId);
      loadData();
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("Failed to delete product. Please try again.");
    }
  };

  const openEditModal = (product: ProductModel) => {
    setSelectedProduct(product);
    setProductForm({
      name: product.name,
      description: product.description || "",
      category_id: product.category_id, // This will now properly set the selected category
      featuredImage: undefined,
      images: [],
    });

    setShowEditModal(true);
    // React Query will automatically fetch images when the modal opens
  };

  const resetForm = () => {
    setProductForm({
      name: "",
      description: "",
      category_id: 0,
      featuredImage: undefined,
      images: [],
    });
  };

  const handleImageChange = (images: File[]) => {
    setProductForm({ ...productForm, images });
  };

  const handleFeaturedImageChange = (image: File | null) => {
    setProductForm({ ...productForm, featuredImage: image || undefined });
  };

  const handleDeleteProductImage = async (imageId: number) => {
    if (!confirm("Are you sure you want to delete this image?")) return;

    try {
      await vendorService.methods.deleteProductImage(imageId);
      // Invalidate and refetch the images query
      await queryClient.invalidateQueries({
        queryKey: ["productImages", selectedProduct?.id],
      });
      // Also reload the products list to update the table display
      await loadData();
    } catch (error) {
      console.error("Error deleting image:", error);
      alert("Failed to delete image. Please try again.");
    }
  };

  const handleUploadProductImages = async (
    images: File[],
    replaceAll: boolean = false
  ) => {
    if (!selectedProduct) return;

    try {
      // Use the new workflow: upload images first, then update product
      await vendorService.methods.updateProduct(
        selectedProduct.id,
        { images },
        replaceAll
      );
      // Invalidate and refetch the images query
      await queryClient.invalidateQueries({
        queryKey: ["productImages", selectedProduct?.id],
      });
    } catch (error) {
      console.error("Error uploading images:", error);
      throw error;
    }
  };

  const openImageModal = (product: ProductModel) => {
    setSelectedProduct(product);
    setShowImageModal(true);
    // React Query will automatically fetch images when the modal opens
  };

  const columns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "product",
      header: "Product",
      render: (product: ProductModel) => {
        const featuredImage = product.featured_image_url || product.image;
        const galleryCount = product.images?.length || 0;

        return (
          <div className="flex items-center">
            <div className="relative mr-3">
              {featuredImage ? (
                <GlobalImage
                  src={featuredImage}
                  alt={product.name}
                  width={20}
                  height={20}
                  className="h-12 w-12 rounded-lg object-cover"
                />
              ) : (
                <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              )}
              {featuredImage && (
                <div className="absolute -top-1 -right-1 bg-indigo-600 text-white text-xs px-1 rounded">
                  ★
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="font-medium text-gray-900">{product.name}</div>
              <div className="text-sm text-gray-500 max-w-xs truncate">
                {product.description}
              </div>
              {galleryCount > 0 && (
                <div className="text-xs text-indigo-600 mt-1">
                  +{galleryCount} gallery image{galleryCount !== 1 ? "s" : ""}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      key: "category",
      header: "Category",
      render: (product: ProductModel) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {product.category?.name || "Uncategorized"}
        </span>
      ),
    },
    {
      key: "created_at",
      header: "Created",
      render: (product: ProductModel) =>
        product.created_at
          ? new Date(product.created_at).toLocaleDateString()
          : "-",
    },
    {
      key: "updated_at",
      header: "Updated",
      render: (product: ProductModel) =>
        product.updated_at
          ? new Date(product.updated_at).toLocaleDateString()
          : "-",
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("vendor")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="vendor">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Products</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your product inventory
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Add Product
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    status: e.target.value || undefined,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Products</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Per Page
              </label>
              <select
                value={filters.per_page}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    per_page: parseInt(e.target.value),
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Products Table */}
        <DataTable
          data={products}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onPageChange={(page) => setFilters({ ...filters, page })}
          actions={(product) => (
            <div className="flex space-x-2">
              <button
                onClick={() => openEditModal(product)}
                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                Edit
              </button>
              <button
                onClick={() => openImageModal(product)}
                className="text-purple-600 hover:text-purple-900 text-sm font-medium"
              >
                Images
              </button>
              <button
                onClick={() =>
                  router.push(
                    `/dashboard/vendor/auctions?product_id=${product.id}`
                  )
                }
                className="text-green-600 hover:text-green-900 text-sm font-medium"
              >
                Create Auction
              </button>
              <button
                onClick={() => handleDeleteProduct(product.id)}
                className="text-red-600 hover:text-red-900 text-sm font-medium"
              >
                Delete
              </button>
            </div>
          )}
          emptyMessage="No products found. Create your first product to get started."
        />

        {/* Create Product Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New Product"
          size="lg"
        >
          <form onSubmit={handleCreateProduct} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Product Name
              </label>
              <input
                type="text"
                required
                value={productForm.name}
                onChange={(e) =>
                  setProductForm({ ...productForm, name: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                rows={3}
                required
                value={productForm.description}
                onChange={(e) =>
                  setProductForm({
                    ...productForm,
                    description: e.target.value,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                required
                value={productForm.category_id}
                onChange={(e) =>
                  setProductForm({
                    ...productForm,
                    category_id: parseInt(e.target.value),
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={0}>Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              <FeaturedImageUpload
                image={productForm.featuredImage || null}
                onImageChange={handleFeaturedImageChange}
                maxSizeInMB={5}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Images (Gallery)
              </label>
              <ImageUpload
                images={productForm.images || []}
                onImagesChange={handleImageChange}
                maxImages={5}
                maxSizePerImage={5}
                showPreview={true}
              />
              <p className="text-xs text-gray-500 mt-1">
                Optional: Add more images to showcase your product from
                different angles
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Create Product
              </button>
            </div>
          </form>
        </Modal>

        {/* Edit Product Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title="Edit Product"
          size="lg"
        >
          <form onSubmit={handleUpdateProduct} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Product Name
              </label>
              <input
                type="text"
                required
                value={productForm.name}
                onChange={(e) =>
                  setProductForm({ ...productForm, name: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                rows={3}
                required
                value={productForm.description}
                onChange={(e) =>
                  setProductForm({
                    ...productForm,
                    description: e.target.value,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                required
                value={productForm.category_id}
                onChange={(e) =>
                  setProductForm({
                    ...productForm,
                    category_id: parseInt(e.target.value),
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={0}>Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              <FeaturedImageUpload
                image={productForm.featuredImage || null}
                currentImageUrl={
                  selectedProduct?.featured_image_url || selectedProduct?.image
                }
                onImageChange={handleFeaturedImageChange}
                maxSizeInMB={5}
              />
            </div>

            {/* Existing Gallery Images */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Gallery Images
              </label>
              {imagesLoading ? (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span className="ml-2 text-gray-600">
                      Loading images...
                    </span>
                  </div>
                </div>
              ) : imagesError ? (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">
                    Failed to load images. Please try again.
                  </p>
                </div>
              ) : productImages && productImages.length > 0 ? (
                <div>
                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3 p-4 bg-gray-50 rounded-lg">
                    {productImages.map((image) => (
                      <div key={image.id} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden bg-white border">
                          <img
                            src={image.url}
                            alt={image.original_name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => handleDeleteProductImage(image.id)}
                          className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <svg
                            className="h-3 w-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Click the × button to remove individual images
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-gray-50 rounded-lg text-center">
                  <p className="text-gray-500 text-sm">
                    No gallery images yet. Add some below!
                  </p>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add New Gallery Images (optional)
              </label>
              <ImageUpload
                images={productForm.images || []}
                onImagesChange={handleImageChange}
                maxImages={5}
                maxSizePerImage={5}
                showPreview={true}
              />
              <p className="text-xs text-gray-500 mt-1">
                These will be added to existing gallery images
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Update Product
              </button>
            </div>
          </form>
        </Modal>

        {/* Product Images Modal */}
        <Modal
          isOpen={showImageModal}
          onClose={() => setShowImageModal(false)}
          title={`Manage Images - ${selectedProduct?.name || ""}`}
          size="xl"
        >
          <div className="space-y-6">
            <ProductImageGallery
              images={productImages || []}
              onDeleteImage={handleDeleteProductImage}
              onUploadImages={handleUploadProductImages}
              showUpload={true}
            />
          </div>
        </Modal>
      </div>
    </DashboardLayout>
  );
};

export default VendorProductsPage;
