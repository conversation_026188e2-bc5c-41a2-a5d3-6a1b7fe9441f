"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import Modal from "@/modules/dashboard/components/shared/Modal";
import VendorService from "@/modules/dashboard/services/VendorService";

const vendorService = new VendorService();
import {
  AuctionModel,
  ProductModel,
  CreateAuctionForm,
} from "@/modules/dashboard/models/DashboardModels";

const VendorAuctionsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [auctions, setAuctions] = useState<AuctionModel[]>([]);
  const [products, setProducts] = useState<ProductModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState({
    status: undefined as string | undefined,
    per_page: 15,
    page: 1,
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState<AuctionModel | null>(
    null
  );
  const [auctionForm, setAuctionForm] = useState<CreateAuctionForm>({
    product_id: 0,
    start_time: "",
    end_time: "",
    starting_price: 0,
    reserve_price: 0,
  });

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("vendor")) {
      router.push("/");
      return;
    }

    loadData();
  }, [session, status, router, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [auctionsResponse, productsResponse] = await Promise.all([
        vendorService.methods.getMyAuctions(filters),
        vendorService.methods.getMyProducts({ per_page: 100 }),
      ]);

      setAuctions(auctionsResponse.data);
      setPagination(auctionsResponse.meta);
      setProducts(productsResponse.data);
    } catch (error) {
      console.error("Error loading auctions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAuction = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await vendorService.methods.createAuction(auctionForm);
      setShowCreateModal(false);
      resetForm();
      loadData();
    } catch (error) {
      console.error("Error creating auction:", error);
      alert("Failed to create auction. Please try again.");
    }
  };

  const handleUpdateAuction = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedAuction) return;

    try {
      await vendorService.methods.updateAuction(selectedAuction.id, {
        end_time: auctionForm.end_time,
        reserve_price: auctionForm.reserve_price,
      });
      setShowEditModal(false);
      setSelectedAuction(null);
      resetForm();
      loadData();
    } catch (error) {
      console.error("Error updating auction:", error);
      alert("Failed to update auction. Please try again.");
    }
  };

  const handleEndAuction = async (auctionId: number) => {
    if (!confirm("Are you sure you want to end this auction?")) return;

    try {
      await vendorService.methods.endAuction(auctionId);
      loadData();
    } catch (error) {
      console.error("Error ending auction:", error);
      alert("Failed to end auction. Please try again.");
    }
  };

  const handleProcessRefunds = async (auctionId: number) => {
    if (!confirm("Process refunds for all losing bidders?")) return;

    try {
      await vendorService.methods.processRefunds(auctionId);
      alert("Refunds processed successfully!");
      loadData();
    } catch (error) {
      console.error("Error processing refunds:", error);
      alert("Failed to process refunds. Please try again.");
    }
  };

  const openEditModal = (auction: AuctionModel) => {
    setSelectedAuction(auction);
    setAuctionForm({
      product_id: auction.product.id,
      start_time: auction.start_time,
      end_time: auction.end_time,
      starting_price: parseFloat(auction.starting_price),
      reserve_price: parseFloat(auction.reserve_price),
    });
    setShowEditModal(true);
  };

  const resetForm = () => {
    setAuctionForm({
      product_id: 0,
      start_time: "",
      end_time: "",
      starting_price: 0,
      reserve_price: 0,
    });
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800",
      ended: "bg-gray-100 text-gray-800",
      pending: "bg-yellow-100 text-yellow-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const getTimeRemaining = (endTime: string) => {
    const end = new Date(endTime);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const columns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "product",
      header: "Product",
      render: (auction: AuctionModel) => (
        <div className="flex items-center">
          {auction.product.image && (
            <img
              src={auction.product.image}
              alt={auction.product.name}
              className="h-10 w-10 rounded-lg object-cover mr-3"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">
              {auction.product.name}
            </div>
            <div className="text-sm text-gray-500">
              {auction.product.category?.name}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "current_price",
      header: "Current Price",
      render: (auction: AuctionModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(auction.current_price).toFixed(2)}
        </span>
      ),
    },
    {
      key: "starting_price",
      header: "Starting Price",
      render: (auction: AuctionModel) =>
        `$${parseFloat(auction.starting_price).toFixed(2)}`,
    },
    {
      key: "reserve_price",
      header: "Reserve Price",
      render: (auction: AuctionModel) =>
        `$${parseFloat(auction.reserve_price).toFixed(2)}`,
    },
    {
      key: "bids_count",
      header: "Bids",
      render: (auction: AuctionModel) => auction.bids?.length || 0,
    },
    {
      key: "time_remaining",
      header: "Time Remaining",
      render: (auction: AuctionModel) => (
        <span
          className={`font-medium ${
            getTimeRemaining(auction.end_time) === "Ended"
              ? "text-red-600"
              : "text-blue-600"
          }`}
        >
          {getTimeRemaining(auction.end_time)}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (auction: AuctionModel) => getStatusBadge(auction.status),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("vendor")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="vendor">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Auctions</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your product auctions
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Create Auction
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    status: e.target.value || undefined,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="ended">Ended</option>
                <option value="pending">Pending</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Per Page
              </label>
              <select
                value={filters.per_page}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    per_page: parseInt(e.target.value),
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Auctions Table */}
        <DataTable
          data={auctions}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onPageChange={(page) => setFilters({ ...filters, page })}
          actions={(auction) => (
            <div className="flex space-x-2">
              <button
                onClick={() => router.push(`/auctions/${auction.id}`)}
                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                View
              </button>
              {auction.status === "active" && (
                <>
                  <button
                    onClick={() => openEditModal(auction)}
                    className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleEndAuction(auction.id)}
                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                  >
                    End
                  </button>
                </>
              )}
              {auction.status === "ended" &&
                auction.bids &&
                auction.bids.length > 0 && (
                  <button
                    onClick={() => handleProcessRefunds(auction.id)}
                    className="text-green-600 hover:text-green-900 text-sm font-medium"
                  >
                    Refunds
                  </button>
                )}
            </div>
          )}
          emptyMessage="No auctions found. Create your first auction to get started."
        />

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {
                  auctions.filter((auction) => auction.status === "active")
                    .length
                }
              </div>
              <div className="text-sm text-gray-500">Active Auctions</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {auctions.reduce(
                  (total, auction) => total + (auction.bids?.length || 0),
                  0
                )}
              </div>
              <div className="text-sm text-gray-500">Total Bids</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                $
                {auctions
                  .reduce(
                    (total, auction) =>
                      total + parseFloat(auction.current_price),
                    0
                  )
                  .toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Total Value</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {
                  auctions.filter((auction) => auction.status === "ended")
                    .length
                }
              </div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
          </div>
        </div>

        {/* Create Auction Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New Auction"
          size="lg"
        >
          <form onSubmit={handleCreateAuction} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Product
              </label>
              <select
                required
                value={auctionForm.product_id}
                onChange={(e) =>
                  setAuctionForm({
                    ...auctionForm,
                    product_id: parseInt(e.target.value),
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={0}>Select a product</option>
                {products.map((product) => (
                  <option key={product.id} value={product.id}>
                    {product.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Start Time
                </label>
                <input
                  type="datetime-local"
                  required
                  value={auctionForm.start_time}
                  onChange={(e) =>
                    setAuctionForm({
                      ...auctionForm,
                      start_time: e.target.value,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  End Time
                </label>
                <input
                  type="datetime-local"
                  required
                  value={auctionForm.end_time}
                  onChange={(e) =>
                    setAuctionForm({ ...auctionForm, end_time: e.target.value })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Starting Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  required
                  value={auctionForm.starting_price}
                  onChange={(e) =>
                    setAuctionForm({
                      ...auctionForm,
                      starting_price: parseFloat(e.target.value),
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Reserve Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  required
                  value={auctionForm.reserve_price}
                  onChange={(e) =>
                    setAuctionForm({
                      ...auctionForm,
                      reserve_price: parseFloat(e.target.value),
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Create Auction
              </button>
            </div>
          </form>
        </Modal>

        {/* Edit Auction Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title="Edit Auction"
        >
          {selectedAuction && (
            <form onSubmit={handleUpdateAuction} className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900">
                  {selectedAuction.product.name}
                </h4>
                <p className="text-sm text-gray-500">
                  Current Price: $
                  {parseFloat(selectedAuction.current_price).toFixed(2)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  End Time
                </label>
                <input
                  type="datetime-local"
                  required
                  value={auctionForm.end_time}
                  onChange={(e) =>
                    setAuctionForm({ ...auctionForm, end_time: e.target.value })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Reserve Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0.01"
                  required
                  value={auctionForm.reserve_price}
                  onChange={(e) =>
                    setAuctionForm({
                      ...auctionForm,
                      reserve_price: parseFloat(e.target.value),
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  Update Auction
                </button>
              </div>
            </form>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  );
};

export default VendorAuctionsPage;
