import Container from "@/modules/common/components/Container";
import Navbar from "@/modules/common/components/Navbar/Navbar";
import { LogoutButton } from "@/modules/dashboard/components/LogoutButton";
import { Metadata } from "next";
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "لوحة التحكم - متجر تجريبي",
  };
}
export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Container className="min-h-[92vh]">
      <Navbar />
      {/* <LogoutButton /> */}

      {children}
    </Container>
  );
}
