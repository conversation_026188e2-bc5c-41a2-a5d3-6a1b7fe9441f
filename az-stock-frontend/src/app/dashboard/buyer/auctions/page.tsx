"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import Modal from "@/modules/dashboard/components/shared/Modal";
import VerificationGuard from "@/modules/dashboard/components/shared/VerificationGuard";
import { buyerService } from "@/modules/dashboard/services/BuyerService";
import VendorService from "@/modules/dashboard/services/VendorService";
import useGlobalService from "@/core/hook/useGlobalService";
import {
  AuctionModel,
  CategoryModel,
  PaginationMeta,
} from "@/modules/dashboard/models/DashboardModels";

const BuyerAuctionsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const vendorService = useGlobalService(VendorService);
  const [auctions, setAuctions] = useState<AuctionModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationMeta | null>(null);
  const [filters, setFilters] = useState<{
    status?: "active" | "ended" | "upcoming";
    vendor_id?: number;
    category_id?: number;
    search?: string;
    min_price?: number;
    max_price?: number;
    ending_soon?: boolean;
    per_page?: number;
    page?: number;
  }>({
    status: "active",
    per_page: 15,
    page: 1,
  });
  const [showBidModal, setShowBidModal] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState<AuctionModel | null>(
    null
  );
  const [bidAmount, setBidAmount] = useState("");

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("buyer")) {
      router.push("/");
      return;
    }

    loadData();
  }, [session, status, router, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [auctionsResponse, categoriesResponse] = await Promise.all([
        buyerService.getAuctions(filters),
        vendorService.methods.getCategories(),
      ]);

      setAuctions(auctionsResponse.data);
      setPagination(auctionsResponse.meta || null);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error("Error loading auctions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlaceBid = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedAuction) return;

    try {
      await buyerService.placeBid(selectedAuction.id, parseFloat(bidAmount));
      setShowBidModal(false);
      setSelectedAuction(null);
      setBidAmount("");
      loadData(); // Reload to get updated auction data
    } catch (error) {
      console.error("Error placing bid:", error);
      alert("Failed to place bid. Please try again.");
    }
  };

  const handleAddToWatchlist = async (auctionId: number) => {
    try {
      await buyerService.addToWatchlist(auctionId);
      alert("Added to watchlist!");
    } catch (error) {
      console.error("Error adding to watchlist:", error);
      alert("Failed to add to watchlist.");
    }
  };

  const openBidModal = (auction: AuctionModel) => {
    setSelectedAuction(auction);
    setBidAmount((parseFloat(auction.current_price) + 1).toString());
    setShowBidModal(true);
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800",
      ended: "bg-gray-100 text-gray-800",
      pending: "bg-yellow-100 text-yellow-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const getTimeRemaining = (endTime: string) => {
    const end = new Date(endTime);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const columns = [
    {
      key: "product",
      header: "Product",
      render: (auction: AuctionModel) => (
        <div className="flex items-center">
          {auction.product?.image && (
            <img
              src={auction.product.image}
              alt={auction.product.name}
              className="h-10 w-10 rounded-lg object-cover mr-3"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">
              {auction.product?.name || auction.title || "Untitled Auction"}
            </div>
            <div className="text-sm text-gray-500">
              by {auction.vendor?.name || "Unknown Vendor"}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "current_price",
      header: "Current Price",
      render: (auction: AuctionModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(auction.current_price).toFixed(2)}
        </span>
      ),
    },
    {
      key: "starting_price",
      header: "Starting Price",
      render: (auction: AuctionModel) =>
        `$${parseFloat(auction.starting_price).toFixed(2)}`,
    },
    {
      key: "bids_count",
      header: "Bids",
      render: (auction: AuctionModel) => auction.bids?.length || 0,
    },
    {
      key: "time_remaining",
      header: "Time Remaining",
      render: (auction: AuctionModel) => (
        <span
          className={`font-medium ${
            getTimeRemaining(auction.end_time) === "Ended"
              ? "text-red-600"
              : "text-blue-600"
          }`}
        >
          {getTimeRemaining(auction.end_time)}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (auction: AuctionModel) => getStatusBadge(auction.status),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("buyer")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="buyer">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Browse Auctions</h1>
          <p className="mt-1 text-sm text-gray-500">
            Discover and bid on exciting auctions
          </p>
        </div>

        {/* Advanced Filters */}
        <VerificationGuard requiredRole="buyer">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Filter Auctions
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Search
                </label>
                <input
                  type="text"
                  value={filters.search || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      search: e.target.value,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Search auctions..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  value={filters.status || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      status: e.target.value as any,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="upcoming">Upcoming</option>
                  <option value="ended">Ended</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  value={filters.category_id || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      category_id: e.target.value
                        ? parseInt(e.target.value)
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Ending Soon
                </label>
                <select
                  value={filters.ending_soon?.toString() || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      ending_soon: e.target.value
                        ? e.target.value === "true"
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Auctions</option>
                  <option value="true">Ending Soon</option>
                  <option value="false">Not Ending Soon</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Min Price
                </label>
                <input
                  type="number"
                  value={filters.min_price || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      min_price: e.target.value
                        ? parseFloat(e.target.value)
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Max Price
                </label>
                <input
                  type="number"
                  value={filters.max_price || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      max_price: e.target.value
                        ? parseFloat(e.target.value)
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="1000.00"
                />
              </div>
            </div>
          </div>
        </VerificationGuard>

        {/* Auctions Table */}
        <VerificationGuard requiredRole="buyer">
          <DataTable
            data={auctions}
            columns={columns}
            loading={loading}
            pagination={pagination || undefined}
            onPageChange={(page) => setFilters({ ...filters, page })}
            actions={(auction) => (
              <div className="flex space-x-2">
                <button
                  onClick={() => router.push(`/auctions/${auction.id}`)}
                  className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                >
                  View
                </button>
                {auction.status === "active" && (
                  <VerificationGuard requiredRole="buyer" showMessage={false}>
                    <button
                      onClick={() => openBidModal(auction)}
                      className="text-green-600 hover:text-green-900 text-sm font-medium"
                    >
                      Bid
                    </button>
                  </VerificationGuard>
                )}
              </div>
            )}
            emptyMessage="No auctions found for the selected filters."
          />
        </VerificationGuard>

        {/* Place Bid Modal */}
        <Modal
          isOpen={showBidModal}
          onClose={() => setShowBidModal(false)}
          title="Place Bid"
        >
          {selectedAuction && (
            <form onSubmit={handlePlaceBid} className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900">
                  {selectedAuction.product?.name ||
                    selectedAuction.title ||
                    "Untitled Auction"}
                </h4>
                <p className="text-sm text-gray-500">
                  Current Price: $
                  {parseFloat(selectedAuction.current_price).toFixed(2)}
                </p>
                <p className="text-sm text-gray-500">
                  Time Remaining: {getTimeRemaining(selectedAuction.end_time)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Your Bid Amount
                </label>
                <input
                  type="number"
                  step="0.01"
                  min={parseFloat(selectedAuction.current_price) + 0.01}
                  required
                  value={bidAmount}
                  onChange={(e) => setBidAmount(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Minimum bid: $
                  {(parseFloat(selectedAuction.current_price) + 0.01).toFixed(
                    2
                  )}
                </p>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowBidModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Place Bid
                </button>
              </div>
            </form>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  );
};

export default BuyerAuctionsPage;
