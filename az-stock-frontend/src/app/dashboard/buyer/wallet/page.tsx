"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import Modal from "@/modules/dashboard/components/shared/Modal";
import StatsCard from "@/modules/dashboard/components/shared/StatsCard";
import { buyerService } from "@/modules/dashboard/services/BuyerService";
import {
  WalletModel,
  TransactionModel,
  WalletHoldModel,
  WalletDepositForm,
  WalletWithdrawForm,
  TransactionFilters,
} from "@/modules/dashboard/models/DashboardModels";

const BuyerWalletPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [wallet, setWallet] = useState<WalletModel | null>(null);
  const [transactions, setTransactions] = useState<TransactionModel[]>([]);
  const [holds, setHolds] = useState<WalletHoldModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState<TransactionFilters>({
    per_page: 15,
    page: 1,
  });
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [depositForm, setDepositForm] = useState<WalletDepositForm>({
    amount: 0,
    payment_method: "credit_card",
    payment_details: {
      card_number: "",
      expiry_month: 1,
      expiry_year: new Date().getFullYear(),
      cvc: "",
    },
  });
  const [withdrawForm, setWithdrawForm] = useState<WalletWithdrawForm>({
    amount: 0,
    withdrawal_method: "bank_transfer",
    withdrawal_details: {
      bank_name: "",
      account_number: "",
      routing_number: "",
    },
  });

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("buyer")) {
      router.push("/");
      return;
    }

    loadWalletData();
  }, [session, status, router, filters]);

  const loadWalletData = async () => {
    try {
      setLoading(true);
      const [walletResponse, transactionsResponse, holdsResponse] =
        await Promise.all([
          buyerService.getWallet(),
          buyerService.getWalletTransactions(filters),
          buyerService.getWalletHolds({ per_page: 10 }),
        ]);

      setWallet(walletResponse.data);
      setTransactions(transactionsResponse.data);
      setPagination(transactionsResponse.meta);
      setHolds(holdsResponse.data);
    } catch (error) {
      console.error("Error loading wallet data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeposit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await buyerService.depositFunds(depositForm);
      setShowDepositModal(false);
      setDepositForm({
        amount: 0,
        payment_method: "credit_card",
        payment_details: {
          card_number: "",
          expiry_month: 1,
          expiry_year: new Date().getFullYear(),
          cvc: "",
        },
      });
      loadWalletData();
    } catch (error) {
      console.error("Error depositing funds:", error);
      alert("Failed to deposit funds. Please try again.");
    }
  };

  const handleWithdraw = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await buyerService.withdrawFunds(withdrawForm);
      setShowWithdrawModal(false);
      setWithdrawForm({
        amount: 0,
        withdrawal_method: "bank_transfer",
        withdrawal_details: {
          bank_name: "",
          account_number: "",
          routing_number: "",
        },
      });
      loadWalletData();
    } catch (error) {
      console.error("Error withdrawing funds:", error);
      alert("Failed to withdraw funds. Please try again.");
    }
  };

  const transactionColumns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "type",
      header: "Type",
      render: (transaction: TransactionModel) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            transaction.type === "deposit"
              ? "bg-green-100 text-green-800"
              : transaction.type === "withdrawal"
              ? "bg-red-100 text-red-800"
              : "bg-blue-100 text-blue-800"
          }`}
        >
          {transaction.type}
        </span>
      ),
    },
    {
      key: "amount",
      header: "Amount",
      render: (transaction: TransactionModel) => (
        <span
          className={`font-medium ${
            transaction.type === "deposit" ? "text-green-600" : "text-red-600"
          }`}
        >
          {transaction.type === "deposit" ? "+" : "-"}$
          {parseFloat(transaction.amount).toFixed(2)}
        </span>
      ),
    },
    {
      key: "description",
      header: "Description",
    },
    {
      key: "status",
      header: "Status",
      render: (transaction: TransactionModel) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            transaction.status === "completed"
              ? "bg-green-100 text-green-800"
              : transaction.status === "pending"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {transaction.status}
        </span>
      ),
    },
    {
      key: "created_at",
      header: "Date",
      render: (transaction: TransactionModel) =>
        new Date(transaction.created_at).toLocaleDateString(),
    },
  ];

  const holdColumns = [
    {
      key: "amount",
      header: "Amount",
      render: (hold: WalletHoldModel) =>
        `$${parseFloat(hold.amount).toFixed(2)}`,
    },
    {
      key: "reason",
      header: "Reason",
    },
    {
      key: "status",
      header: "Status",
      render: (hold: WalletHoldModel) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            hold.status === "active"
              ? "bg-yellow-100 text-yellow-800"
              : hold.status === "released"
              ? "bg-green-100 text-green-800"
              : "bg-blue-100 text-blue-800"
          }`}
        >
          {hold.status}
        </span>
      ),
    },
    {
      key: "created_at",
      header: "Date",
      render: (hold: WalletHoldModel) =>
        new Date(hold.created_at).toLocaleDateString(),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("buyer")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="buyer">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Wallet</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your funds and view transaction history
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowDepositModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Deposit
            </button>
            <button
              onClick={() => setShowWithdrawModal(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 12H4"
                />
              </svg>
              Withdraw
            </button>
          </div>
        </div>

        {/* Wallet Stats */}
        {wallet && (
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatsCard
              title="Available Balance"
              value={`$${parseFloat(wallet.available_balance).toFixed(2)}`}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="Held Balance"
              value={`$${parseFloat(wallet.held_balance).toFixed(2)}`}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="Total Balance"
              value={`$${parseFloat(wallet.balance).toFixed(2)}`}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              }
            />
          </div>
        )}

        {/* Transaction Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Transaction Type
              </label>
              <select
                value={filters.type || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    type: e.target.value as any,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Types</option>
                <option value="deposit">Deposit</option>
                <option value="withdrawal">Withdrawal</option>
                <option value="hold">Hold</option>
                <option value="release">Release</option>
                <option value="payment">Payment</option>
              </select>
            </div>
          </div>
        </div>

        {/* Transactions Table */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Transaction History
            </h3>
            <DataTable
              data={transactions}
              columns={transactionColumns}
              loading={loading}
              pagination={pagination}
              onPageChange={(page) => setFilters({ ...filters, page })}
              emptyMessage="No transactions found."
            />
          </div>
        </div>

        {/* Held Funds */}
        {holds.length > 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Held Funds
              </h3>
              <DataTable
                data={holds}
                columns={holdColumns}
                loading={loading}
                emptyMessage="No held funds."
              />
            </div>
          </div>
        )}

        {/* Deposit Modal */}
        <Modal
          isOpen={showDepositModal}
          onClose={() => setShowDepositModal(false)}
          title="Deposit Funds"
          size="lg"
        >
          <form onSubmit={handleDeposit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Amount
              </label>
              <input
                type="number"
                step="0.01"
                min="1"
                required
                value={depositForm.amount}
                onChange={(e) =>
                  setDepositForm({
                    ...depositForm,
                    amount: parseFloat(e.target.value),
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Payment Method
              </label>
              <select
                value={depositForm.payment_method}
                onChange={(e) =>
                  setDepositForm({
                    ...depositForm,
                    payment_method: e.target.value,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="credit_card">Credit Card</option>
                <option value="debit_card">Debit Card</option>
                <option value="bank_transfer">Bank Transfer</option>
              </select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Card Number
                </label>
                <input
                  type="text"
                  required
                  value={depositForm.payment_details.card_number}
                  onChange={(e) =>
                    setDepositForm({
                      ...depositForm,
                      payment_details: {
                        ...depositForm.payment_details,
                        card_number: e.target.value,
                      },
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  CVC
                </label>
                <input
                  type="text"
                  required
                  value={depositForm.payment_details.cvc}
                  onChange={(e) =>
                    setDepositForm({
                      ...depositForm,
                      payment_details: {
                        ...depositForm.payment_details,
                        cvc: e.target.value,
                      },
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowDepositModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                Deposit Funds
              </button>
            </div>
          </form>
        </Modal>

        {/* Withdraw Modal */}
        <Modal
          isOpen={showWithdrawModal}
          onClose={() => setShowWithdrawModal(false)}
          title="Withdraw Funds"
          size="lg"
        >
          <form onSubmit={handleWithdraw} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Amount
              </label>
              <input
                type="number"
                step="0.01"
                min="1"
                max={wallet ? parseFloat(wallet.available_balance) : 0}
                required
                value={withdrawForm.amount}
                onChange={(e) =>
                  setWithdrawForm({
                    ...withdrawForm,
                    amount: parseFloat(e.target.value),
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              {wallet && (
                <p className="mt-1 text-sm text-gray-500">
                  Available: ${parseFloat(wallet.available_balance).toFixed(2)}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Bank Name
              </label>
              <input
                type="text"
                required
                value={withdrawForm.withdrawal_details.bank_name}
                onChange={(e) =>
                  setWithdrawForm({
                    ...withdrawForm,
                    withdrawal_details: {
                      ...withdrawForm.withdrawal_details,
                      bank_name: e.target.value,
                    },
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Account Number
              </label>
              <input
                type="text"
                required
                value={withdrawForm.withdrawal_details.account_number}
                onChange={(e) =>
                  setWithdrawForm({
                    ...withdrawForm,
                    withdrawal_details: {
                      ...withdrawForm.withdrawal_details,
                      account_number: e.target.value,
                    },
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowWithdrawModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
              >
                Withdraw Funds
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </DashboardLayout>
  );
};

export default BuyerWalletPage;
