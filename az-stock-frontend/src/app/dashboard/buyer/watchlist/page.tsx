"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import { buyerService } from "@/modules/dashboard/services/BuyerService";
import { AuctionModel } from "@/modules/dashboard/models/DashboardModels";

const BuyerWatchlistPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [watchlist, setWatchlist] = useState<AuctionModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("buyer")) {
      router.push("/");
      return;
    }

    loadWatchlist();
  }, [session, status, router]);

  const loadWatchlist = async () => {
    try {
      setLoading(true);
      const response = await buyerService.getWatchlist();
      setWatchlist(response.data);
      setPagination(response.meta);
    } catch (error) {
      console.error("Error loading watchlist:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromWatchlist = async (auctionId: number) => {
    if (!confirm("Remove this auction from your watchlist?")) return;

    try {
      await buyerService.removeFromWatchlist(auctionId);
      loadWatchlist();
    } catch (error) {
      console.error("Error removing from watchlist:", error);
      alert("Failed to remove from watchlist.");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800",
      ended: "bg-gray-100 text-gray-800",
      pending: "bg-yellow-100 text-yellow-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const getTimeRemaining = (endTime: string) => {
    const end = new Date(endTime);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const columns = [
    {
      key: "product",
      header: "Product",
      render: (auction: AuctionModel) => (
        <div className="flex items-center">
          {auction.featured_image && (
            <img
              src={auction.featured_image}
              alt={auction.title}
              className="h-10 w-10 rounded-lg object-cover mr-3"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">{auction.title}</div>
            <div className="text-sm text-gray-500">
              by {auction.vendor.name}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "current_price",
      header: "Current Price",
      render: (auction: AuctionModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(auction.current_price).toFixed(2)}
        </span>
      ),
    },
    {
      key: "starting_price",
      header: "Starting Price",
      render: (auction: AuctionModel) =>
        `$${parseFloat(auction.starting_price).toFixed(2)}`,
    },
    {
      key: "bids_count",
      header: "Bids",
      render: (auction: AuctionModel) => auction.bids?.length || 0,
    },
    {
      key: "time_remaining",
      header: "Time Remaining",
      render: (auction: AuctionModel) => (
        <span
          className={`font-medium ${
            getTimeRemaining(auction.end_time) === "Ended"
              ? "text-red-600"
              : "text-blue-600"
          }`}
        >
          {getTimeRemaining(auction.end_time)}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (auction: AuctionModel) => getStatusBadge(auction.status),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("buyer")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="buyer">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Watchlist</h1>
          <p className="mt-1 text-sm text-gray-500">
            Keep track of auctions you're interested in
          </p>
        </div>

        {/* Watchlist Table */}
        <DataTable
          data={watchlist}
          columns={columns}
          loading={loading}
          pagination={pagination}
          actions={(auction) => (
            <div className="flex space-x-2">
              <button
                onClick={() => router.push(`/auctions/${auction.id}`)}
                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                View
              </button>
              {auction.status === "active" && (
                <button
                  onClick={() => router.push(`/dashboard/buyer/auctions`)}
                  className="text-green-600 hover:text-green-900 text-sm font-medium"
                >
                  Bid
                </button>
              )}
              <button
                onClick={() => handleRemoveFromWatchlist(auction.id)}
                className="text-red-600 hover:text-red-900 text-sm font-medium"
              >
                Remove
              </button>
            </div>
          )}
          emptyMessage="Your watchlist is empty. Browse auctions and add items you're interested in."
        />

        {/* Quick Actions */}
        {watchlist.length === 0 && (
          <div className="text-center py-12">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No watched auctions
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by browsing auctions and adding items to your
              watchlist.
            </p>
            <div className="mt-6">
              <button
                onClick={() => router.push("/dashboard/buyer/auctions")}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <svg
                  className="-ml-1 mr-2 h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
                Browse Auctions
              </button>
            </div>
          </div>
        )}

        {/* Summary Stats */}
        {watchlist.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white shadow rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {
                    watchlist.filter((auction) => auction.status === "active")
                      .length
                  }
                </div>
                <div className="text-sm text-gray-500">Active Auctions</div>
              </div>
            </div>
            <div className="bg-white shadow rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {
                    watchlist.filter((auction) => {
                      const timeRemaining = getTimeRemaining(auction.end_time);
                      return (
                        timeRemaining !== "Ended" && auction.status === "active"
                      );
                    }).length
                  }
                </div>
                <div className="text-sm text-gray-500">Ending Soon</div>
              </div>
            </div>
            <div className="bg-white shadow rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">
                  {watchlist.length}
                </div>
                <div className="text-sm text-gray-500">Total Watched</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default BuyerWatchlistPage;
