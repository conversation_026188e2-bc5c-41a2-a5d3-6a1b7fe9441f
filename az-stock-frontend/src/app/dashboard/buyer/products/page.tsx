"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import VerificationGuard from "@/modules/dashboard/components/shared/VerificationGuard";
import { buyerService } from "@/modules/dashboard/services/BuyerService";
import { vendorService } from "@/modules/dashboard/services/VendorService";
import {
  ProductModel,
  CategoryModel,
  ProductFilters,
  PaginationMeta,
} from "@/modules/dashboard/models/DashboardModels";

const BuyerProductsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [products, setProducts] = useState<ProductModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationMeta | null>(null);
  const [filters, setFilters] = useState<ProductFilters>({
    per_page: 15,
    page: 1,
  });

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("buyer")) {
      router.push("/");
      return;
    }

    loadData();
  }, [session, status, router, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsResponse, categoriesResponse] = await Promise.all([
        buyerService.getProducts(filters),
        vendorService.methods.getCategories(),
      ]);
      setProducts(productsResponse.data);
      setPagination(productsResponse.meta || null);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error("Error loading products:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-100 text-green-800",
      inactive: "bg-red-100 text-red-800",
      draft: "bg-yellow-100 text-yellow-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const columns = [
    {
      key: "product",
      header: "Product",
      render: (product: ProductModel) => (
        <div className="flex items-center">
          {product.image_url && (
            <img
              src={product.image_url}
              alt={product.name}
              className="h-12 w-12 rounded-lg object-cover mr-3"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">{product.name}</div>
            <div className="text-sm text-gray-500">{product.category?.name}</div>
          </div>
        </div>
      ),
    },
    {
      key: "description",
      header: "Description",
      render: (product: ProductModel) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-900 truncate">
            {product.description || "No description"}
          </p>
        </div>
      ),
    },
    {
      key: "price",
      header: "Price",
      render: (product: ProductModel) => (
        <div className="font-medium text-gray-900">
          {formatCurrency(product.price)}
        </div>
      ),
    },
    {
      key: "vendor",
      header: "Vendor",
      render: (product: ProductModel) => (
        <div className="text-sm text-gray-900">
          {product.vendor?.name || "Unknown"}
        </div>
      ),
    },
    {
      key: "auction_status",
      header: "Auction",
      render: (product: ProductModel) => (
        <div>
          {product.auction ? (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Has Auction
            </span>
          ) : (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              No Auction
            </span>
          )}
        </div>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (product: ProductModel) => getStatusBadge(product.status || "active"),
    },
    {
      key: "actions",
      header: "Actions",
      render: (product: ProductModel) => (
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/products/${product.id}`)}
            className="text-blue-600 hover:text-blue-900 text-sm font-medium"
          >
            View
          </button>
          {product.auction && (
            <button
              onClick={() => router.push(`/auctions/${product.auction.id}`)}
              className="text-green-600 hover:text-green-900 text-sm font-medium"
            >
              View Auction
            </button>
          )}
        </div>
      ),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("buyer")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="buyer">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Browse Products</h1>
          <p className="mt-1 text-sm text-gray-500">
            Discover products and find items with active auctions
          </p>
        </div>

        {/* Advanced Filters */}
        <VerificationGuard requiredRole="buyer">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Filter Products</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Search
                </label>
                <input
                  type="text"
                  value={filters.search || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      search: e.target.value,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Search products..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  value={filters.category_id || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      category_id: e.target.value ? parseInt(e.target.value) : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Has Auction
                </label>
                <select
                  value={filters.has_auction?.toString() || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      has_auction: e.target.value ? e.target.value === "true" : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Products</option>
                  <option value="true">With Auction</option>
                  <option value="false">Without Auction</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  value={filters.status || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      status: e.target.value,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Min Price
                </label>
                <input
                  type="number"
                  value={filters.min_price || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      min_price: e.target.value ? parseFloat(e.target.value) : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Max Price
                </label>
                <input
                  type="number"
                  value={filters.max_price || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      max_price: e.target.value ? parseFloat(e.target.value) : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="1000.00"
                />
              </div>
            </div>
          </div>
        </VerificationGuard>

        {/* Products Table */}
        <VerificationGuard requiredRole="buyer">
          <DataTable
            data={products}
            columns={columns}
            loading={loading}
            pagination={pagination}
            onPageChange={(page) => setFilters({ ...filters, page })}
            emptyMessage="No products found for the selected filters."
          />
        </VerificationGuard>
      </div>
    </DashboardLayout>
  );
};

export default BuyerProductsPage;
