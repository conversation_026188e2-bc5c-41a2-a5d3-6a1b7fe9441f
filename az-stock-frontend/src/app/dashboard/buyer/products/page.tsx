"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import VerificationGuard from "@/modules/dashboard/components/shared/VerificationGuard";
import { buyerService } from "@/modules/dashboard/services/BuyerService";
import VendorService from "@/modules/dashboard/services/VendorService";
import useGlobalService from "@/core/hook/useGlobalService";
import {
  ProductModel,
  CategoryModel,
  PaginationMeta,
} from "@/modules/dashboard/models/DashboardModels";

// Since the system now uses auctions instead of standalone products,
// this page should redirect to auctions or show auction items
interface AuctionItemDisplay {
  id: number;
  name: string;
  description: string;
  image: string;
  category_id: number;
  vendor_id: number;
  created_at: string;
  updated_at: string;
  vendor: any;
  category: any;
  price: number;
  auction_id: number;
  auction_status: string;
}

const BuyerProductsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const vendorService = useGlobalService(VendorService);
  const [products, setProducts] = useState<AuctionItemDisplay[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationMeta | null>(null);
  const [filters, setFilters] = useState<{
    status?: "active" | "ended" | "upcoming";
    auction_type?: "sealed" | "online";
    vendor_id?: number;
    category_id?: number;
    search?: string;
    min_price?: number;
    max_price?: number;
    ending_soon?: boolean;
    per_page?: number;
    page?: number;
  }>({
    per_page: 15,
    page: 1,
  });

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("buyer")) {
      router.push("/");
      return;
    }

    loadData();
  }, [session, status, router, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      // Since buyers are interested in auctions, let's get auctions instead of products
      const [auctionsResponse, categoriesResponse] = await Promise.all([
        buyerService.getAuctions(filters),
        vendorService.methods.getCategories(),
      ]);
      // Convert auctions to auction items for display
      const itemsFromAuctions: AuctionItemDisplay[] = [];
      auctionsResponse.data.forEach((auction) => {
        auction.auction_items?.forEach((item) => {
          itemsFromAuctions.push({
            id: item.id,
            name: item.item_name,
            description: item.item_description || "",
            image: item.images?.[0]?.url || "",
            category_id: auction.category?.id || 0,
            vendor_id: auction.vendor?.id || 0,
            created_at: auction.created_at,
            updated_at: auction.updated_at,
            vendor: auction.vendor,
            category: auction.category,
            price: parseFloat(auction.starting_price),
            auction_id: auction.id,
            auction_status: auction.status,
          });
        });
      });
      setProducts(itemsFromAuctions);
      setPagination(auctionsResponse.meta || null);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error("Error loading products:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-100 text-green-800",
      inactive: "bg-red-100 text-red-800",
      draft: "bg-yellow-100 text-yellow-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status as keyof typeof statusColors] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const columns = [
    {
      key: "product",
      header: "Product",
      render: (product: AuctionItemDisplay) => (
        <div className="flex items-center">
          {product.image && (
            <img
              src={product.image}
              alt={product.name}
              className="h-12 w-12 rounded-lg object-cover mr-3"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">{product.name}</div>
            <div className="text-sm text-gray-500">
              {product.category?.name}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "description",
      header: "Description",
      render: (product: AuctionItemDisplay) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-900 truncate">
            {product.description || "No description"}
          </p>
        </div>
      ),
    },
    {
      key: "price",
      header: "Price",
      render: (product: AuctionItemDisplay) => (
        <div className="font-medium text-gray-900">
          {formatCurrency(product.price)}
        </div>
      ),
    },
    {
      key: "vendor",
      header: "Vendor",
      render: (product: AuctionItemDisplay) => (
        <div className="text-sm text-gray-900">
          {product.vendor?.name || "Unknown"}
        </div>
      ),
    },
    {
      key: "auction_status",
      header: "Auction",
      render: (product: AuctionItemDisplay) => (
        <div>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {product.auction_status}
          </span>
        </div>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (product: AuctionItemDisplay) =>
        getStatusBadge(product.auction_status || "active"),
    },
    {
      key: "actions",
      header: "Actions",
      render: (product: AuctionItemDisplay) => (
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/auctions/${product.auction_id}`)}
            className="text-blue-600 hover:text-blue-900 text-sm font-medium"
          >
            View Auction
          </button>
        </div>
      ),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("buyer")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="buyer">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Browse Products</h1>
          <p className="mt-1 text-sm text-gray-500">
            Discover products and find items with active auctions
          </p>
        </div>

        {/* Advanced Filters */}
        <VerificationGuard requiredRole="buyer">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Filter Products
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Search
                </label>
                <input
                  type="text"
                  value={filters.search || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      search: e.target.value,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Search products..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  value={filters.category_id || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      category_id: e.target.value
                        ? parseInt(e.target.value)
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Auction Type
                </label>
                <select
                  value={filters.auction_type || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      auction_type: e.target.value as
                        | "sealed"
                        | "online"
                        | undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Types</option>
                  <option value="online">Online Auction</option>
                  <option value="sealed">Sealed Auction</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  value={filters.status || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      status: e.target.value as
                        | "active"
                        | "ended"
                        | "upcoming"
                        | undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Min Price
                </label>
                <input
                  type="number"
                  value={filters.min_price || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      min_price: e.target.value
                        ? parseFloat(e.target.value)
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Max Price
                </label>
                <input
                  type="number"
                  value={filters.max_price || ""}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      max_price: e.target.value
                        ? parseFloat(e.target.value)
                        : undefined,
                      page: 1,
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="1000.00"
                />
              </div>
            </div>
          </div>
        </VerificationGuard>

        {/* Products Table */}
        <VerificationGuard requiredRole="buyer">
          <DataTable
            data={products}
            columns={columns}
            loading={loading}
            pagination={pagination || undefined}
            onPageChange={(page) => setFilters({ ...filters, page })}
            emptyMessage="No products found for the selected filters."
          />
        </VerificationGuard>
      </div>
    </DashboardLayout>
  );
};

export default BuyerProductsPage;
