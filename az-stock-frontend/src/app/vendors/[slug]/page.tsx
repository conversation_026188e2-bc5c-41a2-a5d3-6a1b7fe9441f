"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import VendorProfileService from "@/modules/dashboard/services/VendorProfileService";
import { VendorModel } from "@/modules/dashboard/models/DashboardModels";
import { VendorStatistics } from "@/modules/dashboard/services/VendorProfileService";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import Link from "next/link";

const vendorProfileService = new VendorProfileService();

interface VendorProfilePageProps {}

const VendorProfilePage: React.FC<VendorProfilePageProps> = () => {
  const params = useParams();
  const slug = params.slug as string;

  // Fetch vendor profile
  const {
    data: vendorResponse,
    isLoading: vendorLoading,
    error: vendorError,
  } = useQuery({
    queryKey: ["vendor", slug],
    queryFn: () => vendorProfileService.methods.getVendorBySlug(slug),
    enabled: !!slug,
  });

  // Fetch vendor statistics
  const { data: statistics, isLoading: statsLoading } = useQuery({
    queryKey: ["vendorStatistics", vendorResponse?.data?.id],
    queryFn: () =>
      vendorProfileService.methods.getVendorStatistics(vendorResponse!.data.id),
    enabled: !!vendorResponse?.data?.id,
  });

  const vendor = vendorResponse?.data;

  if (vendorLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (vendorError || !vendor) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Vendor Not Found
          </h1>
          <p className="text-gray-600 mb-8">
            The vendor you're looking for doesn't exist or has been deactivated.
          </p>
          <Link
            href="/auctions"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Browse Auctions
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-6">
            {/* Vendor Logo */}
            <div className="flex-shrink-0">
              {vendor.logo ? (
                <GlobalImage
                  src={vendor.logo}
                  alt={vendor.name}
                  width={80}
                  height={80}
                  className="h-20 w-20 rounded-full object-cover border-4 border-white shadow-lg"
                />
              ) : (
                <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center border-4 border-white shadow-lg">
                  <svg
                    className="h-10 w-10 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Vendor Info */}
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900">
                {vendor.name}
              </h1>
              <p className="text-lg text-gray-600 mt-1">
                Member since {vendor.member_since}
              </p>

              {/* Rating and Stats */}
              <div className="flex items-center space-x-6 mt-3">
                {vendor.average_rating && (
                  <div className="flex items-center">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`h-5 w-5 ${
                            i < Math.floor(vendor.average_rating!)
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-600">
                      {vendor.average_rating} ({vendor.total_reviews_count}{" "}
                      reviews)
                    </span>
                  </div>
                )}

                <div className="text-sm text-gray-600">
                  {vendor.total_auctions_count} auctions
                </div>

                <div className="text-sm text-gray-600">
                  {vendor.active_auctions_count} active
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="flex-shrink-0">
              <div className="space-y-2">
                {vendor.website && (
                  <a
                    href={vendor.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg
                      className="h-4 w-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                    Visit Website
                  </a>
                )}

                {vendor.email && (
                  <a
                    href={`mailto:${vendor.email}`}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <svg
                      className="h-4 w-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                    Contact
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* About */}
            {vendor.description && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  About
                </h2>
                <p className="text-gray-700 whitespace-pre-wrap">
                  {vendor.description}
                </p>
              </div>
            )}

            {/* Recent Auctions */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Recent Auctions
              </h2>
              {vendor.auctions && vendor.auctions.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {vendor.auctions.slice(0, 6).map((auction) => (
                    <Link
                      key={auction.id}
                      href={`/auctions/${auction.id}`}
                      className="block border rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center space-x-3">
                        {(auction.featured_image ||
                          auction.auction_items?.[0]?.images?.[0]) && (
                          <GlobalImage
                            src={
                              auction.featured_image ||
                              auction.auction_items?.[0]?.images?.[0]?.url ||
                              ""
                            }
                            alt={auction.title}
                            width={60}
                            height={60}
                            className="h-15 w-15 rounded-lg object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 line-clamp-2">
                            {auction.title}
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            Current: $
                            {parseFloat(auction.current_price).toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {auction.status} •{" "}
                            {auction.auction_items?.length || 0} items
                          </p>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No auctions available.</p>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Statistics */}
            {statistics && !statsLoading && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Statistics
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Success Rate</span>
                    <span className="font-medium">
                      {statistics.performance.success_rate}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Revenue</span>
                    <span className="font-medium">
                      ${statistics.revenue.total.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Repeat Bidders</span>
                    <span className="font-medium">
                      {statistics.performance.repeat_bidders}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg. Duration</span>
                    <span className="font-medium">
                      {statistics.performance.average_auction_duration} days
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Contact Information
              </h3>
              <div className="space-y-3">
                {vendor.address && (
                  <div className="flex items-start space-x-3">
                    <svg
                      className="h-5 w-5 text-gray-400 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <span className="text-gray-700">{vendor.address}</span>
                  </div>
                )}

                {vendor.phone && (
                  <div className="flex items-center space-x-3">
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                    <span className="text-gray-700">{vendor.phone}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Reviews */}
            {statistics?.recent_activity?.recent_reviews &&
              statistics.recent_activity.recent_reviews.length > 0 && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Recent Reviews
                  </h3>
                  <div className="space-y-4">
                    {statistics.recent_activity.recent_reviews
                      .slice(0, 3)
                      .map((review: any) => (
                        <div
                          key={review.id}
                          className="border-b border-gray-200 pb-4 last:border-b-0"
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <svg
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating
                                      ? "text-yellow-400"
                                      : "text-gray-300"
                                  }`}
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              ))}
                            </div>
                            <span className="text-sm text-gray-600">
                              {review.user?.name}
                            </span>
                          </div>
                          {review.comment && (
                            <p className="text-sm text-gray-700">
                              {review.comment}
                            </p>
                          )}
                        </div>
                      ))}
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VendorProfilePage;
